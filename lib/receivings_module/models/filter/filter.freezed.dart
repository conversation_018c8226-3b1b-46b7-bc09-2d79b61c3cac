// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FilterModel {
  String get id => throw _privateConstructorUsedError;
  String get label => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) =>
      throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FilterModelCopyWith<FilterModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterModelCopyWith<$Res> {
  factory $FilterModelCopyWith(
          FilterModel value, $Res Function(FilterModel) then) =
      _$FilterModelCopyWithImpl<$Res, FilterModel>;
  @useResult
  $Res call({String id, String label});
}

/// @nodoc
class _$FilterModelCopyWithImpl<$Res, $Val extends FilterModel>
    implements $FilterModelCopyWith<$Res> {
  _$FilterModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QueryFilterModelImplCopyWith<$Res>
    implements $FilterModelCopyWith<$Res> {
  factory _$$QueryFilterModelImplCopyWith(_$QueryFilterModelImpl value,
          $Res Function(_$QueryFilterModelImpl) then) =
      __$$QueryFilterModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String label, bool eanScannerEnabled, bool qrScannerEnabled});
}

/// @nodoc
class __$$QueryFilterModelImplCopyWithImpl<$Res>
    extends _$FilterModelCopyWithImpl<$Res, _$QueryFilterModelImpl>
    implements _$$QueryFilterModelImplCopyWith<$Res> {
  __$$QueryFilterModelImplCopyWithImpl(_$QueryFilterModelImpl _value,
      $Res Function(_$QueryFilterModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? eanScannerEnabled = null,
    Object? qrScannerEnabled = null,
  }) {
    return _then(_$QueryFilterModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      eanScannerEnabled: null == eanScannerEnabled
          ? _value.eanScannerEnabled
          : eanScannerEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      qrScannerEnabled: null == qrScannerEnabled
          ? _value.qrScannerEnabled
          : qrScannerEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$QueryFilterModelImpl implements QueryFilterModel {
  const _$QueryFilterModelImpl(
      {required this.id,
      required this.label,
      this.eanScannerEnabled = false,
      this.qrScannerEnabled = false});

  @override
  final String id;
  @override
  final String label;
  @override
  @JsonKey()
  final bool eanScannerEnabled;
  @override
  @JsonKey()
  final bool qrScannerEnabled;

  @override
  String toString() {
    return 'FilterModel.query(id: $id, label: $label, eanScannerEnabled: $eanScannerEnabled, qrScannerEnabled: $qrScannerEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QueryFilterModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.eanScannerEnabled, eanScannerEnabled) ||
                other.eanScannerEnabled == eanScannerEnabled) &&
            (identical(other.qrScannerEnabled, qrScannerEnabled) ||
                other.qrScannerEnabled == qrScannerEnabled));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, label, eanScannerEnabled, qrScannerEnabled);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QueryFilterModelImplCopyWith<_$QueryFilterModelImpl> get copyWith =>
      __$$QueryFilterModelImplCopyWithImpl<_$QueryFilterModelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) {
    return query(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) {
    if (query != null) {
      return query(this);
    }
    return orElse();
  }
}

abstract class QueryFilterModel implements FilterModel {
  const factory QueryFilterModel(
      {required final String id,
      required final String label,
      final bool eanScannerEnabled,
      final bool qrScannerEnabled}) = _$QueryFilterModelImpl;

  @override
  String get id;
  @override
  String get label;
  bool get eanScannerEnabled;
  bool get qrScannerEnabled;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QueryFilterModelImplCopyWith<_$QueryFilterModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListFilterModelImplCopyWith<$Res>
    implements $FilterModelCopyWith<$Res> {
  factory _$$ListFilterModelImplCopyWith(_$ListFilterModelImpl value,
          $Res Function(_$ListFilterModelImpl) then) =
      __$$ListFilterModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String label, List<FilterValueModel<String>> values});
}

/// @nodoc
class __$$ListFilterModelImplCopyWithImpl<$Res>
    extends _$FilterModelCopyWithImpl<$Res, _$ListFilterModelImpl>
    implements _$$ListFilterModelImplCopyWith<$Res> {
  __$$ListFilterModelImplCopyWithImpl(
      _$ListFilterModelImpl _value, $Res Function(_$ListFilterModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? values = null,
  }) {
    return _then(_$ListFilterModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      values: null == values
          ? _value.values
          : values // ignore: cast_nullable_to_non_nullable
              as List<FilterValueModel<String>>,
    ));
  }
}

/// @nodoc

class _$ListFilterModelImpl implements ListFilterModel {
  const _$ListFilterModelImpl(
      {required this.id, required this.label, required this.values});

  @override
  final String id;
  @override
  final String label;
  @override
  final List<FilterValueModel<String>> values;

  @override
  String toString() {
    return 'FilterModel.list(id: $id, label: $label, values: $values)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListFilterModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            const DeepCollectionEquality().equals(other.values, values));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, label, const DeepCollectionEquality().hash(values));

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListFilterModelImplCopyWith<_$ListFilterModelImpl> get copyWith =>
      __$$ListFilterModelImplCopyWithImpl<_$ListFilterModelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) {
    return list(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) {
    if (list != null) {
      return list(this);
    }
    return orElse();
  }
}

abstract class ListFilterModel implements FilterModel {
  const factory ListFilterModel(
          {required final String id,
          required final String label,
          required final List<FilterValueModel<String>> values}) =
      _$ListFilterModelImpl;

  @override
  String get id;
  @override
  String get label;
  List<FilterValueModel<String>> get values;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListFilterModelImplCopyWith<_$ListFilterModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DateRangeFilterModelImplCopyWith<$Res>
    implements $FilterModelCopyWith<$Res> {
  factory _$$DateRangeFilterModelImplCopyWith(_$DateRangeFilterModelImpl value,
          $Res Function(_$DateRangeFilterModelImpl) then) =
      __$$DateRangeFilterModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String label});
}

/// @nodoc
class __$$DateRangeFilterModelImplCopyWithImpl<$Res>
    extends _$FilterModelCopyWithImpl<$Res, _$DateRangeFilterModelImpl>
    implements _$$DateRangeFilterModelImplCopyWith<$Res> {
  __$$DateRangeFilterModelImplCopyWithImpl(_$DateRangeFilterModelImpl _value,
      $Res Function(_$DateRangeFilterModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
  }) {
    return _then(_$DateRangeFilterModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DateRangeFilterModelImpl implements DateRangeFilterModel {
  const _$DateRangeFilterModelImpl({required this.id, required this.label});

  @override
  final String id;
  @override
  final String label;

  @override
  String toString() {
    return 'FilterModel.dateRange(id: $id, label: $label)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateRangeFilterModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, label);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateRangeFilterModelImplCopyWith<_$DateRangeFilterModelImpl>
      get copyWith =>
          __$$DateRangeFilterModelImplCopyWithImpl<_$DateRangeFilterModelImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) {
    return dateRange(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) {
    if (dateRange != null) {
      return dateRange(this);
    }
    return orElse();
  }
}

abstract class DateRangeFilterModel implements FilterModel {
  const factory DateRangeFilterModel(
      {required final String id,
      required final String label}) = _$DateRangeFilterModelImpl;

  @override
  String get id;
  @override
  String get label;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateRangeFilterModelImplCopyWith<_$DateRangeFilterModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TextFilterModelImplCopyWith<$Res>
    implements $FilterModelCopyWith<$Res> {
  factory _$$TextFilterModelImplCopyWith(_$TextFilterModelImpl value,
          $Res Function(_$TextFilterModelImpl) then) =
      __$$TextFilterModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String label,
      String hintText,
      int maxLength,
      FilterValueInputDelegate? valueInputDelegate});
}

/// @nodoc
class __$$TextFilterModelImplCopyWithImpl<$Res>
    extends _$FilterModelCopyWithImpl<$Res, _$TextFilterModelImpl>
    implements _$$TextFilterModelImplCopyWith<$Res> {
  __$$TextFilterModelImplCopyWithImpl(
      _$TextFilterModelImpl _value, $Res Function(_$TextFilterModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? hintText = null,
    Object? maxLength = null,
    Object? valueInputDelegate = freezed,
  }) {
    return _then(_$TextFilterModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      hintText: null == hintText
          ? _value.hintText
          : hintText // ignore: cast_nullable_to_non_nullable
              as String,
      maxLength: null == maxLength
          ? _value.maxLength
          : maxLength // ignore: cast_nullable_to_non_nullable
              as int,
      valueInputDelegate: freezed == valueInputDelegate
          ? _value.valueInputDelegate
          : valueInputDelegate // ignore: cast_nullable_to_non_nullable
              as FilterValueInputDelegate?,
    ));
  }
}

/// @nodoc

class _$TextFilterModelImpl implements TextFilterModel {
  const _$TextFilterModelImpl(
      {required this.id,
      required this.label,
      required this.hintText,
      required this.maxLength,
      this.valueInputDelegate});

  @override
  final String id;
  @override
  final String label;
  @override
  final String hintText;
  @override
  final int maxLength;
  @override
  final FilterValueInputDelegate? valueInputDelegate;

  @override
  String toString() {
    return 'FilterModel.text(id: $id, label: $label, hintText: $hintText, maxLength: $maxLength, valueInputDelegate: $valueInputDelegate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TextFilterModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.hintText, hintText) ||
                other.hintText == hintText) &&
            (identical(other.maxLength, maxLength) ||
                other.maxLength == maxLength) &&
            (identical(other.valueInputDelegate, valueInputDelegate) ||
                other.valueInputDelegate == valueInputDelegate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, label, hintText, maxLength, valueInputDelegate);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TextFilterModelImplCopyWith<_$TextFilterModelImpl> get copyWith =>
      __$$TextFilterModelImplCopyWithImpl<_$TextFilterModelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) {
    return text(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) {
    if (text != null) {
      return text(this);
    }
    return orElse();
  }
}

abstract class TextFilterModel implements FilterModel {
  const factory TextFilterModel(
          {required final String id,
          required final String label,
          required final String hintText,
          required final int maxLength,
          final FilterValueInputDelegate? valueInputDelegate}) =
      _$TextFilterModelImpl;

  @override
  String get id;
  @override
  String get label;
  String get hintText;
  int get maxLength;
  FilterValueInputDelegate? get valueInputDelegate;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TextFilterModelImplCopyWith<_$TextFilterModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NumericFilterModelImplCopyWith<$Res>
    implements $FilterModelCopyWith<$Res> {
  factory _$$NumericFilterModelImplCopyWith(_$NumericFilterModelImpl value,
          $Res Function(_$NumericFilterModelImpl) then) =
      __$$NumericFilterModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String label, String hintText});
}

/// @nodoc
class __$$NumericFilterModelImplCopyWithImpl<$Res>
    extends _$FilterModelCopyWithImpl<$Res, _$NumericFilterModelImpl>
    implements _$$NumericFilterModelImplCopyWith<$Res> {
  __$$NumericFilterModelImplCopyWithImpl(_$NumericFilterModelImpl _value,
      $Res Function(_$NumericFilterModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? hintText = null,
  }) {
    return _then(_$NumericFilterModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      hintText: null == hintText
          ? _value.hintText
          : hintText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NumericFilterModelImpl implements NumericFilterModel {
  const _$NumericFilterModelImpl(
      {required this.id, required this.label, required this.hintText});

  @override
  final String id;
  @override
  final String label;
  @override
  final String hintText;

  @override
  String toString() {
    return 'FilterModel.numeric(id: $id, label: $label, hintText: $hintText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NumericFilterModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.hintText, hintText) ||
                other.hintText == hintText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, label, hintText);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NumericFilterModelImplCopyWith<_$NumericFilterModelImpl> get copyWith =>
      __$$NumericFilterModelImplCopyWithImpl<_$NumericFilterModelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) {
    return numeric(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) {
    if (numeric != null) {
      return numeric(this);
    }
    return orElse();
  }
}

abstract class NumericFilterModel implements FilterModel {
  const factory NumericFilterModel(
      {required final String id,
      required final String label,
      required final String hintText}) = _$NumericFilterModelImpl;

  @override
  String get id;
  @override
  String get label;
  String get hintText;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NumericFilterModelImplCopyWith<_$NumericFilterModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MultiSelectFilterModelImplCopyWith<$Res>
    implements $FilterModelCopyWith<$Res> {
  factory _$$MultiSelectFilterModelImplCopyWith(
          _$MultiSelectFilterModelImpl value,
          $Res Function(_$MultiSelectFilterModelImpl) then) =
      __$$MultiSelectFilterModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String label, List<FilterValueModel<NovaModel>> values});
}

/// @nodoc
class __$$MultiSelectFilterModelImplCopyWithImpl<$Res>
    extends _$FilterModelCopyWithImpl<$Res, _$MultiSelectFilterModelImpl>
    implements _$$MultiSelectFilterModelImplCopyWith<$Res> {
  __$$MultiSelectFilterModelImplCopyWithImpl(
      _$MultiSelectFilterModelImpl _value,
      $Res Function(_$MultiSelectFilterModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? values = null,
  }) {
    return _then(_$MultiSelectFilterModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      values: null == values
          ? _value.values
          : values // ignore: cast_nullable_to_non_nullable
              as List<FilterValueModel<NovaModel>>,
    ));
  }
}

/// @nodoc

class _$MultiSelectFilterModelImpl implements MultiSelectFilterModel {
  const _$MultiSelectFilterModelImpl(
      {required this.id, required this.label, required this.values});

  @override
  final String id;
  @override
  final String label;
  @override
  final List<FilterValueModel<NovaModel>> values;

  @override
  String toString() {
    return 'FilterModel.multiSelect(id: $id, label: $label, values: $values)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MultiSelectFilterModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            const DeepCollectionEquality().equals(other.values, values));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, label, const DeepCollectionEquality().hash(values));

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MultiSelectFilterModelImplCopyWith<_$MultiSelectFilterModelImpl>
      get copyWith => __$$MultiSelectFilterModelImplCopyWithImpl<
          _$MultiSelectFilterModelImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(QueryFilterModel value) query,
    required TResult Function(ListFilterModel value) list,
    required TResult Function(DateRangeFilterModel value) dateRange,
    required TResult Function(TextFilterModel value) text,
    required TResult Function(NumericFilterModel value) numeric,
    required TResult Function(MultiSelectFilterModel value) multiSelect,
  }) {
    return multiSelect(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(QueryFilterModel value)? query,
    TResult Function(ListFilterModel value)? list,
    TResult Function(DateRangeFilterModel value)? dateRange,
    TResult Function(TextFilterModel value)? text,
    TResult Function(NumericFilterModel value)? numeric,
    TResult Function(MultiSelectFilterModel value)? multiSelect,
    required TResult orElse(),
  }) {
    if (multiSelect != null) {
      return multiSelect(this);
    }
    return orElse();
  }
}

abstract class MultiSelectFilterModel implements FilterModel {
  const factory MultiSelectFilterModel(
          {required final String id,
          required final String label,
          required final List<FilterValueModel<NovaModel>> values}) =
      _$MultiSelectFilterModelImpl;

  @override
  String get id;
  @override
  String get label;
  List<FilterValueModel<NovaModel>> get values;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MultiSelectFilterModelImplCopyWith<_$MultiSelectFilterModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
