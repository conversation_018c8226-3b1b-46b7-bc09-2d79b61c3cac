import 'package:app/i18n/labels_config.dart';
import 'package:app/receivings_module/models/filter/config.dart';
import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:flutter/material.dart';

const RECEIVING_ORDERS_FREE_TEXT_FID = '1625751695';

const RECEIVING_ORDERS_SORTING_FID = '1625751696';
const RECEIVING_ORDERS_SORT_BY_DELIVERY_DATE_ASC_ID = '1625751722';
const RECEIVING_ORDERS_SORT_BY_DELIVERY_DATE_DESC_ID = '1625751723';
const RECEIVING_ORDERS_SORT_BY_ORDER_DATE_ASC_ID = '1625751724';
const RECEIVING_ORDERS_SORT_BY_ORDER_DATE_DESC_ID = '1625751725';

const RECEIVING_ORDERS_ORDER_TYPE_FID = '1752051079';
const RECEIVING_ORDERS_ORDER_TYPE_MATCH_ALL = '1752051080';
const RECEIVING_ORDERS_ORDER_TYPE_MATCH_EXACT = '1752051081';
const RECEIVING_ORDERS_ORDER_TYPE_MATCH_PARTIAL = '1752051082';
const RECEIVING_ORDERS_ORDER_TYPE_MATCH_RELATED = '1752051083';

const RECEIVING_ORDERS_STATUS_FID = '1625751697';
const RECEIVING_ORDERS_STATUS_ALL_ID = '1625751720';
const RECEIVING_ORDERS_STATUS_IN_PROGRESS_ID = '1625751721';
const RECEIVING_ORDERS_STATUS_PENDING_ID = '1752051084';

const RECEIVING_ORDERS_DATE_RANGE_FID = '1625751698';

const RECEIVING_ORDERS_SUPPLIER_NAME_FID = '1655886559';
const RECEIVING_ORDERS_PRODUCT_NAME_FID = '1655886560';
const RECEIVING_ORDERS_USER_NAME_FID = '1655886561';
const RECEIVING_ORDERS_COST_CENTER_NAME_FID = '1655886562';
const RECEIVING_ORDERS_REQUESTED_BY_COST_CENTER_NAME_FID = '1699534619';
const RECEIVING_ORDERS_ORDER_ID_FID = '1684745957';

const RECEIVING_ORDERS_DELIVERY_DATE_RANGE_FID = '1709558794';

class ProcessReceivingOrdersFilterConfig extends FilterConfig {
  late final List<FilterModel> _advancedFilterConfig;

  ProcessReceivingOrdersFilterConfig({
    required super.valueInputDelegates,
  }) : assert(
          valueInputDelegates
              .containsKey(RECEIVING_ORDERS_COST_CENTER_NAME_FID) &&
          valueInputDelegates
              .containsKey(RECEIVING_ORDERS_REQUESTED_BY_COST_CENTER_NAME_FID,),
        ) {
    _advancedFilterConfig = [
      // MOVED HERE FOR TEST
      FilterModel.text(
        id: RECEIVING_ORDERS_ORDER_ID_FID,
        label: Labels.receivings.orders.filter.order_id.label,
        hintText: Labels.receivings.orders.filter.order_id.hint_text,
        maxLength: 255,
      ),
      FilterModel.multiSelect(
        id: RECEIVING_ORDERS_ORDER_TYPE_FID,
        label: Labels.receivings.orders.filter.order_type.label,
        values: [
          FilterValueModel<NovaModel>(
            value: NovaModel(
              RECEIVING_ORDERS_ORDER_TYPE_MATCH_ALL,
              Labels.receivings.orders.filter.order_type.all,
            ),
            label: RECEIVING_ORDERS_ORDER_TYPE_MATCH_ALL,
          ),
          FilterValueModel<NovaModel>(
            value: NovaModel(
              RECEIVING_ORDERS_ORDER_TYPE_MATCH_EXACT,
              Labels.receivings.orders.filter.order_type.exact_match,
            ),
            label: RECEIVING_ORDERS_ORDER_TYPE_MATCH_EXACT,
          ),
          FilterValueModel<NovaModel>(
            value: NovaModel(
              RECEIVING_ORDERS_ORDER_TYPE_MATCH_PARTIAL,
              Labels.receivings.orders.filter.order_type.partial,
            ),
            label: RECEIVING_ORDERS_ORDER_TYPE_MATCH_PARTIAL,
          ),
          FilterValueModel<NovaModel>(
            value: NovaModel(
              RECEIVING_ORDERS_ORDER_TYPE_MATCH_RELATED,
              Labels.receivings.orders.filter.order_type.related,
            ),
            label: RECEIVING_ORDERS_ORDER_TYPE_MATCH_RELATED,
          ),
        ],
      ),

      FilterModel.list(
        id: RECEIVING_ORDERS_STATUS_FID,
        label: Labels.receivings.orders.filter.status_label,
        values: [
          FilterValueModel<String>(
            value: RECEIVING_ORDERS_STATUS_ALL_ID,
            label: Labels.receivings.orders.filter.status.all,
          ),
          FilterValueModel<String>(
            value: RECEIVING_ORDERS_STATUS_PENDING_ID,
            label: Labels.receivings.orders.filter.status.pending,
          ),
          FilterValueModel<String>(
            value: RECEIVING_ORDERS_STATUS_IN_PROGRESS_ID,
            label: Labels.receivings.orders.filter.status.in_progress,
          ),
        ],
      ),
      FilterModel.dateRange(
        id: RECEIVING_ORDERS_DATE_RANGE_FID,
        label: Labels.receivings.orders.filter.order_date,
      ),
      FilterModel.dateRange(
        id: RECEIVING_ORDERS_DELIVERY_DATE_RANGE_FID,
        label: Labels.receivings.orders.filter.delivery_date,
      ),
      // PLACE FOR ORDDER TYPE
      FilterModel.text(
        id: RECEIVING_ORDERS_SUPPLIER_NAME_FID,
        label: Labels.receivings.orders.filter.supplier_name.label,
        hintText: Labels.receivings.orders.filter.supplier_name.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: RECEIVING_ORDERS_PRODUCT_NAME_FID,
        label: Labels.receivings.orders.filter.product_name.label,
        hintText: Labels.receivings.orders.filter.product_name.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: RECEIVING_ORDERS_USER_NAME_FID,
        label: Labels.receivings.orders.filter.user_name.label,
        hintText: Labels.receivings.orders.filter.user_name.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: RECEIVING_ORDERS_COST_CENTER_NAME_FID,
        label: Labels.receivings.orders.filter.sent_cost_center_name.label,
        hintText:
            Labels.receivings.orders.filter.sent_cost_center_name.hint_text,
        maxLength: 255,
        valueInputDelegate:
            valueInputDelegates[RECEIVING_ORDERS_COST_CENTER_NAME_FID],
      ),
      FilterModel.text(
        id: RECEIVING_ORDERS_REQUESTED_BY_COST_CENTER_NAME_FID,
        label: Labels.receivings.orders.filter.requested_cost_center_name.label,
        hintText: Labels
            .receivings.orders.filter.requested_cost_center_name.hint_text,
        maxLength: 255,
        valueInputDelegate: valueInputDelegates[
            RECEIVING_ORDERS_REQUESTED_BY_COST_CENTER_NAME_FID],
      ),
    ];
  }

  static final _freeTextFilterConfig = QueryFilterModel(
    id: RECEIVING_ORDERS_FREE_TEXT_FID,
    label: Labels.filter.search,
    eanScannerEnabled: false,
  );

  static final _sortingFilterConfig = ListFilterModel(
    id: RECEIVING_ORDERS_SORTING_FID,
    label: Labels.filter.sorting,
    values: [
      FilterValueModel<String>(
        value: RECEIVING_ORDERS_SORT_BY_ORDER_DATE_ASC_ID,
        label: Labels.receivings.orders.filter.sorting.by_order_date_asc,
      ),
      FilterValueModel<String>(
        value: RECEIVING_ORDERS_SORT_BY_ORDER_DATE_DESC_ID,
        label: Labels.receivings.orders.filter.sorting.by_order_date_desc,
      ),
      FilterValueModel<String>(
        value: RECEIVING_ORDERS_SORT_BY_DELIVERY_DATE_ASC_ID,
        label: Labels.receivings.orders.filter.sorting.by_delivery_date_asc,
      ),
      FilterValueModel<String>(
        value: RECEIVING_ORDERS_SORT_BY_DELIVERY_DATE_DESC_ID,
        label: Labels.receivings.orders.filter.sorting.by_delivery_date_desc,
      ),
    ],
  );

  @override
  QueryFilterModel get freeTextFilterConfig => _freeTextFilterConfig;

  @override
  ListFilterModel get sortingFilterConfig => _sortingFilterConfig;

  @override
  List<FilterModel> get advancedFilterConfig => _advancedFilterConfig;

  @override
  Map<String, FilterValueModel> get defaultFilterValues => {
        RECEIVING_ORDERS_FREE_TEXT_FID:
            const FilterValueModel<String>(value: ''),
        RECEIVING_ORDERS_SORTING_FID: FilterValueModel<String>(
          value: RECEIVING_ORDERS_SORT_BY_DELIVERY_DATE_DESC_ID,
          label: Labels.receivings.orders.filter.sorting.by_delivery_date_desc,
        ),
        RECEIVING_ORDERS_STATUS_FID: FilterValueModel<String>(
          value: RECEIVING_ORDERS_STATUS_ALL_ID,
          label: Labels.receivings.orders.filter.status.all,
        ),
        RECEIVING_ORDERS_DATE_RANGE_FID: const FilterValueModel<DateTimeRange?>(
          value: null,
        ),
        RECEIVING_ORDERS_DELIVERY_DATE_RANGE_FID:
            const FilterValueModel<DateTimeRange?>(
          value: null,
        ),
        RECEIVING_ORDERS_ORDER_ID_FID: const FilterValueModel<String>(
          value: '',
        ),
        RECEIVING_ORDERS_ORDER_TYPE_FID: FilterValueModel<String>(
          value: RECEIVING_ORDERS_ORDER_TYPE_MATCH_ALL,
          label: Labels.receivings.orders.filter.order_type.all,
        ),
        RECEIVING_ORDERS_SUPPLIER_NAME_FID: const FilterValueModel<String>(
          value: '',
        ),
        RECEIVING_ORDERS_PRODUCT_NAME_FID: const FilterValueModel<String>(
          value: '',
        ),
        RECEIVING_ORDERS_USER_NAME_FID: const FilterValueModel<String>(
          value: '',
        ),
        RECEIVING_ORDERS_COST_CENTER_NAME_FID: const FilterValueModel<String>(
          value: '',
        ),
        RECEIVING_ORDERS_REQUESTED_BY_COST_CENTER_NAME_FID:
            const FilterValueModel<String>(
          value: '',
        ),
      };
}
