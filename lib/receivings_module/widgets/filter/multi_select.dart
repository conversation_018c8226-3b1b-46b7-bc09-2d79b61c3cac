import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/receivings_module/widgets/filter/heading.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/gap.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MultiSelectFilter<T extends FilterCubit> extends StatelessWidget {
  final MultiSelectFilterModel filter;

  const MultiSelectFilter({
    Key? key,
    required this.filter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return BlocBuilder<T, FilterState>(
      buildWhen: (previousState, currentState) {
        return currentState.values[filter.id] !=
            previousState.values[filter.id];
      },
      builder: (context, state) {
        // Get current selected values
        final currentValue = state.values[filter.id];
        final selectedValues = currentValue?.value;

        // Create display text for selected items
        String displayText;
        if (selectedValues.isEmpty) {
          displayText = tr(filter.label);
        } else {
          final selectedLabels = filter.values
              .where((item) => selectedValues.contains(item.value.id))
              .map((item) => tr(item.label))
              .toList();
          displayText = selectedLabels.join(', ');
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FilterHeading(
              label: tr(filter.label),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GestureDetector(
                onTap: () => _openMultiSelectModal(context, state),
                child: FLTextField(
                  enabled: false,
                  decoration: FLTextFieldDecoration(
                    hintText: displayText,
                  ),
                ),
              ),
            ),
            const Gap(4),
            const Divider(),
          ],
        );
      },
    );
  }

  Future<void> _openMultiSelectModal(BuildContext context, FilterState state) async {
    final tr = getTranslator(context);

    await FLModalBottomSheet.showSinglePage(
      context: context,
      pageBuilder: (context, controller) {
        return FLSliverModalBottomSheetPage(
          title: FLModalBottomSheetTitle.regular(
            title: tr(filter.label),
            showDivider: true,
          ),
          mainContentSlivers: [
            SliverPadding(
              padding: const EdgeInsets.only(
                top: FLSpacings.sm,
                bottom: FLSpacings.sm,
              ),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final child = filter.values[index];
                    final currentValue = state.values[filter.id];
                    final selectedValues = currentValue?.value as Set<String>? ?? <String>{};

                    return FLFilterListTile.multiSelect(
                      text: tr(child.label),
                      selected: selectedValues.contains(child.value.id),
                      onChanged: (selected) {
                        final cubit = context.read<T>();
                        final newSet = Set<String>.from(selectedValues);

                        if (selected) {
                          newSet.add(child.value.id);
                        } else {
                          newSet.remove(child.value.id);
                        }

                        cubit.add(
                          id: filter.id,
                          value: FilterValueModel<Set<String>>(
                            value: newSet,
                            label: tr(filter.label),
                          ),
                        );
                      },
                    );
                  },
                  childCount: filter.values.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
