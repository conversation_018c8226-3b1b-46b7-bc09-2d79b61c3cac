import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/receivings_module/widgets/filter/heading.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/gap.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MultiSelectFilter<T extends FilterCubit> extends StatelessWidget {
  final MultiSelectFilterModel filter;

  const MultiSelectFilter({
    Key? key,
    required this.filter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return FLModalBottomSheet.showMultiPage(
      context: context,
      pageBuilder: (context, controller) {
        return FLSliverModalBottomSheetPage(
          title: FLModalBottomSheetTitle.regular(
            title: tr(filter.label),
            showDivider: true,
          ),
          mainContentSlivers: [
            SliverPadding(
              padding: const EdgeInsets.only(
                top: FLSpacings.sm,
                bottom: FLSpacings.sm,
              ),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final child = filter.values[index];

                    return FLFilterListTile.multiSelect(
                      text: tr(child.label),
                      selected: state.values[filter.id]!.value.contains(child.value),
                      onChanged: (selected) {
                        final newSet = Set<String>.from(state.values[filter.id]!.value);
                        if (selected) {

    return BlocBuilder<T, FilterState>(
      buildWhen: (previousState, currentState) {
        return currentState.values[filter.id] !=
            previousState.values[filter.id];
      },
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          FilterHeading(
            label: tr(filter.label),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: FLTextField(
              decoration: FLTextFieldDecoration(
                hintText: tr(filter.label),
              ),
              
            ),
          ),
          const Gap(4),
          const Divider(),
        ],
        );
      },
    );
  }
}
