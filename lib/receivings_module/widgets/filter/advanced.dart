import 'package:app/i18n/labels_config.dart';
import 'package:app/receivings_module/widgets/filter/date_range.dart';
import 'package:app/receivings_module/widgets/filter/list.dart';
import 'package:app/receivings_module/widgets/filter/multi_select.dart';
import 'package:app/receivings_module/widgets/filter/numeric.dart';
import 'package:app/receivings_module/widgets/filter/text.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AdvancedFilter<T extends FilterCubit> extends StatelessWidget {
  const AdvancedFilter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tr = getTranslator(context);

    return Drawer(
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  bottom: 16,
                  left: 16,
                ),
                child: Text(
                  tr(Labels.filter.label),
                  style: theme.textTheme.titleLarge!.copyWith(
                    color: Colors.black,
                  ),
                ),
              ),
              const Divider(),
            ],
          ),
        ),
        body: SafeArea(
          top: false,
          left: false,
          bottom: true,
          right: true,
          child: Container(
            color: Colors.white,
            child: BlocBuilder<T, FilterState>(
              buildWhen: (previousState, currentState) => false,
              builder: (_, state) {
                return ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    ...state.config.advancedFilterConfig.map(
                      (f) {
                        return f.map<Widget>(
                          query: (_) => const SizedBox.shrink(),
                          list: (f) => ListFilter<T>(filter: f),
                          dateRange: (f) => DateRangeFilter<T>(filter: f),
                          text: (f) => TextFilter<T>(filter: f),
                          numeric: (f) => NumericFilter<T>(filter: f),
                          multiSelect: (f) => MultiSelectFilter<T>(filter: f),
                        );
                      },
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
