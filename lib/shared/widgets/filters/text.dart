import 'dart:async';

import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/mixins/after_init.dart';
import 'package:async/async.dart';
import 'package:equatable/equatable.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

enum _EventType {
  text,
  action,
}

class _Event extends Equatable {
  final _EventType type;

  final String value;

  const _Event({
    required this.type,
    required this.value,
  });

  @override
  List<Object> get props => [type, value];
}

class TextFilter<T extends FilterCubit> extends StatefulWidget {
  static const textEventDebounce = Duration(milliseconds: 800);

  final TextFilterModel filter;

  const TextFilter({
    Key? key,
    required this.filter,
  }) : super(key: key);

  @override
  State<TextFilter> createState() => _TextFilterState<T>();
}

class _TextFilterState<T extends FilterCubit> extends State<TextFilter>
    with AfterInitMixin {
  late final TextEditingController _controller;

  late final FocusNode _focusNode;

  late final PublishSubject<_Event> _streamController;

  late Stream<_Event> _eventStream;

  late final StreamSubscription<_Event> _subscription;

  String? _initialValue;

  @override
  void didInitState() {
    final cubit = context.read<T>();

    _streamController = PublishSubject<_Event>();

    List<Stream<_Event>> streams = StreamSplitter.splitFrom(
      _streamController.stream,
      2,
    );

    Stream<_Event> textEventStream =
        streams[0].distinct().debounceTime(TextFilter.textEventDebounce).where(
              (event) => event.type == _EventType.text,
            );

    Stream<_Event> actionEventStream = streams[1].distinct().where(
          (event) => event.type == _EventType.action,
        );

    _eventStream = StreamGroup.merge(
      [textEventStream, actionEventStream],
    ).asBroadcastStream();

    _subscription = _eventStream.distinct().listen(
      (event) {
        cubit.add(
          id: widget.filter.id,
          value: FilterValueModel<String?>(value: event.value),
        );
      },
    );

    _controller = TextEditingController();

    _initialValue = cubit.state.values[widget.filter.id]!.value;
    _controller.text = _initialValue ?? '';

    _focusNode = FocusNode();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _streamController.sink.add(
          _Event(
            type: _EventType.text,
            value: _controller.text,
          ),
        );

        return;
      }

      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    _streamController.close();
    _controller.dispose();
    _focusNode.dispose();

    super.dispose();
  }

  Future<void> _getValueFromDelegate(BuildContext context) async {
    final navigator = Navigator.of(context);

    _focusNode.unfocus();

    final value = await widget.filter.valueInputDelegate?.call(
      context.read<T>().state.values,
    );

    if (value != null) {
      _controller.text = value;

      _streamController.add(
        _Event(
          type: _EventType.action,
          value: value,
        ),
      );

      navigator.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FLFilterHeading.regular(
          text: tr(widget.filter.label),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: FLTextField(
            controller: _controller,
            focusNode: _focusNode,
            enableSuggestions: false,
            autocorrect: false,
            maxLength: widget.filter.maxLength,
            decoration: FLTextFieldDecoration(
              hintText: tr(widget.filter.hintText),
              suffixIcon: StreamBuilder<String>(
                initialData: _initialValue,
                stream: _streamController.stream.map((event) => event.value),
                builder: (context, snapshot) {
                  if (snapshot.data == null || snapshot.data == '') {
                    if (widget.filter.valueInputDelegate == null) {
                      return const SizedBox.shrink();
                    }

                    return FLTextFieldIconButton(
                      symbol: const FLSymbol(Symbols.search),
                      onPressed: () => _getValueFromDelegate(context),
                    );
                  }

                  return FLTextFieldIconButton(
                    symbol: const FLSymbol(Symbols.close),
                    onPressed: () {
                      _controller.clear();

                      _initialValue = '';

                      _streamController.add(
                        const _Event(
                          type: _EventType.action,
                          value: '',
                        ),
                      );

                      if (widget.filter.valueInputDelegate != null) {
                        Navigator.of(context).pop();
                      }
                    },
                  );
                },
              ),
            ),
            onTap: widget.filter.valueInputDelegate != null
                ? () => _getValueFromDelegate(context)
                : null,
            onChanged: widget.filter.valueInputDelegate == null
                ? (value) => _streamController.add(
                      _Event(
                        type: _EventType.text,
                        value: value,
                      ),
                    )
                : null,
          ),
        ),
        const FLGap(4),
        const FLDivider(),
      ],
    );
  }
}
