import 'dart:io';

import 'package:app/fuzzy_search/indexer_services.dart';
import 'package:app/i18n/localization_storage.dart';
import 'package:app/shared/config/common.dart';
import 'package:app/shared/config/routes.dart';
import 'package:app/shared/helpers/permission_checker.dart';
import 'package:app/shared/repositories/advanced_search.dart';
import 'package:app/shared/repositories/announcements.dart';
import 'package:app/shared/repositories/app_theme.dart';
import 'package:app/shared/repositories/app_version.dart';
import 'package:app/shared/repositories/approvals.dart';
import 'package:app/shared/repositories/auth.dart';
import 'package:app/shared/repositories/bookings.dart';
import 'package:app/shared/repositories/cache.dart';
import 'package:app/shared/repositories/capex.dart';
import 'package:app/shared/repositories/cart.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/repositories/cost_center.dart';
import 'package:app/shared/repositories/dashboard.dart';
import 'package:app/shared/repositories/delivery_notes.dart';
import 'package:app/shared/repositories/division.dart';
import 'package:app/shared/repositories/file.dart';
import 'package:app/shared/repositories/file_attachments.dart';
import 'package:app/shared/repositories/i18n.dart';
import 'package:app/shared/repositories/ims.dart';
import 'package:app/shared/repositories/inhouse_lists.dart';
import 'package:app/shared/repositories/inventory_control.dart';
import 'package:app/shared/repositories/invoices.dart';
import 'package:app/shared/repositories/notifications.dart';
import 'package:app/shared/repositories/order_lists.dart';
import 'package:app/shared/repositories/orders.dart';
import 'package:app/shared/repositories/platform_file.dart';
import 'package:app/shared/repositories/purchase_requests.dart';
import 'package:app/shared/repositories/receivings.dart';
import 'package:app/shared/repositories/recipes.dart';
import 'package:app/shared/repositories/reports.dart';
import 'package:app/shared/repositories/saved_devices.dart';
import 'package:app/shared/repositories/scan_to_order.dart';
import 'package:app/shared/repositories/scanned_pages.dart';
import 'package:app/shared/repositories/sessions.dart';
import 'package:app/shared/repositories/supplier.dart';
import 'package:app/shared/repositories/test.dart';
import 'package:app/shared/repositories/transfer_lists.dart';
import 'package:app/shared/repositories/user.dart';
import 'package:app/shared/repositories/warehouse.dart';
import 'package:app/shared/services/advanced_search/search.dart';
import 'package:app/shared/services/api/api.dart';
import 'package:app/shared/services/api/auth/access_forbidden.dart';
import 'package:app/shared/services/api/client/api_transport.dart';
import 'package:app/shared/services/api/client/api_transport_factory.dart';
import 'package:app/shared/services/api/web.dart';
import 'package:app/shared/services/deep_links.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/common_preferences.dart';
import 'package:app/shared/services/preferences/storage.dart';
import 'package:app/shared/services/preferences/user_preferences.dart';
import 'package:app/shared/services/push_notifications.dart';
import 'package:app/shared/services/storage/sqlite.dart';
import 'package:app/shared/services/storage/storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:http/http.dart';
import 'package:http/io_client.dart';
import 'package:platform_info_repository/platform_info_repository.dart';

class AppRepositoriesProvider extends StatelessWidget {
  const AppRepositoriesProvider({
    super.key,
    required this.child,
    required PreferencesCacheService preferencesCacheService,
    required PreferencesStorage preferencesStorage,
    required UserPreferencesService userPreferencesService,
    required CommonPreferencesService commonPreferencesService,
    required LocalizationStorage localizationStorage,
    this.askNotificationsPermissions = true,
  })  : _preferencesCacheService = preferencesCacheService,
        _preferencesStorage = preferencesStorage,
        _userPreferencesService = userPreferencesService,
        _commonPreferencesService = commonPreferencesService,
        _localizationStorage = localizationStorage;

  final Widget child;

  final PreferencesCacheService _preferencesCacheService;

  final PreferencesStorage _preferencesStorage;

  final UserPreferencesService _userPreferencesService;

  final CommonPreferencesService _commonPreferencesService;

  final LocalizationStorage _localizationStorage;

  final bool askNotificationsPermissions;

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<BaseClient>(
          create: (_) => IOClient(),
          dispose: (client) => client.close(),
        ),
        // Register configuration objects
        RepositoryProvider<CommonConfiguration>(
          create: (_) => CommonConfiguration(),
        ),
        RepositoryProvider<RoutesConfiguration>(
          create: (_) => RoutesConfiguration(),
        ),

        RepositoryProvider<PreferencesCacheService>.value(
          value: _preferencesCacheService,
        ),
        RepositoryProvider<PreferencesStorage>.value(
          value: _preferencesStorage,
        ),
        RepositoryProvider<UserPreferencesService>.value(
          value: _userPreferencesService,
        ),
        RepositoryProvider<CommonPreferencesService>.value(
          value: _commonPreferencesService,
        ),
        RepositoryProvider<PermissionChecker>(
          create: (context) => PermissionChecker(
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<AdvancedSearchService>(
          create: (context) => AdvancedSearchService(
            authenticator: AccessForbidden(),
            client: context.read<BaseClient>(),
            endpoint: CommonConfiguration.advancedSearchUrl,
          ),
        ),
        RepositoryProvider<PlatformInfoRepository>(
          create: (_) => PlatformInfoRepository(),
        ),
        RepositoryProvider<AppVersionRepository>(
          create: (context) => AppVersionRepository(
            platformInfoRepository: context.read<PlatformInfoRepository>(),
            preferencesCacheService: _preferencesCacheService,
            commonPreferencesService: context.read<CommonPreferencesService>(),
          ),
        ),
        RepositoryProvider<PlatformFileRepository>(
          create: (context) => PlatformFileRepository(
            platformInfoRepository: context.read<PlatformInfoRepository>(),
          ),
        ),
        RepositoryProvider<IndexerServices>(
          create: (_) => IndexerServices(),
        ),
        RepositoryProvider<StorageService>(
          create: (context) => SqliteStorageService(
            preferencesCacheService: _preferencesCacheService,
            indexerServices: context.read<IndexerServices>(),
          ),
          lazy: false,
        ),
        // NOTE: This repository must be registered before ApiService!
        RepositoryProvider<CacheRepository>(
          create: (context) => CacheRepository(
            context.read<StorageService>(),
          ),
          lazy: false,
        ),
        // Register api service
        RepositoryProvider<ApiService>(
          create: (context) {
            ApiTransport apiTransport = ApiTransportFactory().initHttpTransport(
              unsecureEndpoint: CommonConfiguration.apiUrl,
              secureEndpoint: CommonConfiguration.apiSecureUrl,
              uploadEndpoint: CommonConfiguration.apiUploadUrl,
              universalFilesApiUrl: CommonConfiguration.universalFilesApiUrl,
              cacheRepository: context.read<CacheRepository>(),
              client: context.read<BaseClient>(),
            );

            return WebApiService(apiTransport);
          },
          lazy: false,
        ),
        RepositoryProvider<NotificationsRepository>(
          create: (context) => NotificationsRepository(
            context.read<StorageService>(),
          ),
        ),
        //App repositories
        RepositoryProvider<AppThemeRepository>(
          create: (context) => AppThemeRepository(
            preferencesCacheService: _preferencesCacheService,
            commonPreferencesService: context.read<CommonPreferencesService>(),
          ),
        ),
        RepositoryProvider<TestRepository>(
          create: (context) => TestRepository(
            context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<UserRepository>(
          create: (context) => UserRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<CartRepository>(
          create: (context) => CartRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<SupplierRepository>(
          create: (context) => SupplierRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<AuthRepository>(
          create: (context) => AuthRepository(
            context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<CatalogRepository>(
          create: (context) => CatalogRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<AdvancedSearchRepository>(
          create: (context) => AdvancedSearchRepository(
            searchService: context.read<AdvancedSearchService>(),
            preferencesCacheService: _preferencesCacheService,
            userRepository: context.read<UserRepository>(),
          ),
        ),
        RepositoryProvider<TransferListsRepository>(
          create: (context) => TransferListsRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<InHouseListsRepository>(
          create: (context) => InHouseListsRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<BookingsRepository>(
          create: (context) => BookingsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<InventoryControlRepository>(
          create: (context) => InventoryControlRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<OrdersRepository>(
          create: (context) => OrdersRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<AnnouncementsRepository>(
          create: (context) => AnnouncementsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<WarehouseRepository>(
          create: (context) => WarehouseRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<SessionsRepository>(
          create: (context) => SessionsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<ScannedPagesRepository>(
          create: (context) => ScannedPagesRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<FileAttachmentsRepository>(
          create: (context) => FileAttachmentsRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<I18nRepository>(
          create: (context) => I18nRepository(
            context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<DivisionRepository>(
          create: (context) => DivisionRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<CostCenterRepository>(
          create: (context) => CostCenterRepository(
            context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<PurchaseRequestsRepository>(
          create: (context) => PurchaseRequestsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<CapexRepository>(
          create: (context) => CapexRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<InvoicesRepository>(
          create: (context) => InvoicesRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<ApprovalsRepository>(
          create: (context) => ApprovalsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<ReceivingsRepository>(
          create: (context) => ReceivingsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<OrderListsRepository>(
          create: (context) => OrderListsRepository(
            apiService: context.read<ApiService>(),
            storageService: context.read<StorageService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<ReportsRepository>(
          create: (context) => ReportsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<DashboardRepository>(
          create: (context) => DashboardRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
            routesConfiguration: context.read<RoutesConfiguration>(),
          ),
        ),
        RepositoryProvider<DeliveryNotesRepository>(
          create: (context) => DeliveryNotesRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<SavedDevicesRepository>(
          create: (context) => SavedDevicesRepository(
            context.read<StorageService>(),
          ),
        ),
        RepositoryProvider<ImsRepository>(
          create: (context) => ImsRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<RecipesRepository>(
          create: (context) => RecipesRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<FileRepository>(
          create: (context) => FileRepository(
            context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<ScanToOrderRepository>(
          create: (context) => ScanToOrderRepository(
            apiService: context.read<ApiService>(),
            preferencesCacheService: _preferencesCacheService,
          ),
        ),
        RepositoryProvider<LocalizationStorage>(
          create: (context) => _localizationStorage,
        ),
        RepositoryProvider<PushNotificationsService>(
          create: (context) {
            if (Platform.isWindows) {
              return DisabledPushNotificationService();
            } else {
              return FCMPushNotificationsService(
                notificationsRepository:
                    context.read<NotificationsRepository>(),
                costCenterRepository: context.read<CostCenterRepository>(),
                preferencesCacheService: _preferencesCacheService,
                userPreferencesService: _userPreferencesService,
                askNotificationsPermissions: askNotificationsPermissions,
              )..init();
            }
          },
        ),
        RepositoryProvider<DeepLinksService>(
          create: (context) => Platform.isWindows
              ? DisabledDeppLinkServices()
              : UniLinkDeepLinksService(
                  routesConfig: context.read<RoutesConfiguration>(),
                  apiService: context.read<ApiService>(),
                  userRepository: context.read<UserRepository>(),
                  preferencesCacheService: _preferencesCacheService,
                  userPreferencesService:
                      context.read<UserPreferencesService>(),
                  commonPreferencesService:
                      context.read<CommonPreferencesService>(),
                  sessionsRepository: context.read<SessionsRepository>(),
                  costCenterRepository: context.read<CostCenterRepository>(),
                )
            ..init(),
          dispose: (s) => s.dispose(),
          lazy: false,
        ),
        RepositoryProvider<FlutterReactiveBle>(
          create: (_) => FlutterReactiveBle(),
        ),
        RepositoryProvider<FlutterBluetoothSerial>(
          create: (_) => FlutterBluetoothSerial.instance,
        ),
      ],
      child: child,
    );
  }
}
