import 'package:app/approvals/models/approval_requests_counts.dart';
import 'package:app/approvals/models/approver.dart';
import 'package:app/approvals/models/filter/purchase_requests_config.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/config/config.dart';
import 'package:app/shared/models/filter/entity_lookup_config.dart';
import 'package:app/shared/models/lookup_value.dart';
import 'package:app/shared/repositories/repositories.dart';
import 'package:app/shared/services/api/api.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/types/types.dart';

class ApprovalsRepository extends ApiRepository {
  ApprovalsRepository({
    required this.preferencesCacheService,
    required ApiService apiService,
  }) : super(apiService);

  final PreferencesCacheService preferencesCacheService;

  Future<Result<ApprovalRequestsCountsModel, AppError>> getCountOfNotApproved({
    bool includePurchaseRequests = false,
    bool includeInvoices = false,
    bool includeBookingApprovalRequests = false,
    bool includeReceivingApprovalRequests = false,
    bool includeCapexApprovalRequests = false,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getCountOfNotApprovedApprovals(
        orgUnitKey: divisionId,
        costCenterKey: costCenterId,
        includePurchaseRequests: includePurchaseRequests,
        includeInvoices: includeInvoices,
        includeBookingApprovalRequests: includeBookingApprovalRequests,
        includeReceivingApprovalRequests: includeReceivingApprovalRequests,
        includeCapexApprovalRequests: includeCapexApprovalRequests,
      );

      return result.match(
        ok: (response) => Ok(
          ApprovalRequestsCountsModel.fromJson(response.toMap()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ApproverModel>, AppError>> approversLookup({
    required String id,
    required String type,
    required String? overwriteDivisionId,
    required String? overwriteCostCenterId,
    int level = 0,
  }) async {
    try {
      final divisionId = overwriteDivisionId ??
          preferencesCacheService.getField(PreferencesField.division).id;
      final String costCenterId = overwriteCostCenterId ??
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.approversLookup(
        divisionId: divisionId,
        costCenterId: costCenterId,
        id: id,
        type: type,
        level: level,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ApproverModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<LookupValueModel>, AppError>> approvalsRequestorLookup({
    required String purchaseRequestStatus,
    required int page,
    required int pageSize,
    required Map<String, FilterValueModel> filterValues,
  }) async {
    late final String status;

    switch (purchaseRequestStatus) {
      case PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME:
        status = 'ASSIGNED_TO_ME';
        break;

      case PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_MY_GROUP:
        status = 'ASSIGNED_TO_MY_GROUP';
        break;

      case PURCHASE_REQUESTS_FILTER_STATUS_OPEN:
        status = 'OPEN';
        break;

      case PURCHASE_REQUESTS_FILTER_STATUS_APPROVED:
        status = 'APPROVED';
        break;

      case PURCHASE_REQUESTS_FILTER_STATUS_CLOSED:
        status = 'CLOSED';
        break;

      case PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_APPROVED:
        status = 'CLOSED_APPROVED';
        break;

      case PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_DECLINED:
        status = 'CLOSED_DECLINED';
        break;

      default:
        assert(false);
        break;
    }

    final query = filterValues[ENTITY_LOOKUP_FREE_TEXT_FID]?.value ?? '';

    final divisionId =
        preferencesCacheService.getField(PreferencesField.division).id;

    try {
      final result = await api.approvalsRequestorLookup(
        divisionId: divisionId,
        query: query,
        page: page,
        pageSize: pageSize,
        purchaseRequestStatus: status,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(LookupValueModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }
}
