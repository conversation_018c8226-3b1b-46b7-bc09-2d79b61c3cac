import 'package:app/approvals/models/activity_log_record.dart';
import 'package:app/approvals/models/approval_request.dart';
import 'package:app/approvals/models/approval_request_document.dart';
import 'package:app/approvals/models/approval_trail_item.dart';
import 'package:app/approvals/models/chat_message.dart';
import 'package:app/approvals/models/editable_purchase_request_product_offer.dart';
import 'package:app/approvals/models/filter/purchase_request_product_supplier_lookup_config.dart';
import 'package:app/approvals/models/filter/purchase_requests_config.dart';
import 'package:app/approvals/models/log_record.dart';
import 'package:app/approvals/models/pr_product_transfer_status.dart';
import 'package:app/approvals/models/purchase_request_product.dart';
import 'package:app/approvals/models/purchase_request_product_offer.dart';
import 'package:app/approvals/models/purchase_request_product_offer_replacement.dart';
import 'package:app/approvals/models/purchase_request_product_supplier_lookup_value.dart';
import 'package:app/budget_module/models/budget_forecast.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/config/config.dart';
import 'package:app/shared/models/external_comment.dart';
import 'package:app/shared/models/filter/entity_lookup_config.dart';
import 'package:app/shared/models/lookup_value.dart';
import 'package:app/shared/repositories/repositories.dart';
import 'package:app/shared/services/api/api.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/types/types.dart';
import 'package:intl/intl.dart';

class PurchaseRequestsRepository extends ApiRepository {
  PurchaseRequestsRepository({
    required this.preferencesCacheService,
    required ApiService apiService,
  }) : super(
          apiService,
        );

  final PreferencesCacheService preferencesCacheService;

  Future<Result<List<PurchaseRequestModel>, AppError>> search({
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
    required String language,
    bool includeFilesCount = false,
  }) async {
    try {
      late final String status;
      late final String orderByField;
      late final String orderType;

      switch (filterValues[PURCHASE_REQUESTS_FILTER_STATUS_FID]!.value) {
        case PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME:
          status = 'ASSIGNED_TO_ME';
          break;

        case PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_MY_GROUP:
          status = 'ASSIGNED_TO_MY_GROUP';
          break;

        case PURCHASE_REQUESTS_FILTER_STATUS_OPEN:
          status = 'OPEN';
          break;

        case PURCHASE_REQUESTS_FILTER_STATUS_APPROVED:
          status = 'APPROVED';
          break;

        case PURCHASE_REQUESTS_FILTER_STATUS_CLOSED:
          status = 'CLOSED';
          break;

        case PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_APPROVED:
          status = 'CLOSED_APPROVED';
          break;

        case PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_DECLINED:
          status = 'CLOSED_DECLINED';
          break;

        default:
          assert(false);
          break;
      }

      switch (filterValues[PURCHASE_REQUESTS_SORTING_FID]!.value) {
        case PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_ASCENDING:
          orderByField = 'added_to_cart_at';
          orderType = 'ASC';
          break;
        case PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_DESCENDING:
          orderByField = 'added_to_cart_at';
          orderType = 'DESC';
          break;

        case PURCHASE_REQUESTS_SORT_BY_REQUESTED_AT_ASCENDING:
          orderByField = 'requested_at';
          orderType = 'ASC';
          break;
        case PURCHASE_REQUESTS_SORT_BY_REQUESTED_AT_DESCENDING:
          orderByField = 'requested_at';
          orderType = 'DESC';
          break;

        default:
          assert(false);
          break;
      }

      final String costCenterName =
          filterValues[PURCHASE_REQUESTS_FILTER_COST_CENTER_FID]!.value;
      final String category =
          filterValues[PURCHASE_REQUESTS_FILTER_CATEGORY_FID]!.value;
      final String supplier =
          filterValues[PURCHASE_REQUESTS_FILTER_SUPPLIER_FID]!.value;
      final String description =
          filterValues[PURCHASE_REQUESTS_FILTER_DESCRIPTION_FID]!.value;

      final range = filterValues[PURCHASE_REQUESTS_DATE_RANGE_FID]!.value;
      final df = DateFormat('dd.MM.yyyy');

      final fromDate = range != null ? df.format(range.start) : null;
      final toDate = range != null ? df.format(range.end) : null;

      final String query = filterValues[PURCHASE_REQUESTS_FREE_TEXT_FID]!.value;

      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final requesterName =
          filterValues[PURCHASE_REQUESTS_FILTER_REQUESTOR_FID]!.value;

      final result = await api.searchPurchaseRequests(
        divisionId: divisionId,
        constCenterId: costCenterId,
        query: query,
        costCenterName: costCenterName,
        category: category,
        supplier: supplier,
        requesterName: requesterName,
        itemDescription: description,
        status: status,
        fromDate: fromDate,
        toDate: toDate,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
        includeFilesCount: includeFilesCount,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<PurchaseRequestModel, AppError>> getById({
    required String divisionId,
    required String costCenterId,
    required String purchaseRequestId,
    required String language,
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
    bool includeFiles = false,
  }) async {
    try {
      final result = await api.getOnePurchaseRequestById(
        divisionId: divisionId,
        constCenterId: costCenterId,
        purchaseRequestId: purchaseRequestId,
        language: language,
        includeProducts: includeProducts,
        includeLogRecords: includeLogRecords,
        includeMessages: includeMessages,
        includeFiles: includeFiles,
      );

      return result.match(
        ok: (response) => Ok(
          PurchaseRequestModel.fromJson(response.toMap()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<LogRecordModel>, AppError>> getLogRecords({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    try {
      final result = await api.getPurchaseRequestLogRecords(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(LogRecordModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ApprovalRequestDocumentModel>, AppError>> getDocuments({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    try {
      final result = await api.getPurchaseRequestDocuments(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ApprovalRequestDocumentModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ApprovalTrailItemModel>, AppError>> getApprovalTrail({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    try {
      final result = await api.getPurchaseRequestApprovalTrail(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ApprovalTrailItemModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> deleteDocument({
    required String divisionId,
    required String purchaseRequestId,
    required String documentId,
  }) async {
    try {
      final result = await api.deletePurchaseRequestDocument(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        documentId: documentId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ChatMessageModel>, AppError>> pullMessages({
    required String divisionId,
    required String purchaseRequestId,
    required int size,
    required String order,
    int? pullFromMessageId,
  }) async {
    try {
      final result = await api.pullPurchaseRequestMessages(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        pullFromMessageId: pullFromMessageId,
        size: size,
        order: order,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ChatMessageModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> sendMessage({
    required String divisionId,
    required String purchaseRequestId,
    required String message,
  }) async {
    try {
      final result = await api.sendPurchaseRequestMessage(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        message: message,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> reset({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    try {
      final result = await api.resetPurchaseRequest(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> delete({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    try {
      final result = await api.deletePurchaseRequest(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> moveToCart({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    try {
      final result = await api.movePurchaseRequestToCart(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> update({
    required String divisionId,
    required String purchaseRequestId,
    required bool isApproved,
    String? comment,
    String? nextApproverId,
  }) async {
    try {
      final result = await api.updatePurchaseRequest(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        isApproved: isApproved,
        comment: comment,
        nextApproverId: nextApproverId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> requestApproval({
    required String divisionId,
    required String purchaseRequestId,
    required String type,
    required String approverId,
  }) async {
    try {
      final result = await api.requestPurchaseRequestApproval(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        type: type,
        approverId: approverId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<PurchaseRequestProductTransferStatusModel>, AppError>>
      sendOrders({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
  }) async {
    try {
      final result = await api.sendPurchaseRequestOrders(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        language: language,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestProductTransferStatusModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  // -- Products --

  Future<Result<List<PurchaseRequestProductModel>, AppError>> searchProducts({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required int page,
    required int pageSize,
    required bool includeFilesCount,
    required bool includeFiles,
  }) async {
    try {
      final result = await api.searchPurchaseRequestProducts(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        language: language,
        query: '',
        page: page,
        pageSize: pageSize,
        includeFiles: includeFiles,
        includeFilesCount: includeFilesCount,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestProductModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<PurchaseRequestProductModel, AppError>> getProductById({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required int id,
    required bool includeFiles,
    required bool includeFilesCount,
  }) async {
    try {
      final result = await api.getOnePurchaseRequestProductById(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        language: language,
        productId: id,
        includeFiles: includeFiles,
        includeFilesCount: includeFilesCount,
      );

      return result.match(
        ok: (response) => Ok(
          PurchaseRequestProductModel.fromJson(response.toMap()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateProduct({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required bool isConfirmed,
  }) async {
    try {
      final result = await api.updatePurchaseRequestProduct(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        productId: productId,
        isConfirmed: isConfirmed,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateProductQty({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required double orderUnitQty,
  }) async {
    try {
      final result = await api.updatePurchaseRequestProductQty(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        productId: productId,
        orderUnitQty: orderUnitQty,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<PurchaseRequestProductOfferModel>, AppError>>
      getProductOffers({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
  }) async {
    try {
      final result = await api.getPurchaseRequestProductOffers(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        language: language,
        metaId: metaId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestProductOfferModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> changeProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
    required int replacementOfferPositionId,
    required String replacementOfferType,
    required String originalType,
    required int originalPositionId,
  }) async {
    try {
      final result = await api.changePurchaseRequestProductOffer(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        language: language,
        metaId: metaId,
        replacementOfferPositionId: replacementOfferPositionId,
        replacementOfferType: replacementOfferType,
        originalType: originalType,
        originalPositionId: originalPositionId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ActivityLogRecordModel>, AppError>> searchProductLog({
    required int productId,
    required String divisionId,
    required String purchaseRequestId,
    required int page,
    required int pageSize,
  }) async {
    try {
      final result = await api.searchPurchaseRequestProductLog(
        divisionId: divisionId,
        page: page,
        pageSize: pageSize,
        purchaseRequestId: purchaseRequestId,
        id: productId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ActivityLogRecordModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<EditablePurchaseRequestProductOfferModel, AppError>>
      getProductOfferById({
    required int id,
    required String divisionId,
    required String purchaseRequestId,
    required String language,
  }) async {
    try {
      final result = await api.getPurchaseRequestProductOfferById(
        id: id,
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        language: language,
      );

      return result.match(
        ok: (response) => Ok(
          EditablePurchaseRequestProductOfferModel.fromJson(
            response.getData(),
          ),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required Map<String, dynamic> fields,
  }) async {
    try {
      final result = await api.updatePurchaseRequestProductOffer(
        id: id,
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        fields: fields,
      );

      return result.match(
        ok: (response) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> addProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String supplierId,
    required String supplierProductId,
    required String orderUnit,
    required String contentUnit,
    required double contentUnitsPerOrderUnit,
    required double inventoryUnitsPerOrderUnit,
    required double orderUnitPrice,
    String? ean,
    int? taxId,
  }) async {
    try {
      final result = await api.addPurchaseRequestProductOffer(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        id: id,
        supplierId: supplierId,
        supplierProductId: supplierProductId,
        orderUnit: orderUnit,
        contentUnit: contentUnit,
        contentUnitsPerOrderUnit: contentUnitsPerOrderUnit,
        inventoryUnitsPerOrderUnit: inventoryUnitsPerOrderUnit,
        orderUnitPrice: orderUnitPrice,
        ean: ean,
        taxId: taxId,
      );

      return result.match(
        ok: (response) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> replaceProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int replacementId,
  }) async {
    try {
      final result = await api.replacePurchaseRequestProductOffer(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        id: id,
        replacementId: replacementId,
      );

      return result.match(
        ok: (response) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<PurchaseRequestProductOfferReplacementModel>, AppError>>
      productOfferReplacementLookup({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    try {
      final result = await api.purchaseRequestProductOfferReplacementLookup(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        id: id,
        query: '',
        page: page,
        pageSize: pageSize,
        language: language,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestProductOfferReplacementModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<PurchaseRequestProductSupplierLookupValueModel>, AppError>>
      productSupplierLookup({
    required String divisionId,
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    try {
      final String query =
          filterValues[PURCHASE_REQUEST_PRODUCT_SUPPLIER_LOOKUP_FREE_TEXT_FID]!
              .value;

      final result = await api.purchaseRequestProductSupplierLookup(
        divisionId: divisionId,
        query: query,
        page: page,
        pageSize: pageSize,
        language: language,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestProductSupplierLookupValueModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<LookupValueModel>, AppError>> costTypeLookup({
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
    required String? lookupDivisionId,
    required String? lookupCostCenterId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final String query = filterValues[ENTITY_LOOKUP_FREE_TEXT_FID]!.value;

      final result = await api.purchaseRequestCostTypeLookup(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
        lookupDivisionId: lookupDivisionId ?? divisionId,
        lookupCostCenterId: lookupCostCenterId ?? costCenterId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(LookupValueModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateProductCostType({
    required String? targetDivisionId,
    required String? productCostCenterId,
    required int itemPositionId,
    required String itemType,
    required String costTypeId,
    required String purchaseRequestId,
    required int productId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.updatePurchaseRequestProductCostType(
        divisionId: targetDivisionId ?? divisionId,
        costCenterId: costCenterId,
        productCostCenterId: productCostCenterId ?? costCenterId,
        itemPositionId: itemPositionId,
        itemType: itemType,
        costTypeId: costTypeId,
        purchaseRequestId: purchaseRequestId,
        productId: productId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<PurchaseRequestBudgetForecastModel>, AppError>>
      calculateBudget({
    required String? targetDivisionId,
    required String? targetCostCenterId,
    required String purchaseRequestId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.calculatePurchaseRequestBudget(
        divisionId: targetDivisionId ?? divisionId,
        costCenterId: costCenterId,
        targetCostCenterId: targetCostCenterId ?? costCenterId,
        purchaseRequestId: purchaseRequestId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(PurchaseRequestBudgetForecastModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ExternalCommentModel>, AppError>>
      getExternalCommentHistory({
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required String purchaseRequestId,
    required int leadTime,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getPurchaseRequestExternalCommentHistory(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderDivisionId: orderDivisionId,
        orderCostCenterId: orderCostCenterId,
        supplierId: supplierId,
        purchaseRequestId: purchaseRequestId,
        leadTime: leadTime,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ExternalCommentModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }
}
