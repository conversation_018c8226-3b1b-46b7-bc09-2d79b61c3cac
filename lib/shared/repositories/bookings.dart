import 'package:app/approvals/models/approval_request.dart';
import 'package:app/approvals/models/approval_request_document.dart';
import 'package:app/approvals/models/approval_trail_item.dart';
import 'package:app/approvals/models/booking/booking_approval_request_product.dart';
import 'package:app/approvals/models/chat_message.dart';
import 'package:app/approvals/models/filter/booking_approval_requests_config.dart';
import 'package:app/approvals/models/log_record.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/config/config.dart';
import 'package:app/shared/models/lookup_value.dart';
import 'package:app/shared/repositories/repositories.dart';
import 'package:app/shared/services/api/api.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/types/types.dart';
import 'package:app/transfers_module/models/booking.dart';
import 'package:app/transfers_module/models/booking_item.dart';
import 'package:app/transfers_module/models/booking_status.dart';
import 'package:app/transfers_module/models/filter/booking_items_config.dart';
import 'package:app/transfers_module/models/filter/booking_manage_config.dart';
import 'package:app/transfers_module/models/filter/booking_reason_lookup_config.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BookingsRepository extends ApiRepository {
  BookingsRepository({
    required this.preferencesCacheService,
    required ApiService apiService,
  }) : super(apiService);

  final PreferencesCacheService preferencesCacheService;

  Future<Result<List<BookingModel>, AppError>> search({
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
  }) async {
    try {
      late final String orderByField;
      late final String orderType;

      switch (filterValues[BOOKINGS_MANAGE_SORTING_FID]!.value) {
        case BOOKINGS_MANAGE_SORT_BY_NAME_ASC_ID:
          orderByField = 'list_name';
          orderType = 'ASC';
          break;

        case BOOKINGS_MANAGE_SORT_BY_NAME_DESC_ID:
          orderByField = 'list_name';
          orderType = 'DESC';
          break;

        case BOOKINGS_MANAGE_SORT_BY_TRANSFER_DATE_ASC_ID:
          orderByField = 'transfer_date';
          orderType = 'ASC';
          break;

        case BOOKINGS_MANAGE_SORT_BY_TRANSFER_DATE_DESC_ID:
          orderByField = 'transfer_date';
          orderType = 'DESC';
          break;

        case BOOKINGS_MANAGE_SORT_BY_ORDER_DATE_ASC_ID:
          orderByField = 'order_date';
          orderType = 'ASC';
          break;

        case BOOKINGS_MANAGE_SORT_BY_ORDER_DATE_DESC_ID:
          orderByField = 'order_date';
          orderType = 'DESC';
          break;

        default:
          assert(false);
          break;
      }

      late final String status;

      switch (filterValues[BOOKINGS_MANAGE_FILTER_STATUS_FID]!.value) {
        case BOOKINGS_MANAGE_FILTER_STATUS_ANY:
          status = 'ANY';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_CONFIRMED_AND_UNCONFIRMED:
          status = 'CONFIRMED_AND_UNCONFIRMED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_CONFIRMED:
          status = 'CONFIRMED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_UNCONFIRMED:
          status = 'UNCONFIRMED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_NOT_APPROVED:
          status = 'NOT_APPROVED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_PENDING:
          status = 'PENDING';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_EMPTY:
          status = 'EMPTY';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_DELIVERED:
          status = 'DELIVERED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_BOOKED:
          status = 'BOOKED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_ARCHIVED:
          status = 'ARCHIVED';
          break;

        case BOOKINGS_MANAGE_FILTER_STATUS_DECLINED:
          status = 'DECLINED';
          break;

        default:
          assert(false);
          break;
      }

      late final List<String> transferType;

      switch (filterValues[BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_FID]!.value) {
        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_ANY:
          transferType = [];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_STKOUT_RECIPE:
          transferType = ['STKOUT_RECIPE'];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_STK_TRANSFER:
          transferType = ['STK_TRANSFER'];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_STK_CORRECTION:
          transferType = ['STK_CORRECTION'];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_OUT_ORDER_IH:
          transferType = ['OUT_ORDER_IH'];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_OUT_ORDER_IP:
          transferType = ['OUT_ORDER_IP'];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_OUT_NOT_SYSTEM:
          transferType = ['OUT_NOT_SYSTEM'];
          break;

        case BOOKINGS_MANAGE_FILTER_TRANSFER_TYPE_STK_IP_TRANSFER:
          transferType = ['STK_IP_TRANSFER'];
          break;

        default:
          transferType = [];
          break;
      }

      late final bool onlyThisCostCenter;

      switch (filterValues[BOOKINGS_MANAGE_FILTER_COST_CENTER_FID]!.value) {
        case BOOKINGS_MANAGE_FILTER_COST_CENTER_ANY:
          onlyThisCostCenter = false;
          break;

        case BOOKINGS_MANAGE_FILTER_COST_CENTER_ONLY_THIS:
          onlyThisCostCenter = true;
          break;

        default:
          assert(false);
          break;
      }

      final range = filterValues[BOOKINGS_MANAGE_FILTER_DATE_RANGE_FID]?.value
          as DateTimeRange?;
      final df = DateFormat('dd.MM.yyyy');

      final fromDate = range != null ? df.format(range.start) : null;
      final toDate = range != null ? df.format(range.end) : null;

      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.searchBookings(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: filterValues[BOOKINGS_MANAGE_FREE_TEXT_FID]!.value,
        status: status,
        fromDate: fromDate,
        toDate: toDate,
        orderByField: orderByField,
        orderType: orderType,
        transferType: transferType,
        onlyThisCostCenter: onlyThisCostCenter,
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(BookingModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<BookingModel, AppError>> getOneById({
    required String bookingId,
    bool includeItems = false,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getOneBookingById(
        bookingId: bookingId,
        divisionId: divisionId,
        includeProducts: includeItems,
      );

      return result.match(
        ok: (response) {
          final record = Map<String, dynamic>.from(response.toMap());

          if (record['products'] != null) {
            final String bookingId = record['id'];
            final List products = record['products'];

            for (Map<String, dynamic> p in products) {
              p['booking_id'] = bookingId;
            }
          }

          return Ok(
            BookingModel.fromJson(record),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> delete(String id) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.deleteBooking(
        divisionId: divisionId,
        id: id,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> update({
    required String bookingId,
    required Map<String, dynamic> fields,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBooking(
        divisionId: divisionId,
        bookingId: bookingId,
        fields: fields,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<BookingStatusModel>, AppError>> confirm({
    required String bookingId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.confirmBooking(
        divisionId: divisionId,
        bookingId: bookingId,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(BookingStatusModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<String, AppError>> create({
    required String transferType,
    required String bookingDate,
    required String reasonId,
    required String language,
    required String reference,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.createBooking(
        divisionId: divisionId,
        constCenterId: costCenterId,
        transferType: transferType,
        bookingDate: bookingDate,
        reasonId: reasonId,
        language: language,
        reference: reference,
      );

      return result.match(
        ok: (r) => Ok(
          Map<String, dynamic>.from(r.getData())['id'],
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<LookupValueModel>, AppError>> reasonLookup({
    required Map<String, FilterValueModel> filterValues,
    required String transferType,
    int page = 0,
    int pageSize = 10,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.bookingReasonLookup(
        divisionId: divisionId,
        transferType: transferType,
        query: filterValues[BOOKING_REASON_LOOKUP_FREE_TEXT_FID]!.value,
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(LookupValueModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<String?, AppError>> getVoucherUrl({
    required String bookingId,
    required String language,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getBookingVoucherUrl(
        divisionId: divisionId,
        bookingId: bookingId,
        language: language,
      );

      return result.match(
        ok: (r) => Ok(r.getData() as String?),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  // -- Items --

  Future<Result<List<BookingItemModel>, AppError>> searchItems({
    required String bookingId,
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.searchBookingItems(
        divisionId: divisionId,
        costCenterId: costCenterId,
        bookingId: bookingId,
        query: filterValues[BOOKING_PRODUCTS_FREE_TEXT_FID]!.value,
        orderByField: 'name',
        orderType: 'ASC',
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) {
          final records = List<Map<String, dynamic>>.from(response.getData());

          return Ok(
            records.map(
              (r) {
                final record = Map<String, dynamic>.from(r);
                record['booking_id'] = bookingId;

                return BookingItemModel.fromJson(record);
              },
            ).toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<BookingItemModel>, AppError>> getAllItems({
    required String bookingId,
    required String language,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getAllBookingItems(
        divisionId: divisionId,
        bookingId: bookingId,
        language: language,
      );

      return result.match(
        ok: (response) {
          final records = List<Map<String, dynamic>>.from(response.getData());

          return Ok(
            records.map(
              (r) {
                final record = Map<String, dynamic>.from(r);
                record['booking_id'] = bookingId;

                return BookingItemModel.fromJson(record);
              },
            ).toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<BookingItemModel, AppError>> getOneItemById({
    required String bookingId,
    required int id,
    required String language,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getOneBookingItemById(
        divisionId: divisionId,
        bookingId: bookingId,
        id: id,
        language: language,
      );

      return result.match(
        ok: (response) {
          final record = Map<String, dynamic>.from(response.toMap());
          record['booking_id'] = bookingId;

          return Ok(
            BookingItemModel.fromJson(record),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> deleteItem({
    required String bookingId,
    required int id,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.deleteBookingItem(
        divisionId: divisionId,
        bookingId: bookingId,
        id: id,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemSourceCostCenter({
    required String bookingId,
    required int itemId,
    required String costCenterId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {
          'src_warehouse_id': costCenterId,
          'src_store_id': null,
          'src_cost_type_id': null,
        },
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemSourceStock({
    required String bookingId,
    required int itemId,
    required String stockId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {'src_store_id': stockId},
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemSourceCostType({
    required String bookingId,
    required int itemId,
    required String costTypeId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {'src_cost_type_id': costTypeId},
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemTargetCostCenter({
    required String bookingId,
    required int itemId,
    required String costCenterId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {
          'tar_warehouse_id': costCenterId,
          'tar_store_id': null,
          'tar_cost_type_id': null,
        },
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemTargetStock({
    required String bookingId,
    required int itemId,
    required String stockId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {'tar_store_id': stockId},
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemTargetCostType({
    required String bookingId,
    required int itemId,
    required String costTypeId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {'tar_cost_type_id': costTypeId},
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemTargetDivision({
    required String bookingId,
    required int itemId,
    required String targetDivisionId,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {'tar_division_id': targetDivisionId},
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateItemQty({
    required String bookingId,
    required int itemId,
    required double qty,
    required BookingItemModel item,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingItem(
        divisionId: divisionId,
        id: itemId,
        bookingId: bookingId,
        fields: {'qty': qty},
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> addItem({
    required String bookingId,
    required String productStockId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.addBookingItem(
        divisionId: divisionId,
        bookingId: bookingId,
        productStockId: productStockId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> confirmItem({
    required String bookingId,
    required int id,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.confirmBookingItem(
        divisionId: divisionId,
        bookingId: bookingId,
        id: id,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  // -- Booking approval requests --

  Future<Result<bool, AppError>> requestApproval({
    required String approvalRequestId,
    required String approverId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.requestBookingApproval(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        approverId: approverId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<BookingApprovalRequestModel>, AppError>>
      searchApprovalRequests({
    required Map<String, FilterValueModel> filterValues,
    int limit = 5,
    int offset = 0,
    required String language,
  }) async {
    try {
      late final String filter;
      late final String orderByField;
      late final String orderType;

      switch (
          filterValues[BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_FID]!.value) {
        case BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_ALL:
          filter = 'ALL';
          break;

        case BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_NOT_ASSIGNED:
          filter = 'NOT_ASSIGNED';
          break;

        case BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME:
          filter = 'ASSIGNED_TO_ME';
          break;

        case BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_IN_PROGRESS:
          filter = 'IN_PROGRESS';
          break;

        case BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_APPROVED:
          filter = 'APPROVED';
          break;

        case BOOKING_APPROVAL_REQUESTS_FILTER_STATUS_CLOSED:
          filter = 'CLOSED';
          break;

        default:
          assert(false);
          break;
      }

      switch (filterValues[BOOKING_APPROVAL_REQUESTS_SORTING_FID]!.value) {
        case BOOKING_APPROVAL_REQUESTS_SORT_BY_REQUESTED_AT_ASC:
          orderByField = 'requested_at';
          orderType = 'ASC';
          break;
        case BOOKING_APPROVAL_REQUESTS_SORT_BY_REQUESTED_AT_DESC:
          orderByField = 'requested_at';
          orderType = 'DESC';
          break;

        case BOOKING_APPROVAL_REQUESTS_SORT_BY_APPROVED_AT_ASC:
          orderByField = 'approved_at';
          orderType = 'ASC';
          break;
        case BOOKING_APPROVAL_REQUESTS_SORT_BY_APPROVED_AT_DESC:
          orderByField = 'approved_at';
          orderType = 'DESC';
          break;

        case BOOKING_APPROVAL_REQUESTS_SORT_BY_DECLINED_AT_ASC:
          orderByField = 'declined_at';
          orderType = 'ASC';
          break;
        case BOOKING_APPROVAL_REQUESTS_SORT_BY_DECLINED_AT_DESC:
          orderByField = 'declined_at';
          orderType = 'DESC';
          break;

        case BOOKING_APPROVAL_REQUESTS_SORT_BY_CLOSED_AT_ASC:
          orderByField = 'closed_at';
          orderType = 'ASC';
          break;
        case BOOKING_APPROVAL_REQUESTS_SORT_BY_CLOSED_AT_DESC:
          orderByField = 'closed_at';
          orderType = 'DESC';
          break;

        case BOOKING_APPROVAL_REQUESTS_SORT_BY_TOTAL_AMOUNT_ASC:
          orderByField = 'total_amount';
          orderType = 'ASC';
          break;
        case BOOKING_APPROVAL_REQUESTS_SORT_BY_TOTAL_AMOUNT_DESC:
          orderByField = 'total_amount';
          orderType = 'DESC';
          break;

        default:
          assert(false);
          break;
      }

      final range = filterValues[BOOKING_APPROVAL_REQUESTS_DATE_RANGE_FID]!
          .value as DateTimeRange?;
      final df = DateFormat('dd.MM.yyyy');

      final fromDate = range != null ? df.format(range.start) : null;
      final toDate = range != null ? df.format(range.end) : null;

      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.searchBookingApprovalRequests(
        divisionId: divisionId,
        costCenterKey: costCenterId,
        query: filterValues[BOOKING_APPROVAL_REQUESTS_FREE_TEXT_FID]!.value,
        fromDate: fromDate,
        toDate: toDate,
        filter: filter,
        orderByField: orderByField,
        orderType: orderType,
        page: (offset / limit).round(),
        pageSize: limit,
        language: language,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(BookingApprovalRequestModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateApprovalRequest({
    required String approvalRequestId,
    required bool isApproved,
    required String? nextApproverId,
    String? comment,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingApprovalRequest(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        isApproved: isApproved,
        comment: comment,
        nextApproverId: nextApproverId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<BookingApprovalRequestModel, AppError>>
      getOneApprovalRequestById({
    required String approvalRequestId,
    required String language,
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getOneBookingApprovalRequestById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        approvalRequestId: approvalRequestId,
        language: language,
        includeProducts: includeProducts,
        includeLogRecords: includeLogRecords,
        includeMessages: includeMessages,
      );

      return result.match(
        ok: (response) {
          return Ok(
            BookingApprovalRequestModel.fromJson(response.toMap()),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<BookingApprovalRequestProductModel>, AppError>>
      searchApprovalRequestProducts({
    required String approvalRequestId,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.searchBookingApprovalRequestProducts(
        divisionId: divisionId,
        costCenterId: costCenterId,
        approvalRequestId: approvalRequestId,
        query: '',
        page: page,
        pageSize: pageSize,
        language: language,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(BookingApprovalRequestProductModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<BookingApprovalRequestProductModel, AppError>>
      getApprovalRequestProductById({
    required String approvalRequestId,
    required String language,
    required int productId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getOneBookingApprovalRequestProductById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        approvalRequestId: approvalRequestId,
        language: language,
        productId: productId,
      );

      return result.match(
        ok: (response) {
          return Ok(
            BookingApprovalRequestProductModel.fromJson(
              response.toMap(),
            ),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<BookingApprovalRequestProductModel>, AppError>>
      getApprovalRequestProducts({
    required String approvalRequestId,
    required String language,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getBookingApprovalRequestProducts(
        divisionId: divisionId,
        costCenterId: costCenterId,
        approvalRequestId: approvalRequestId,
        language: language,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map<BookingApprovalRequestProductModel>(
                  BookingApprovalRequestProductModel.fromJson,
                )
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> resetApprovalRequest(
    String approvalRequestId,
  ) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.resetBookingApprovalRequest(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateApprovalRequestProductQty({
    required String approvalRequestId,
    required int productId,
    required double orderUnitQty,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingApprovalRequestProductQty(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        productId: productId,
        orderUnitQty: orderUnitQty,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> updateApprovalRequestProduct({
    required String approvalRequestId,
    required int productId,
    required bool isConfirmed,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.updateBookingApprovalRequestProduct(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        productId: productId,
        isConfirmed: isConfirmed,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ChatMessageModel>, AppError>> pullApprovalRequestMessages({
    required String approvalRequestId,
    required int size,
    required String order,
    int? pullFromMessageId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.pullBookingApprovalRequestMessages(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        pullFromMessageId: pullFromMessageId,
        size: size,
        order: order,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(ChatMessageModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> sendApprovalRequestMessage({
    required String approvalRequestId,
    required String message,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.sendBookingApprovalRequestMessage(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        message: message,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<LogRecordModel>, AppError>> getLogRecords(
    String approvalRequestId,
  ) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getBookingApprovalRequestLogRecords(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(LogRecordModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ApprovalRequestDocumentModel>, AppError>> getDocuments(
    String approvalRequestId,
  ) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getBookingApprovalRequestDocuments(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(ApprovalRequestDocumentModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ApprovalTrailItemModel>, AppError>> getApprovalTrail(
    String approvalRequestId,
  ) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.getBookingApprovalRequestApprovalTrail(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
      );

      return result.match(
        ok: (response) {
          return Ok(
            List<Map<String, dynamic>>.from(response.getData())
                .map(ApprovalTrailItemModel.fromJson)
                .toList(),
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> deleteDocument({
    required String approvalRequestId,
    required String documentId,
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;

      final result = await api.deleteBookingApprovalRequestDocument(
        divisionId: divisionId,
        approvalRequestId: approvalRequestId,
        documentId: documentId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }
}
