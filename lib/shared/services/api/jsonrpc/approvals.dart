import 'package:app/shared/services/api/client/api_transport.dart';
import 'package:app/shared/services/api/response.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';

class JsonRpcApprovals {
  final ApiTransport apiTransport;

  JsonRpcApprovals(this.apiTransport);

  Future<Result<Response, AppError>> getCountOfNotApproved({
    required String orgUnitKey,
    required String costCenterKey,
    bool includePurchaseRequests = false,
    bool includeInvoices = false,
    bool includeBookingApprovalRequests = false,
    bool includeReceivingApprovalRequests = false,
    bool includeCapexApprovalRequests = false,
  }) {
    return apiTransport.secureCall(
      'Approvals.getCountOfNotApproved',
      {
        'orgUnitKey': orgUnitKey,
        'costCenterKey': costCenterKey,
        'include': [
          if (includePurchaseRequests) 'PURCHASE_REQUEST',
          if (includeInvoices) 'INVOICE',
          if (includeBookingApprovalRequests) 'BOOKING',
          if (includeReceivingApprovalRequests) 'RECEIVING',
          if (includeCapexApprovalRequests) 'CAPEX',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> approversLookup({
    required String divisionId,
    required String costCenterId,
    required String id,
    required String type,
    required int level,
  }) {
    return apiTransport.secureCall(
      'Approvals.approversLookup',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'id': id,
        'type': type,
        'level': level,
      },
    );
  }

  Future<Result<Response, AppError>> approvalsRequestorLookup({
    String? divisionId = '',
    required String query,
    required int page,
    required int pageSize,
    String? purchaseRequestStatus = 'OPEN',
  }) {
    return apiTransport.secureCall(
      'Approvals.requesterLookup',
      {
        'orgUnitKey': divisionId,
        'query': query,
        'page': page,
        'pageSize': pageSize,
        'purchaseRequestStatus': purchaseRequestStatus,
      },
    );
  }
}
