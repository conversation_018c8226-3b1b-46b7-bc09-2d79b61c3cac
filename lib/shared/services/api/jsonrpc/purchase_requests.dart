import 'package:app/shared/services/api/client/api_transport.dart';
import 'package:app/shared/services/api/response.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';

class JsonRpcPurchaseRequests {
  static const _newPurchaseRequestTransformerCompatibilityLevel = 20220729;

  final ApiTransport _apiTransport;

  const JsonRpcPurchaseRequests(this._apiTransport);

  Future<Result<Response, AppError>> search({
    required String divisionId,
    required String costCenterId,
    required String? query,
    required String? costCenterName,
    required String? category,
    required String? supplier,
    required String? itemDescription,
    required String? requesterName,
    required String status,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeFilesCount = false,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.search',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'query': query ?? '',
        'costCenterName': costCenterName ?? '',
        'category': category ?? '',
        'itemDescription': itemDescription ?? '',
        'purchaseRequestId': '',
        'requesterName': requesterName ?? '',
        'supplierName': supplier ?? '',
        'productMetaId': '',
        'status': status,
        'fromDate': fromDate,
        'toDate': toDate,
        'orderByField': orderByField,
        'orderType': orderType,
        'page': page,
        'pageSize': pageSize,
        'includes': [
          if (includeFilesCount) 'files_count',
        ],
        'compatibilityLevel': _newPurchaseRequestTransformerCompatibilityLevel,
      },
    );
  }

  Future<Result<Response, AppError>> getOneById({
    required String divisionId,
    required String costCenterId,
    required String purchaseRequestId,
    String language = 'de_DE',
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
    bool includeFiles = false,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.getOneById',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'purchaseRequestId': purchaseRequestId,
        'language': language,
        'compatibilityLevel': _newPurchaseRequestTransformerCompatibilityLevel,
        'includes': [
          if (includeProducts) 'products',
          if (includeLogRecords) 'log',
          if (includeMessages) 'messages',
          if (includeFiles) 'files',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> getLogRecords({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.getLogRecords',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> getDocuments({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.getDocuments',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> getApprovalTrail({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.getApprovalTrail',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> deleteDocument({
    required String divisionId,
    required String purchaseRequestId,
    required String documentId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.deleteDocument',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'documentId': documentId,
      },
    );
  }

  Future<Result<Response, AppError>> getMessages({
    required String divisionId,
    required String purchaseRequestId,
    required int lastMessageId,
    String? olderThan,
    int page = 0,
    int pageSize = 100,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.getMessages',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'olderThan': olderThan,
        'lastMessageId': lastMessageId,
        'page': page,
        'pageSize': pageSize,
      },
    );
  }

  Future<Result<Response, AppError>> pullMessages({
    required String divisionId,
    required String purchaseRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.pullMessages',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'pullFromMessageId': pullFromMessageId,
        'size': size,
        'order': order,
      },
    );
  }

  Future<Result<Response, AppError>> sendMessage({
    required String divisionId,
    required String purchaseRequestId,
    required String message,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.sendMessage',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'message': message,
      },
    );
  }

  Future<Result<Response, AppError>> reset({
    required String divisionId,
    required String purchaseRequestId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.reset',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> delete({
    required String divisionId,
    required String purchaseRequestId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.delete',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> moveToCart({
    required String divisionId,
    required String purchaseRequestId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.moveToCart',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> update({
    required String divisionId,
    required String purchaseRequestId,
    required bool isApproved,
    String? comment,
    String? nextApproverId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.update',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'status': isApproved ? 'APPROVED' : 'DECLINED',
        'comment': comment,
        'nextApproverId': nextApproverId,
      },
    );
  }

  Future<Result<Response, AppError>> requestApproval({
    required String divisionId,
    required String purchaseRequestId,
    required String type,
    required String approverId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.requestApproval',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'type': type,
        'approverId': approverId,
      },
    );
  }

  Future<Result<Response, AppError>> sendOrders({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.sendOrders',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'language': language,
      },
    );
  }

  // -- Product --

  Future<Result<Response, AppError>> searchProducts({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String query,
    required int page,
    required int pageSize,
    required bool includeFilesCount,
    required bool includeFiles,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.search',
      {
        'orgUnitKey': divisionId,
        'query': query,
        'purchaseRequestId': purchaseRequestId,
        'language': language,
        'page': page,
        'pageSize': pageSize,
        'includes': [
          if (includeFilesCount) 'files_count',
          if (includeFiles) 'files',
        ],
        'compatibilityLevel': _newPurchaseRequestTransformerCompatibilityLevel,
      },
    );
  }

  Future<Result<Response, AppError>> getOneProductById({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required String language,
    required bool includeFiles,
    required bool includeFilesCount,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.getOneById',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': productId,
        'language': language,
        'compatibilityLevel': _newPurchaseRequestTransformerCompatibilityLevel,
        'includes': [
          if (includeFiles) 'files',
          if (includeFilesCount) 'files_count',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> updateProduct({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required bool isConfirmed,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.update',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'productId': productId,
        'status': isConfirmed ? 'CONFIRMED' : 'DECLINED',
      },
    );
  }

  Future<Result<Response, AppError>> updateProductQty({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required double orderUnitQty,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.updateQty',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'productId': productId,
        'orderUnitQty': orderUnitQty,
      },
    );
  }

  Future<Result<Response, AppError>> getProductOffers({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.getOffers',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'language': language,
        'metaId': metaId,
      },
    );
  }

  Future<Result<Response, AppError>> changeProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
    required String replacementOfferType,
    required int replacementOfferPositionId,
    required String originalType,
    required int originalPositionId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.changeOffer',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'language': language,
        'metaId': metaId,
        'replacementOfferType': replacementOfferType,
        'replacementOfferPositionId': replacementOfferPositionId,
        'originalType': originalType,
        'originalPositionId': originalPositionId,
      },
    );
  }

  Future<Result<Response, AppError>> searchProductLog({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int page,
    required int pageSize,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Log.search',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': id,
        'userName': '',
        'page': page,
        'pageSize': pageSize,
      },
    );
  }

  Future<Result<Response, AppError>> getProductOfferById({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String language,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Offer.getOneById',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': id,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> updateProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required Map<String, dynamic> fields,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Offer.update',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': id,
        'fields': fields,
      },
    );
  }

  Future<Result<Response, AppError>> addProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String supplierId,
    required String supplierProductId,
    required String orderUnit,
    required String contentUnit,
    required double contentUnitsPerOrderUnit,
    required double inventoryUnitsPerOrderUnit,
    required double orderUnitPrice,
    String? ean,
    int? taxId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Offer.add',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': id,
        'supplierId': supplierId,
        'supplierProductId': supplierProductId,
        'orderUnit': orderUnit,
        'contentUnit': contentUnit,
        'contentUnitsPerOrderUnit': contentUnitsPerOrderUnit,
        'inventoryUnitsPerOrderUnit': inventoryUnitsPerOrderUnit,
        'orderUnitPrice': orderUnitPrice,
        'ean': ean,
        'taxId': taxId,
      },
    );
  }

  Future<Result<Response, AppError>> replaceProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int replacementId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Offer.replace',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': id,
        'replacementId': replacementId,
      },
    );
  }

  Future<Result<Response, AppError>> productSupplierLookup({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Supplier.lookup',
      {
        'orgUnitKey': divisionId,
        'query': query,
        'page': page,
        'pageSize': pageSize,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> productOfferReplacementLookup({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.Offer.replacementLookup',
      {
        'orgUnitKey': divisionId,
        'purchaseRequestId': purchaseRequestId,
        'id': id,
        'query': query,
        'page': page,
        'pageSize': pageSize,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> costTypeLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String? lookupDivisionId,
    required String? lookupCostCenterId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.costTypeLookup',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'divisionId': lookupDivisionId,
        'costCenterId': lookupCostCenterId,
        'query': query,
      },
    );
  }

  Future<Result<Response, AppError>> updateProductCostType({
    required String divisionId,
    required String costCenterId,
    required String productCostCenterId,
    required int itemPositionId,
    required String itemType,
    required String costTypeId,
    required String purchaseRequestId,
    required int productId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Product.updateCostType',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'costCenterId': productCostCenterId,
        'itemPositionId': itemPositionId,
        'itemType': itemType,
        'costTypeId': costTypeId,
        'purchaseRequestId': purchaseRequestId,
        'productId': productId,
      },
    );
  }

  Future<Result<Response, AppError>> calculateBudget({
    required String divisionId,
    required String costCenterId,
    required String targetCostCenterId,
    required String purchaseRequestId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.Budget.calculate',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'costCenterId': targetCostCenterId,
        'purchaseRequestId': purchaseRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> getExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required String purchaseRequestId,
    required int leadTime,
  }) {
    return _apiTransport.secureCall(
      'Approvals.PurchaseRequest.ExternalComment.getHistory',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'divisionId': orderDivisionId,
        'costCenterId': orderCostCenterId,
        'supplierId': supplierId,
        'purchaseRequestId': purchaseRequestId,
        'leadTime': leadTime,
      },
    );
  }
}
