import 'package:app/shared/services/api/client/api_transport.dart';
import 'package:app/shared/services/api/response.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';

class JsonRpcBookings {
  final ApiTransport _apiTransport;

  JsonRpcBookings(this._apiTransport);

  // -- Booking --

  Future<Result<Response, AppError>> search({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String status,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required List<String> transferType,
    required int page,
    required int pageSize,
    bool onlyThisCostCenter = false,
    bool includeProducts = false,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.search',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'query': query,
        'status': status,
        'fromDate': fromDate,
        'toDate': toDate,
        'orderByField': orderByField,
        'orderType': orderType,
        'transferType': transferType,
        'onlyThisCostCenter': onlyThisCostCenter,
        'page': page,
        'pageSize': pageSize,
        'includes': [
          if (includeProducts) 'products',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> getOneById({
    required String divisionId,
    required String bookingId,
    bool includeProducts = false,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.getOneById',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'includes': [
          if (includeProducts) 'products',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> delete({
    required String divisionId,
    required String id,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.delete',
      {
        'orgUnitKey': divisionId,
        'id': id,
      },
    );
  }

  Future<Result<Response, AppError>> update({
    required String divisionId,
    required String bookingId,
    required Map<String, dynamic> fields,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.update',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'fields': fields,
      },
    );
  }

  Future<Result<Response, AppError>> confirm({
    required String divisionId,
    required String bookingId,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.confirm',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
      },
    );
  }

  Future<Result<Response, AppError>> reasonLookup({
    required String divisionId,
    required String transferType,
    required String query,
    required int page,
    required int pageSize,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.reasonLookup',
      {
        'orgUnitKey': divisionId,
        'transferType': transferType,
        'query': query,
        'page': page,
        'pageSize': pageSize,
      },
    );
  }

  Future<Result<Response, AppError>> create({
    required String divisionId,
    required String constCenterId,
    required String transferType,
    required String bookingDate,
    required String reasonId,
    required String language,
    required String reference,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.create',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': constCenterId,
        'transferType': transferType,
        'bookingDate': bookingDate,
        'reasonId': reasonId,
        'language': language,
        'reference': reference,
      },
    );
  }

  Future<Result<Response, AppError>> getVoucherUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  }) {
    return _apiTransport.secureCall(
      'Bookings.getVoucherUrl',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'language': language,
      },
    );
  }

  // -- Items --

  Future<Result<Response, AppError>> searchItems({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.searchProducts',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'bookingId': bookingId,
        'query': query,
        'orderByField': orderByField,
        'orderType': orderType,
        'page': page,
        'pageSize': pageSize,
      },
    );
  }

  Future<Result<Response, AppError>> getAllItems({
    required String divisionId,
    required String bookingId,
    required String language,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.Item.getAll',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> getOneItemById({
    required String divisionId,
    required String bookingId,
    required int id,
    required String language,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.Item.getOneById',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'id': id,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> deleteItem({
    required String divisionId,
    required int id,
    required String bookingId,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.Item.delete',
      {
        'orgUnitKey': divisionId,
        'id': id,
        'bookingId': bookingId,
      },
    );
  }

  Future<Result<Response, AppError>> updateItem({
    required String divisionId,
    required String bookingId,
    required int id,
    required Map<String, dynamic> fields,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.Item.update',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'id': id,
        'fields': fields,
      },
    );
  }

  Future<Result<Response, AppError>> addItem({
    required String divisionId,
    required String bookingId,
    required String productStockId,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.Item.add',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'productStockId': productStockId,
      },
    );
  }

  Future<Result<Response, AppError>> confirmItem({
    required String divisionId,
    required String bookingId,
    required int id,
  }) async {
    return _apiTransport.secureCall(
      'Bookings.Item.confirm',
      {
        'orgUnitKey': divisionId,
        'bookingId': bookingId,
        'id': id,
      },
    );
  }

  // -- Approvals --

  Future<Result<Response, AppError>> requestApproval({
    required String divisionId,
    required String approvalRequestId,
    required String approverId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.requestApproval',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'approverId': approverId,
      },
    );
  }

  Future<Result<Response, AppError>> searchApprovalRequests({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String? fromDate,
    required String? toDate,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.search',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'query': query,
        'fromDate': fromDate,
        'toDate': toDate,
        'filter': filter,
        'orderByField': orderByField,
        'orderType': orderType,
        'page': page,
        'pageSize': pageSize,
        'language': language,
        'includes': [],
      },
    );
  }

  Future<Result<Response, AppError>> updateApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
    required bool isApproved,
    required String? comment,
    required String? nextApproverId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.Booking.update',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'status': isApproved ? 'APPROVED' : 'DECLINED',
        'comment': comment,
        'nextApproverId': nextApproverId,
      },
    );
  }

  Future<Result<Response, AppError>> getOneApprovalRequestById({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.getOneById',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'approvalRequestId': approvalRequestId,
        'language': language,
        'includes': [
          if (includeProducts) 'products',
          if (includeLogRecords) 'log',
          if (includeMessages) 'messages',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> getApprovalRequestProducts({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.getProducts',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'approvalRequestId': approvalRequestId,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> searchApprovalRequestProducts({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
    required String query,
    required int page,
    required int pageSize,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.Product.search',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'approvalRequestId': approvalRequestId,
        'language': language,
        'query': query,
        'page': page,
        'pageSize': pageSize,
      },
    );
  }

  Future<Result<Response, AppError>> getOneApprovalRequestProductById({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required int productId,
    required String language,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.Product.getOneById',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'approvalRequestId': approvalRequestId,
        'productId': productId,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> resetApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
  }) {
    return _apiTransport.secureCall(
      'Approvals.Booking.reset',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> updateApprovalRequestProductQty({
    required String divisionId,
    required String approvalRequestId,
    required int productId,
    required double orderUnitQty,
  }) {
    return _apiTransport.secureCall(
      'Approvals.Booking.updateProductQty',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'productId': productId,
        'orderUnitQty': orderUnitQty,
      },
    );
  }

  Future<Result<Response, AppError>> updateApprovalRequestProduct({
    required String divisionId,
    required String approvalRequestId,
    required int productId,
    required bool isConfirmed,
  }) {
    return _apiTransport.secureCall(
      'Approvals.Booking.updateProduct',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'productId': productId,
        'status': isConfirmed ? 'CONFIRMED' : 'DECLINED',
      },
    );
  }

  Future<Result<Response, AppError>> pullApprovalRequestMessages({
    required String divisionId,
    required String approvalRequestId,
    required int? pullFromMessageId,
    required int size,
    required String order,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.pullMessages',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'pullFromMessageId': pullFromMessageId,
        'size': size,
        'order': order,
      },
    );
  }

  Future<Result<Response, AppError>> sendApprovalRequestMessage({
    required String divisionId,
    required String approvalRequestId,
    required String message,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.sendMessage',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'message': message,
      },
    );
  }

  Future<Result<Response, AppError>> getApprovalRequestLogRecords({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.getLogRecords',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> getApprovalRequestDocuments({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.getDocuments',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> getApprovalTrail({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.getApprovalTrail',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
      },
    );
  }

  Future<Result<Response, AppError>> deleteApprovalRequestDocument({
    required String divisionId,
    required String approvalRequestId,
    required String documentId,
  }) async {
    return _apiTransport.secureCall(
      'Approvals.Booking.deleteDocument',
      {
        'orgUnitKey': divisionId,
        'approvalRequestId': approvalRequestId,
        'documentId': documentId,
      },
    );
  }
}
