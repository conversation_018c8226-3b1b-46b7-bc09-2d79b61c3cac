// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'labels_config.dart';

// **************************************************************************
// TrLabelsGenerator
// **************************************************************************

// ignore_for_file: constant_identifier_names, non_constant_identifier_names

class Labels {
  const Labels();

  static const action = _LabelsAction();
  static const add_to_cart_button = _LabelsAddToCartButton();
  static const advanced_search = _LabelsAdvancedSearch();
  static const alert_dialog = _LabelsAlertDialog();
  static const announcements = _LabelsAnnouncements();
  static const app = _LabelsApp();
  static const approvals = _LabelsApprovals();
  static const april = 'april';
  static const attachments = _LabelsAttachments();
  static const august = 'august';
  static const bluetooth = _LabelsBluetooth();
  static const bluetooth_devices = _LabelsBluetoothDevices();
  static const bluetooth_lookup = _LabelsBluetoothLookup();
  static const booking = _LabelsBooking();
  static const bookings = _LabelsBookings();
  static const budget = _LabelsBudget();
  static const bulk_price_offers = _LabelsBulkPriceOffers();
  static const button = _LabelsButton();
  static const camera = _LabelsCamera();
  static const cart = _LabelsCart();
  static const catalog = _LabelsCatalog();
  static const common = _LabelsCommon();
  static const costcenter = 'costcenter';
  static const dashboard = _LabelsDashboard();
  static const december = 'december';
  static const delivery_notes = _LabelsDeliveryNotes();
  static const dialog = _LabelsDialog();
  static const error = _LabelsError();
  static const february = 'february';
  static const filter = _LabelsFilter();
  static const fl = _LabelsFl();
  static const form = _LabelsForm();
  static const format = _LabelsFormat();
  static const free_text_order = _LabelsFreeTextOrder();
  static const friday = 'friday';
  static const help_center = _LabelsHelpCenter();
  static const history = _LabelsHistory();
  static const image = _LabelsImage();
  static const image_viewer = _LabelsImageViewer();
  static const ims = _LabelsIms();
  static const info = _LabelsInfo();
  static const inhouse_list = _LabelsInhouseList();
  static const inhouse_lists = _LabelsInhouseLists();
  static const init = _LabelsInit();
  static const inventory = _LabelsInventory();
  static const invoices = _LabelsInvoices();
  static const invoices_archive = _LabelsInvoicesArchive();
  static const january = 'january';
  static const july = 'july';
  static const june = 'june';
  static const language = _LabelsLanguage();
  static const licenses = _LabelsLicenses();
  static const lists = _LabelsLists();
  static const local_cart = _LabelsLocalCart();
  static const login = _LabelsLogin();
  static const march = 'march';
  static const may = 'may';
  static const measurement_taking = _LabelsMeasurementTaking();
  static const monday = 'monday';
  static const navigation = _LabelsNavigation();
  static const no_access = 'no_access';
  static const no_translation = 'no_translation';
  static const notifications = _LabelsNotifications();
  static const november = 'november';
  static const october = 'october';
  static const operation = _LabelsOperation();
  static const order_lists = _LabelsOrderLists();
  static const orders = _LabelsOrders();
  static const pdf = _LabelsPdf();
  static const pdf_view = _LabelsPdfView();
  static const pin_confirmation = _LabelsPinConfirmation();
  static const price = 'price';
  static const process_receiving = _LabelsProcessReceiving();
  static const process_receivings = _LabelsProcessReceivings();
  static const product = _LabelsProduct();
  static const quantity = 'quantity';
  static const quantity_bottom_sheet = _LabelsQuantityBottomSheet();
  static const quantity_selector = _LabelsQuantitySelector();
  static const receiving = _LabelsReceiving();
  static const receivings = _LabelsReceivings();
  static const recipes = _LabelsRecipes();
  static const reports = _LabelsReports();
  static const request_supplier_customer_id_button =
      _LabelsRequestSupplierCustomerIdButton();
  static const saturday = 'saturday';
  static const search = _LabelsSearch();
  static const september = 'september';
  static const settings = _LabelsSettings();
  static const setup = _LabelsSetup();
  static const sum = 'sum';
  static const sunday = 'sunday';
  static const supplier = _LabelsSupplier();
  static const supplier_label = 'supplier_label';
  static const suppliers = _LabelsSuppliers();
  static const sync = _LabelsSync();
  static const thursday = 'thursday';
  static const transfer_lists = _LabelsTransferLists();
  static const tuesday = 'tuesday';
  static const user = _LabelsUser();
  static const warehouse_lookup = _LabelsWarehouseLookup();
  static const webshop = _LabelsWebshop();
  static const wednesday = 'wednesday';
}

class _LabelsAction {
  const _LabelsAction();

  final add = 'action.add';
  final cancel = 'action.cancel';
  final confirm = 'action.confirm';
  final delete = 'action.delete';
  final ok = 'action.ok';
  final reset = 'action.reset';
  final retry = 'action.retry';
  final save = 'action.save';
  final select = 'action.select';
  final update = 'action.update';
  final yes = 'action.yes';
}

class _LabelsAddToCartButton {
  const _LabelsAddToCartButton();

  final add_to_cart = const _LabelsAddToCartButtonAddToCart();
  final bulk_prices_button_label =
      'add_to_cart_button.bulk_prices_button_label';
  final cart_delete_success = const _LabelsAddToCartButtonCartDeleteSuccess();
  final cart_update_error = const _LabelsAddToCartButtonCartUpdateError();
  final cart_update_success = const _LabelsAddToCartButtonCartUpdateSuccess();
  final consolidated_cart_item =
      const _LabelsAddToCartButtonConsolidatedCartItem();
  final current_price = 'add_to_cart_button.current_price';
  final days = const _LabelsAddToCartButtonDays();
  final delivery_time = const _LabelsAddToCartButtonDeliveryTime();
  final directly_available = const _LabelsAddToCartButtonDirectlyAvailable();
  final impossible_to_change_quantity =
      const _LabelsAddToCartButtonImpossibleToChangeQuantity();
  final last_ordered_quantity =
      const _LabelsAddToCartButtonLastOrderedQuantity();
  final minimum_buyable_qty = 'add_to_cart_button.minimum_buyable_qty';
  final offline = const _LabelsAddToCartButtonOffline();
  final price_levels = 'add_to_cart_button.price_levels';
  final product_approved_in_cart =
      const _LabelsAddToCartButtonProductApprovedInCart();
  final product_delete_from_cart_alert =
      const _LabelsAddToCartButtonProductDeleteFromCartAlert();
  final quantity_add_label = 'add_to_cart_button.quantity_add_label';
  final quantity_field_label = 'add_to_cart_button.quantity_field_label';
  final quantity_in_approval = const _LabelsAddToCartButtonQuantityInApproval();
  final quantity_update_label = 'add_to_cart_button.quantity_update_label';
}

class _LabelsAddToCartButtonAddToCart {
  const _LabelsAddToCartButtonAddToCart();

  final label = 'add_to_cart_button.add_to_cart.label';
}

class _LabelsAddToCartButtonCartDeleteSuccess {
  const _LabelsAddToCartButtonCartDeleteSuccess();

  final label = 'add_to_cart_button.cart_delete_success.label';
}

class _LabelsAddToCartButtonCartUpdateError {
  const _LabelsAddToCartButtonCartUpdateError();

  final label = 'add_to_cart_button.cart_update_error.label';
}

class _LabelsAddToCartButtonCartUpdateSuccess {
  const _LabelsAddToCartButtonCartUpdateSuccess();

  final label = 'add_to_cart_button.cart_update_success.label';
}

class _LabelsAddToCartButtonConsolidatedCartItem {
  const _LabelsAddToCartButtonConsolidatedCartItem();

  final message = 'add_to_cart_button.consolidated_cart_item.message';
}

class _LabelsAddToCartButtonDays {
  const _LabelsAddToCartButtonDays();

  final label = 'add_to_cart_button.days.label';
}

class _LabelsAddToCartButtonDeliveryTime {
  const _LabelsAddToCartButtonDeliveryTime();

  final label = 'add_to_cart_button.delivery_time.label';
}

class _LabelsAddToCartButtonDirectlyAvailable {
  const _LabelsAddToCartButtonDirectlyAvailable();

  final label = 'add_to_cart_button.directly_available.label';
}

class _LabelsAddToCartButtonImpossibleToChangeQuantity {
  const _LabelsAddToCartButtonImpossibleToChangeQuantity();

  final message = 'add_to_cart_button.impossible_to_change_quantity.message';
}

class _LabelsAddToCartButtonLastOrderedQuantity {
  const _LabelsAddToCartButtonLastOrderedQuantity();

  final label = 'add_to_cart_button.last_ordered_quantity.label';
}

class _LabelsAddToCartButtonOffline {
  const _LabelsAddToCartButtonOffline();

  final message = 'add_to_cart_button.offline.message';
}

class _LabelsAddToCartButtonProductApprovedInCart {
  const _LabelsAddToCartButtonProductApprovedInCart();

  final message = 'add_to_cart_button.product_approved_in_cart.message';
}

class _LabelsAddToCartButtonProductDeleteFromCartAlert {
  const _LabelsAddToCartButtonProductDeleteFromCartAlert();

  final content = 'add_to_cart_button.product_delete_from_cart_alert.content';
  final no_action =
      'add_to_cart_button.product_delete_from_cart_alert.no_action';
  final yes_action =
      'add_to_cart_button.product_delete_from_cart_alert.yes_action';
}

class _LabelsAddToCartButtonQuantityInApproval {
  const _LabelsAddToCartButtonQuantityInApproval();

  final label = 'add_to_cart_button.quantity_in_approval.label';
}

class _LabelsAdvancedSearch {
  const _LabelsAdvancedSearch();

  final catalog_indexing_is_in_progress =
      'advanced_search.catalog_indexing_is_in_progress';
  final customer_id_requested_label =
      'advanced_search.customer_id_requested_label';
  final filter = const _LabelsAdvancedSearchFilter();
  final filter_value = const _LabelsAdvancedSearchFilterValue();
  final no_permissions_label = 'advanced_search.no_permissions_label';
  final product = const _LabelsAdvancedSearchProduct();
  final product_offer = const _LabelsAdvancedSearchProductOffer();
  final search_available_only_online =
      'advanced_search.search_available_only_online';
  final search_results = const _LabelsAdvancedSearchSearchResults();
  final sorting = const _LabelsAdvancedSearchSorting();
}

class _LabelsAdvancedSearchFilter {
  const _LabelsAdvancedSearchFilter();

  final addLeadTimeInDays = 'advanced_search.filter.addLeadTimeInDays';
  final all_filters = const _LabelsAdvancedSearchFilterAllFilters();
  final apply = const _LabelsAdvancedSearchFilterApply();
  final availability = 'advanced_search.filter.availability';
  final button = const _LabelsAdvancedSearchFilterButton();
  final categoryLevel1 = 'advanced_search.filter.categoryLevel1';
  final categoryLevel2 = 'advanced_search.filter.categoryLevel2';
  final categoryLevel3 = 'advanced_search.filter.categoryLevel3';
  final certificates = 'advanced_search.filter.certificates';
  final clear = const _LabelsAdvancedSearchFilterClear();
  final contentUnit = 'advanced_search.filter.contentUnit';
  final currencyIso4217 = 'advanced_search.filter.currencyIso4217';
  final delivery_time = const _LabelsAdvancedSearchFilterDeliveryTime();
  final labels = 'advanced_search.filter.labels';
  final no_filters_available =
      const _LabelsAdvancedSearchFilterNoFiltersAvailable();
  final orderUnit = 'advanced_search.filter.orderUnit';
  final origin = 'advanced_search.filter.origin';
  final price = 'advanced_search.filter.price';
  final supplierName = 'advanced_search.filter.supplierName';
  final type = const _LabelsAdvancedSearchFilterType();
}

class _LabelsAdvancedSearchFilterAllFilters {
  const _LabelsAdvancedSearchFilterAllFilters();

  final label = 'advanced_search.filter.all_filters.label';
}

class _LabelsAdvancedSearchFilterApply {
  const _LabelsAdvancedSearchFilterApply();

  final label = 'advanced_search.filter.apply.label';
}

class _LabelsAdvancedSearchFilterButton {
  const _LabelsAdvancedSearchFilterButton();

  final clear_all = const _LabelsAdvancedSearchFilterButtonClearAll();
}

class _LabelsAdvancedSearchFilterButtonClearAll {
  const _LabelsAdvancedSearchFilterButtonClearAll();

  final label = 'advanced_search.filter.button.clear_all.label';
}

class _LabelsAdvancedSearchFilterClear {
  const _LabelsAdvancedSearchFilterClear();

  final label = 'advanced_search.filter.clear.label';
}

class _LabelsAdvancedSearchFilterDeliveryTime {
  const _LabelsAdvancedSearchFilterDeliveryTime();

  final days_value = 'advanced_search.filter.delivery_time.days_value';
  final directly_available_value =
      'advanced_search.filter.delivery_time.directly_available_value';
}

class _LabelsAdvancedSearchFilterNoFiltersAvailable {
  const _LabelsAdvancedSearchFilterNoFiltersAvailable();

  final label = 'advanced_search.filter.no_filters_available.label';
}

class _LabelsAdvancedSearchFilterType {
  const _LabelsAdvancedSearchFilterType();

  final EXT = 'advanced_search.filter.type.EXT';
  final TMP = 'advanced_search.filter.type.TMP';
  final WSO = 'advanced_search.filter.type.WSO';
  final WSW = 'advanced_search.filter.type.WSW';
}

class _LabelsAdvancedSearchFilterValue {
  const _LabelsAdvancedSearchFilterValue();

  final core_item = 'advanced_search.filter_value.core_item';
  final is_available = 'advanced_search.filter_value.is_available';
  final is_new = 'advanced_search.filter_value.is_new';
}

class _LabelsAdvancedSearchProduct {
  const _LabelsAdvancedSearchProduct();

  final bulk_price_label = 'advanced_search.product.bulk_price_label';
  final new_tag = const _LabelsAdvancedSearchProductNewTag();
}

class _LabelsAdvancedSearchProductNewTag {
  const _LabelsAdvancedSearchProductNewTag();

  final label = 'advanced_search.product.new_tag.label';
}

class _LabelsAdvancedSearchProductOffer {
  const _LabelsAdvancedSearchProductOffer();

  final add_to_list = const _LabelsAdvancedSearchProductOfferAddToList();
  final added_to_list_successfully_label =
      'advanced_search.product_offer.added_to_list_successfully_label';
  final additives = 'advanced_search.product_offer.additives';
  final allergens = 'advanced_search.product_offer.allergens';
  final barcode = 'advanced_search.product_offer.barcode';
  final bulk_prices_title = 'advanced_search.product_offer.bulk_prices_title';
  final content_units_per_order_unit =
      'advanced_search.product_offer.content_units_per_order_unit';
  final country_of_the_origin =
      'advanced_search.product_offer.country_of_the_origin';
  final description = 'advanced_search.product_offer.description';
  final min_order_value =
      const _LabelsAdvancedSearchProductOfferMinOrderValue();
  final min_order_value_label =
      'advanced_search.product_offer.min_order_value_label';
  final min_unit_order = const _LabelsAdvancedSearchProductOfferMinUnitOrder();
  final min_unit_order_label =
      'advanced_search.product_offer.min_unit_order_label';
  final no_connection = 'advanced_search.product_offer.no_connection';
  final no_customer_id = 'advanced_search.product_offer.no_customer_id';
  final no_permission_for_requesting_customer_id =
      'advanced_search.product_offer.no_permission_for_requesting_customer_id';
  final not_available = 'advanced_search.product_offer.not_available';
  final not_found = 'advanced_search.product_offer.not_found';
  final nutrients = 'advanced_search.product_offer.nutrients';
  final offer_end_label = 'advanced_search.product_offer.offer_end_label';
  final price_last_updated_at =
      'advanced_search.product_offer.price_last_updated_at';
  final product_description_title =
      'advanced_search.product_offer.product_description_title';
  final product_id = 'advanced_search.product_offer.product_id';
  final supplier = 'advanced_search.product_offer.supplier';
  final supplier_conditions_title =
      'advanced_search.product_offer.supplier_conditions_title';
  final supplier_item_link_label =
      'advanced_search.product_offer.supplier_item_link_label';
  final supplier_item_link_text =
      'advanced_search.product_offer.supplier_item_link_text';
}

class _LabelsAdvancedSearchProductOfferAddToList {
  const _LabelsAdvancedSearchProductOfferAddToList();

  final button = const _LabelsAdvancedSearchProductOfferAddToListButton();
  final dialog = const _LabelsAdvancedSearchProductOfferAddToListDialog();
  final title = 'advanced_search.product_offer.add_to_list.title';
}

class _LabelsAdvancedSearchProductOfferAddToListButton {
  const _LabelsAdvancedSearchProductOfferAddToListButton();

  final label = 'advanced_search.product_offer.add_to_list.button.label';
}

class _LabelsAdvancedSearchProductOfferAddToListDialog {
  const _LabelsAdvancedSearchProductOfferAddToListDialog();

  final button_label =
      'advanced_search.product_offer.add_to_list.dialog.button_label';
}

class _LabelsAdvancedSearchProductOfferMinOrderValue {
  const _LabelsAdvancedSearchProductOfferMinOrderValue();

  final any_label = 'advanced_search.product_offer.min_order_value.any_label';
}

class _LabelsAdvancedSearchProductOfferMinUnitOrder {
  const _LabelsAdvancedSearchProductOfferMinUnitOrder();

  final any_label = 'advanced_search.product_offer.min_unit_order.any_label';
}

class _LabelsAdvancedSearchSearchResults {
  const _LabelsAdvancedSearchSearchResults();

  final added_to_list_successfully_label =
      'advanced_search.search_results.added_to_list_successfully_label';
  final product_is_approved_in_cart_label =
      'advanced_search.search_results.product_is_approved_in_cart_label';
  final product_quantity_in_cart_updated_successfully_label =
      'advanced_search.search_results.product_quantity_in_cart_updated_successfully_label';
  final product_removed_from_cart_successfully_label =
      'advanced_search.search_results.product_removed_from_cart_successfully_label';
}

class _LabelsAdvancedSearchSorting {
  const _LabelsAdvancedSearchSorting();

  final best_match = 'advanced_search.sorting.best_match';
  final default_sort = 'advanced_search.sorting.default_sort';
  final price_asc = 'advanced_search.sorting.price_asc';
  final price_desc = 'advanced_search.sorting.price_desc';
}

class _LabelsAlertDialog {
  const _LabelsAlertDialog();

  final add_button_label = 'alert_dialog.add_button_label';
  final cancel_button_label = 'alert_dialog.cancel_button_label';
  final close_button_label = 'alert_dialog.close_button_label';
  final delete_button_label = 'alert_dialog.delete_button_label';
  final dismiss_button_label = 'alert_dialog.dismiss_button_label';
  final done_button_label = 'alert_dialog.done_button_label';
  final error_title = 'alert_dialog.error_title';
  final loading_label = 'alert_dialog.loading_label';
  final ok_button_label = 'alert_dialog.ok_button_label';
  final open_button_label = 'alert_dialog.open_button_label';
  final refresh_button_label = 'alert_dialog.refresh_button_label';
  final retry_button_label = 'alert_dialog.retry_button_label';
  final save_button_label = 'alert_dialog.save_button_label';
  final undo_button_label = 'alert_dialog.undo_button_label';
}

class _LabelsAnnouncements {
  const _LabelsAnnouncements();

  final continue_button_label = 'announcements.continue_button_label';
  final empty_label = 'announcements.empty_label';
  final info_dialog = const _LabelsAnnouncementsInfoDialog();
  final mark_as_read_successfully_label =
      'announcements.mark_as_read_successfully_label';
  final tile = 'announcements.tile';
}

class _LabelsAnnouncementsInfoDialog {
  const _LabelsAnnouncementsInfoDialog();

  final content = 'announcements.info_dialog.content';
  final title = 'announcements.info_dialog.title';
}

class _LabelsApp {
  const _LabelsApp();

  final asset = const _LabelsAppAsset();
  final barcode_scanner_unknown_error = 'app.barcode_scanner_unknown_error';
  final bluetooth_device = const _LabelsAppBluetoothDevice();
  final camera = const _LabelsAppCamera();
  final camera_permission_not_granted = 'app.camera_permission_not_granted';
  final cannot_launch_url_error = 'app.cannot_launch_url_error';
  final common = const _LabelsAppCommon();
  final connection = const _LabelsAppConnection();
  final cost_center_not_available = 'app.cost_center_not_available';
  final db = const _LabelsAppDb();
  final deep_link = const _LabelsAppDeepLink();
  final file = const _LabelsAppFile();
  final file_is_too_large = 'app.file_is_too_large';
  final file_not_scanned = 'app.file_not_scanned';
  final file_not_uploaded = 'app.file_not_uploaded';
  final firebase = const _LabelsAppFirebase();
  final image = const _LabelsAppImage();
  final inventory = const _LabelsAppInventory();
  final inventory_list = const _LabelsAppInventoryList();
  final inventory_list_is_not_valid = 'app.inventory_list_is_not_valid';
  final inventory_list_item_not_found = 'app.inventory_list_item_not_found';
  final inventory_list_not_loaded = 'app.inventory_list_not_loaded';
  final inventory_list_offline_search = 'app.inventory_list_offline_search';
  final localization = const _LabelsAppLocalization();
  final login = const _LabelsAppLogin();
  final maximum_file_size_is_10mb = 'app.maximum_file_size_is_10mb';
  final no_internet_connection_error = 'app.no_internet_connection_error';
  final no_license = 'app.no_license';
  final no_permission = 'app.no_permission';
  final notification = const _LabelsAppNotification();
  final order_list = const _LabelsAppOrderList();
  final pdf = const _LabelsAppPdf();
  final photo = const _LabelsAppPhoto();
  final reports = const _LabelsAppReports();
  final scanned_file_not_cropped = 'app.scanned_file_not_cropped';
  final scanned_file_not_found = 'app.scanned_file_not_found';
  final search_service_timeout_error = 'app.search_service_timeout_error';
  final search_service_unavailable_error =
      'app.search_service_unavailable_error';
  final search_service_unknown_error = 'app.search_service_unknown_error';
  final sso_link = const _LabelsAppSsoLink();
  final storage = const _LabelsAppStorage();
  final supplier = const _LabelsAppSupplier();
  final uni_link = const _LabelsAppUniLink();
  final unsupported_platform = 'app.unsupported_platform';
  final version = const _LabelsAppVersion();
}

class _LabelsAppAsset {
  const _LabelsAppAsset();

  final load_error = 'app.asset.load_error';
}

class _LabelsAppBluetoothDevice {
  const _LabelsAppBluetoothDevice();

  final already_added = 'app.bluetooth_device.already_added';
  final already_connected = 'app.bluetooth_device.already_connected';
  final change_settings_error = 'app.bluetooth_device.change_settings_error';
  final connection_error = 'app.bluetooth_device.connection_error';
  final digi_scale = const _LabelsAppBluetoothDeviceDigiScale();
  final measurement_format_error =
      'app.bluetooth_device.measurement_format_error';
  final mt_scale = const _LabelsAppBluetoothDeviceMtScale();
  final not_connected = 'app.bluetooth_device.not_connected';
  final not_valid = 'app.bluetooth_device.not_valid';
  final scanning_error = 'app.bluetooth_device.scanning_error';
  final testo_thermometer = const _LabelsAppBluetoothDeviceTestoThermometer();
}

class _LabelsAppBluetoothDeviceDigiScale {
  const _LabelsAppBluetoothDeviceDigiScale();

  final continuous_mode_disabled =
      'app.bluetooth_device.digi_scale.continuous_mode_disabled';
  final response_header_code_or_unit_disabled =
      'app.bluetooth_device.digi_scale.response_header_code_or_unit_disabled';
}

class _LabelsAppBluetoothDeviceMtScale {
  const _LabelsAppBluetoothDeviceMtScale();

  final continuous_mode_enabled =
      'app.bluetooth_device.mt_scale.continuous_mode_enabled';
  final not_mt_sic_protocol =
      'app.bluetooth_device.mt_scale.not_mt_sic_protocol';
}

class _LabelsAppBluetoothDeviceTestoThermometer {
  const _LabelsAppBluetoothDeviceTestoThermometer();

  final activation_failed =
      'app.bluetooth_device.testo_thermometer.activation_failed';
  final activation_key_retrieval_failed =
      'app.bluetooth_device.testo_thermometer.activation_key_retrieval_failed';
}

class _LabelsAppCamera {
  const _LabelsAppCamera();

  final access_denied = 'app.camera.access_denied';
  final access_denied_settings = 'app.camera.access_denied_settings';
  final access_restricted = 'app.camera.access_restricted';
  final operation_error = 'app.camera.operation_error';
}

class _LabelsAppCommon {
  const _LabelsAppCommon();

  final unexpected_error = 'app.common.unexpected_error';
}

class _LabelsAppConnection {
  const _LabelsAppConnection();

  final interrupted = 'app.connection.interrupted';
  final missing = 'app.connection.missing';
  final no_token_passed = 'app.connection.no_token_passed';
}

class _LabelsAppDb {
  const _LabelsAppDb();

  final unknown = 'app.db.unknown';
}

class _LabelsAppDeepLink {
  const _LabelsAppDeepLink();

  final unknown = 'app.deep_link.unknown';
}

class _LabelsAppFile {
  const _LabelsAppFile();

  final load_error = 'app.file.load_error';
  final save_error = 'app.file.save_error';
  final share_error = 'app.file.share_error';
  final update_error = 'app.file.update_error';
}

class _LabelsAppFirebase {
  const _LabelsAppFirebase();

  final unknown = 'app.firebase.unknown';
}

class _LabelsAppImage {
  const _LabelsAppImage();

  final not_loaded = 'app.image.not_loaded';
}

class _LabelsAppInventory {
  const _LabelsAppInventory();

  final closed_list_not_available_offline =
      'app.inventory.closed_list_not_available_offline';
  final item_not_found = 'app.inventory.item_not_found';
}

class _LabelsAppInventoryList {
  const _LabelsAppInventoryList();

  final is_not_locked_by_user = 'app.inventory_list.is_not_locked_by_user';
}

class _LabelsAppLocalization {
  const _LabelsAppLocalization();

  final cannot_load_from_api = 'app.localization.cannot_load_from_api';
  final cannot_load_from_assets = 'app.localization.cannot_load_from_assets';
}

class _LabelsAppLogin {
  const _LabelsAppLogin();

  final canceled_error = 'app.login.canceled_error';
  final otp_token_expired_error = 'app.login.otp_token_expired_error';
  final unknown_user_status_error = 'app.login.unknown_user_status_error';
}

class _LabelsAppNotification {
  const _LabelsAppNotification();

  final not_valid = 'app.notification.not_valid';
  final unit_and_cost_center_switch_required__named =
      'app.notification.unit_and_cost_center_switch_required__named';
}

class _LabelsAppOrderList {
  const _LabelsAppOrderList();

  final not_found = 'app.order_list.not_found';
  final product_not_found_in_cart = 'app.order_list.product_not_found_in_cart';
}

class _LabelsAppPdf {
  const _LabelsAppPdf();

  final generation_error = 'app.pdf.generation_error';
  final not_loaded = 'app.pdf.not_loaded';
  final print_error = 'app.pdf.print_error';
}

class _LabelsAppPhoto {
  const _LabelsAppPhoto();

  final camera_error = 'app.photo.camera_error';
  final pick_error = 'app.photo.pick_error';
}

class _LabelsAppReports {
  const _LabelsAppReports();

  final load_error = 'app.reports.load_error';
}

class _LabelsAppSsoLink {
  const _LabelsAppSsoLink();

  final login_required = 'app.sso_link.login_required';
  final unit_access_required = 'app.sso_link.unit_access_required';
  final unit_and_cost_center_switch_required__named =
      'app.sso_link.unit_and_cost_center_switch_required__named';
  final unknown = 'app.sso_link.unknown';
}

class _LabelsAppStorage {
  const _LabelsAppStorage();

  final db_backup_interrupted = 'app.storage.db_backup_interrupted';
  final db_recovery_interrupted = 'app.storage.db_recovery_interrupted';
  final db_unsupported = 'app.storage.db_unsupported';
  final not_initialized = 'app.storage.not_initialized';
}

class _LabelsAppSupplier {
  const _LabelsAppSupplier();

  final cannot_launch_oci_link = 'app.supplier.cannot_launch_oci_link';
}

class _LabelsAppUniLink {
  const _LabelsAppUniLink();

  final unknown = 'app.uni_link.unknown';
}

class _LabelsAppVersion {
  const _LabelsAppVersion();

  final cancel_button_label = 'app.version.cancel_button_label';
  final store_launch_error_message = 'app.version.store_launch_error_message';
  final update_button_label = 'app.version.update_button_label';
  final version_is_lower_than_minimal_required =
      'app.version.version_is_lower_than_minimal_required';
  final version_is_lower_than_the_latest =
      'app.version.version_is_lower_than_the_latest';
}

class _LabelsApprovals {
  const _LabelsApprovals();

  final activity_log = const _LabelsApprovalsActivityLog();
  final approval_requests = const _LabelsApprovalsApprovalRequests();
  final approval_trail = const _LabelsApprovalsApprovalTrail();
  final approver = 'approvals.approver';
  final approver_selection = const _LabelsApprovalsApproverSelection();
  final booking_request = const _LabelsApprovalsBookingRequest();
  final booking_requests = const _LabelsApprovalsBookingRequests();
  final capex = const _LabelsApprovalsCapex();
  final capex_request = const _LabelsApprovalsCapexRequest();
  final capex_requests = const _LabelsApprovalsCapexRequests();
  final category = 'approvals.category';
  final invoices = const _LabelsApprovalsInvoices();
  final log = const _LabelsApprovalsLog();
  final order_value_to_be_approved = 'approvals.order_value_to_be_approved';
  final purchase_request = const _LabelsApprovalsPurchaseRequest();
  final purchase_requests = const _LabelsApprovalsPurchaseRequests();
  final receiving_request = const _LabelsApprovalsReceivingRequest();
  final receiving_requests = const _LabelsApprovalsReceivingRequests();
  final request = const _LabelsApprovalsRequest();
  final request_id = 'approvals.request_id';
  final requests = const _LabelsApprovalsRequests();
}

class _LabelsApprovalsActivityLog {
  const _LabelsApprovalsActivityLog();

  final title = 'approvals.activity_log.title';
}

class _LabelsApprovalsApprovalRequests {
  const _LabelsApprovalsApprovalRequests();

  final details_button_label =
      'approvals.approval_requests.details_button_label';
  final log_button_label = 'approvals.approval_requests.log_button_label';
  final reset_button_label = 'approvals.approval_requests.reset_button_label';
  final view_pdf_button_label =
      'approvals.approval_requests.view_pdf_button_label';
}

class _LabelsApprovalsApprovalTrail {
  const _LabelsApprovalsApprovalTrail();

  final amount_label = 'approvals.approval_trail.amount_label';
  final approved_by_label = 'approvals.approval_trail.approved_by_label';
  final date_time_label = 'approvals.approval_trail.date_time_label';
  final empty = 'approvals.approval_trail.empty';
  final in_approval = 'approvals.approval_trail.in_approval';
  final level_label = 'approvals.approval_trail.level_label';
  final substitute_label = 'approvals.approval_trail.substitute_label';
  final title = 'approvals.approval_trail.title';
}

class _LabelsApprovalsApproverSelection {
  const _LabelsApprovalsApproverSelection();

  final select_button_label =
      'approvals.approver_selection.select_button_label';
  final selection_available_only_online =
      'approvals.approver_selection.selection_available_only_online';
  final title = 'approvals.approver_selection.title';
}

class _LabelsApprovalsBookingRequest {
  const _LabelsApprovalsBookingRequest();

  final approver = 'approvals.booking_request.approver';
  final available_only_online =
      'approvals.booking_request.available_only_online';
  final comment = 'approvals.booking_request.comment';
  final details = const _LabelsApprovalsBookingRequestDetails();
  final item = const _LabelsApprovalsBookingRequestItem();
  final product = const _LabelsApprovalsBookingRequestProduct();
  final total = 'approvals.booking_request.total';
}

class _LabelsApprovalsBookingRequestDetails {
  const _LabelsApprovalsBookingRequestDetails();

  final approval_user = 'approvals.booking_request.details.approval_user';
  final list_name = 'approvals.booking_request.details.list_name';
  final requested_at = 'approvals.booking_request.details.requested_at';
  final requested_by = 'approvals.booking_request.details.requested_by';
  final status = const _LabelsApprovalsBookingRequestDetailsStatus();
  final status_label = 'approvals.booking_request.details.status_label';
  final title = 'approvals.booking_request.details.title';
  final total = 'approvals.booking_request.details.total';
  final type = 'approvals.booking_request.details.type';
}

class _LabelsApprovalsBookingRequestDetailsStatus {
  const _LabelsApprovalsBookingRequestDetailsStatus();

  final approved = 'approvals.booking_request.details.status.approved';
  final declined = 'approvals.booking_request.details.status.declined';
  final in_progress = 'approvals.booking_request.details.status.in_progress';
  final not_assigned = 'approvals.booking_request.details.status.not_assigned';
}

class _LabelsApprovalsBookingRequestItem {
  const _LabelsApprovalsBookingRequestItem();

  final edit_quantity_button_label =
      'approvals.booking_request.item.edit_quantity_button_label';
}

class _LabelsApprovalsBookingRequestProduct {
  const _LabelsApprovalsBookingRequestProduct();

  final disable_button_label =
      'approvals.booking_request.product.disable_button_label';
  final enable_button_label =
      'approvals.booking_request.product.enable_button_label';
}

class _LabelsApprovalsBookingRequests {
  const _LabelsApprovalsBookingRequests();

  final approval_request_alert =
      const _LabelsApprovalsBookingRequestsApprovalRequestAlert();
  final approval_requested_successfully =
      'approvals.booking_requests.approval_requested_successfully';
  final approve_button_label =
      'approvals.booking_requests.approve_button_label';
  final booking_approval_request_update_alert =
      const _LabelsApprovalsBookingRequestsBookingApprovalRequestUpdateAlert();
  final booking_approval_request_updated_successfully =
      'approvals.booking_requests.booking_approval_request_updated_successfully';
  final chat = const _LabelsApprovalsBookingRequestsChat();
  final decline_button_label =
      'approvals.booking_requests.decline_button_label';
  final details_button_label =
      'approvals.booking_requests.details_button_label';
  final documents = const _LabelsApprovalsBookingRequestsDocuments();
  final filter = const _LabelsApprovalsBookingRequestsFilter();
  final hide_changes_button_label =
      'approvals.booking_requests.hide_changes_button_label';
  final item = const _LabelsApprovalsBookingRequestsItem();
  final last_qty = 'approvals.booking_requests.last_qty';
  final log = const _LabelsApprovalsBookingRequestsLog();
  final log_button_label = 'approvals.booking_requests.log_button_label';
  final original_qty = 'approvals.booking_requests.original_qty';
  final qty_changed = 'approvals.booking_requests.qty_changed';
  final request_approval_button_label =
      'approvals.booking_requests.request_approval_button_label';
  final reset_alert = const _LabelsApprovalsBookingRequestsResetAlert();
  final reset_button_label = 'approvals.booking_requests.reset_button_label';
  final resetted_successfully =
      'approvals.booking_requests.resetted_successfully';
  final search_available_only_online =
      'approvals.booking_requests.search_available_only_online';
  final show_changes_button_label =
      'approvals.booking_requests.show_changes_button_label';
  final sort = const _LabelsApprovalsBookingRequestsSort();
  final update_button_label = 'approvals.booking_requests.update_button_label';
  final view_pdf_button_label =
      'approvals.booking_requests.view_pdf_button_label';
}

class _LabelsApprovalsBookingRequestsApprovalRequestAlert {
  const _LabelsApprovalsBookingRequestsApprovalRequestAlert();

  final content = 'approvals.booking_requests.approval_request_alert.content';
  final no_action =
      'approvals.booking_requests.approval_request_alert.no_action';
  final yes_action =
      'approvals.booking_requests.approval_request_alert.yes_action';
}

class _LabelsApprovalsBookingRequestsBookingApprovalRequestUpdateAlert {
  const _LabelsApprovalsBookingRequestsBookingApprovalRequestUpdateAlert();

  final content =
      'approvals.booking_requests.booking_approval_request_update_alert.content';
  final no_action =
      'approvals.booking_requests.booking_approval_request_update_alert.no_action';
  final yes_action =
      'approvals.booking_requests.booking_approval_request_update_alert.yes_action';
}

class _LabelsApprovalsBookingRequestsChat {
  const _LabelsApprovalsBookingRequestsChat();

  final no_messages = 'approvals.booking_requests.chat.no_messages';
}

class _LabelsApprovalsBookingRequestsDocuments {
  const _LabelsApprovalsBookingRequestsDocuments();

  final no_approval_trail =
      'approvals.booking_requests.documents.no_approval_trail';
  final no_documents = 'approvals.booking_requests.documents.no_documents';
}

class _LabelsApprovalsBookingRequestsFilter {
  const _LabelsApprovalsBookingRequestsFilter();

  final status = const _LabelsApprovalsBookingRequestsFilterStatus();
  final status_label = 'approvals.booking_requests.filter.status_label';
}

class _LabelsApprovalsBookingRequestsFilterStatus {
  const _LabelsApprovalsBookingRequestsFilterStatus();

  final all = 'approvals.booking_requests.filter.status.all';
  final approved = 'approvals.booking_requests.filter.status.approved';
  final assigned_to_me =
      'approvals.booking_requests.filter.status.assigned_to_me';
  final closed = 'approvals.booking_requests.filter.status.closed';
  final date_range = 'approvals.booking_requests.filter.status.date_range';
  final in_progress = 'approvals.booking_requests.filter.status.in_progress';
  final not_assigned = 'approvals.booking_requests.filter.status.not_assigned';
}

class _LabelsApprovalsBookingRequestsItem {
  const _LabelsApprovalsBookingRequestsItem();

  final inventory_unit_price_label =
      'approvals.booking_requests.item.inventory_unit_price_label';
  final last_order_date = 'approvals.booking_requests.item.last_order_date';
  final last_order_price = 'approvals.booking_requests.item.last_order_price';
  final last_order_qty = 'approvals.booking_requests.item.last_order_qty';
  final max_stock = 'approvals.booking_requests.item.max_stock';
  final min_stock = 'approvals.booking_requests.item.min_stock';
  final qty_updated_successfully =
      'approvals.booking_requests.item.qty_updated_successfully';
  final quantity_field_label =
      'approvals.booking_requests.item.quantity_field_label';
  final quantity_update_label =
      'approvals.booking_requests.item.quantity_update_label';
  final receiving_division_price_label =
      'approvals.booking_requests.item.receiving_division_price_label';
  final status_updated_successfully =
      'approvals.booking_requests.item.status_updated_successfully';
  final stock_on_hand = 'approvals.booking_requests.item.stock_on_hand';
  final total_label = 'approvals.booking_requests.item.total_label';
}

class _LabelsApprovalsBookingRequestsLog {
  const _LabelsApprovalsBookingRequestsLog();

  final no_record = 'approvals.booking_requests.log.no_record';
}

class _LabelsApprovalsBookingRequestsResetAlert {
  const _LabelsApprovalsBookingRequestsResetAlert();

  final content = 'approvals.booking_requests.reset_alert.content';
  final no_action = 'approvals.booking_requests.reset_alert.no_action';
  final yes_action = 'approvals.booking_requests.reset_alert.yes_action';
}

class _LabelsApprovalsBookingRequestsSort {
  const _LabelsApprovalsBookingRequestsSort();

  final by_approved_at_asc =
      'approvals.booking_requests.sort.by_approved_at_asc';
  final by_approved_at_desc =
      'approvals.booking_requests.sort.by_approved_at_desc';
  final by_closed_at_asc = 'approvals.booking_requests.sort.by_closed_at_asc';
  final by_closed_at_desc = 'approvals.booking_requests.sort.by_closed_at_desc';
  final by_declined_at_asc =
      'approvals.booking_requests.sort.by_declined_at_asc';
  final by_declined_at_desc =
      'approvals.booking_requests.sort.by_declined_at_desc';
  final by_requested_at_asc =
      'approvals.booking_requests.sort.by_requested_at_asc';
  final by_requested_at_desc =
      'approvals.booking_requests.sort.by_requested_at_desc';
  final by_total_amount_asc =
      'approvals.booking_requests.sort.by_total_amount_asc';
  final by_total_amount_desc =
      'approvals.booking_requests.sort.by_total_amount_desc';
}

class _LabelsApprovalsCapex {
  const _LabelsApprovalsCapex();

  final active_tab = 'approvals.capex.active_tab';
  final authorized_tab = 'approvals.capex.authorized_tab';
  final closed_tab = 'approvals.capex.closed_tab';
}

class _LabelsApprovalsCapexRequest {
  const _LabelsApprovalsCapexRequest();

  final approve_button_label = 'approvals.capex_request.approve_button_label';
  final decline_button_label = 'approvals.capex_request.decline_button_label';
  final details = const _LabelsApprovalsCapexRequestDetails();
  final documents = const _LabelsApprovalsCapexRequestDocuments();
  final fields_not_filled_error_message =
      'approvals.capex_request.fields_not_filled_error_message';
  final invoices = const _LabelsApprovalsCapexRequestInvoices();
  final messages = const _LabelsApprovalsCapexRequestMessages();
  final overview = const _LabelsApprovalsCapexRequestOverview();
  final request_approval_button_label =
      'approvals.capex_request.request_approval_button_label';
  final update = const _LabelsApprovalsCapexRequestUpdate();
}

class _LabelsApprovalsCapexRequestDetails {
  const _LabelsApprovalsCapexRequestDetails();

  final accounting_title = 'approvals.capex_request.details.accounting_title';
  final additional_details_title =
      'approvals.capex_request.details.additional_details_title';
  final capex_description_title =
      'approvals.capex_request.details.capex_description_title';
  final instructions_for_accounting =
      'approvals.capex_request.details.instructions_for_accounting';
  final supplier = const _LabelsApprovalsCapexRequestDetailsSupplier();
  final suppliers_title = 'approvals.capex_request.details.suppliers_title';
  final title = 'approvals.capex_request.details.title';
}

class _LabelsApprovalsCapexRequestDetailsSupplier {
  const _LabelsApprovalsCapexRequestDetailsSupplier();

  final contract_period =
      'approvals.capex_request.details.supplier.contract_period';
  final reason_of_selection =
      'approvals.capex_request.details.supplier.reason_of_selection';
  final selected_tag = 'approvals.capex_request.details.supplier.selected_tag';
}

class _LabelsApprovalsCapexRequestDocuments {
  const _LabelsApprovalsCapexRequestDocuments();

  final title = 'approvals.capex_request.documents.title';
}

class _LabelsApprovalsCapexRequestInvoices {
  const _LabelsApprovalsCapexRequestInvoices();

  final amount_gross = 'approvals.capex_request.invoices.amount_gross';
  final title = 'approvals.capex_request.invoices.title';
}

class _LabelsApprovalsCapexRequestMessages {
  const _LabelsApprovalsCapexRequestMessages();

  final add_label = 'approvals.capex_request.messages.add_label';
  final message_label = 'approvals.capex_request.messages.message_label';
  final title = 'approvals.capex_request.messages.title';
}

class _LabelsApprovalsCapexRequestOverview {
  const _LabelsApprovalsCapexRequestOverview();

  final amount_in_division_currency =
      'approvals.capex_request.overview.amount_in_division_currency';
  final budget = 'approvals.capex_request.overview.budget';
  final cost_center = 'approvals.capex_request.overview.cost_center';
  final created_by = 'approvals.capex_request.overview.created_by';
  final date_nad_time = 'approvals.capex_request.overview.date_nad_time';
  final department = 'approvals.capex_request.overview.department';
  final division = 'approvals.capex_request.overview.division';
  final fields_not_filled_error =
      'approvals.capex_request.overview.fields_not_filled_error';
  final last_approver_commented =
      'approvals.capex_request.overview.last_approver_commented';
  final supplier = 'approvals.capex_request.overview.supplier';
  final title = 'approvals.capex_request.overview.title';
  final total_amount = 'approvals.capex_request.overview.total_amount';
  final used_amount = 'approvals.capex_request.overview.used_amount';
  final used_amount_in_division_currency =
      'approvals.capex_request.overview.used_amount_in_division_currency';
}

class _LabelsApprovalsCapexRequestUpdate {
  const _LabelsApprovalsCapexRequestUpdate();

  final add_comment = 'approvals.capex_request.update.add_comment';
  final approval_level = 'approvals.capex_request.update.approval_level';
  final approval_level_text =
      'approvals.capex_request.update.approval_level_text';
  final approve_title = 'approvals.capex_request.update.approve_title';
  final approved_successfully =
      'approvals.capex_request.update.approved_successfully';
  final approver_label = 'approvals.capex_request.update.approver_label';
  final available_only_online =
      'approvals.capex_request.update.available_only_online';
  final can_not_be_authorized =
      'approvals.capex_request.update.can_not_be_authorized';
  final can_not_be_requested =
      'approvals.capex_request.update.can_not_be_requested';
  final comment_label = 'approvals.capex_request.update.comment_label';
  final decline_title = 'approvals.capex_request.update.decline_title';
  final declined_successfully =
      'approvals.capex_request.update.declined_successfully';
  final last_approver = 'approvals.capex_request.update.last_approver';
  final last_comment = 'approvals.capex_request.update.last_comment';
  final last_date = 'approvals.capex_request.update.last_date';
  final next_approver_label =
      'approvals.capex_request.update.next_approver_label';
  final please_add_comment =
      'approvals.capex_request.update.please_add_comment';
  final please_select_approver =
      'approvals.capex_request.update.please_select_approver';
  final request_approval_confirmation =
      const _LabelsApprovalsCapexRequestUpdateRequestApprovalConfirmation();
  final request_title = 'approvals.capex_request.update.request_title';
  final requested_successfully =
      'approvals.capex_request.update.requested_successfully';
  final user_can_not_authorize =
      'approvals.capex_request.update.user_can_not_authorize';
}

class _LabelsApprovalsCapexRequestUpdateRequestApprovalConfirmation {
  const _LabelsApprovalsCapexRequestUpdateRequestApprovalConfirmation();

  final title =
      'approvals.capex_request.update.request_approval_confirmation.title';
  final yes =
      'approvals.capex_request.update.request_approval_confirmation.yes';
}

class _LabelsApprovalsCapexRequests {
  const _LabelsApprovalsCapexRequests();

  final budgeted_status = const _LabelsApprovalsCapexRequestsBudgetedStatus();
  final filter = const _LabelsApprovalsCapexRequestsFilter();
  final search_available_only_online =
      'approvals.capex_requests.search_available_only_online';
  final sort = const _LabelsApprovalsCapexRequestsSort();
  final status = const _LabelsApprovalsCapexRequestsStatus();
  final total_amount = 'approvals.capex_requests.total_amount';
  final used_amount = 'approvals.capex_requests.used_amount';
}

class _LabelsApprovalsCapexRequestsBudgetedStatus {
  const _LabelsApprovalsCapexRequestsBudgetedStatus();

  final budget_not_defined =
      'approvals.capex_requests.budgeted_status.budget_not_defined';
  final budgeted = 'approvals.capex_requests.budgeted_status.budgeted';
  final not_budgeted = 'approvals.capex_requests.budgeted_status.not_budgeted';
  final unknown = 'approvals.capex_requests.budgeted_status.unknown';
}

class _LabelsApprovalsCapexRequestsFilter {
  const _LabelsApprovalsCapexRequestsFilter();

  final division_hint = 'approvals.capex_requests.filter.division_hint';
  final division_label = 'approvals.capex_requests.filter.division_label';
  final status = const _LabelsApprovalsCapexRequestsFilterStatus();
  final status_label = 'approvals.capex_requests.filter.status_label';
}

class _LabelsApprovalsCapexRequestsFilterStatus {
  const _LabelsApprovalsCapexRequestsFilterStatus();

  final active = 'approvals.capex_requests.filter.status.active';
  final approved = 'approvals.capex_requests.filter.status.approved';
  final assigned_to_me =
      'approvals.capex_requests.filter.status.assigned_to_me';
  final authorized = 'approvals.capex_requests.filter.status.authorized';
  final closed = 'approvals.capex_requests.filter.status.closed';
  final created = 'approvals.capex_requests.filter.status.created';
  final declined = 'approvals.capex_requests.filter.status.declined';
  final in_progress = 'approvals.capex_requests.filter.status.in_progress';
}

class _LabelsApprovalsCapexRequestsSort {
  const _LabelsApprovalsCapexRequestsSort();

  final by_created_at_desc = 'approvals.capex_requests.sort.by_created_at_desc';
  final by_name_asc = 'approvals.capex_requests.sort.by_name_asc';
  final by_name_desc = 'approvals.capex_requests.sort.by_name_desc';
}

class _LabelsApprovalsCapexRequestsStatus {
  const _LabelsApprovalsCapexRequestsStatus();

  final approved = 'approvals.capex_requests.status.approved';
  final closed = 'approvals.capex_requests.status.closed';
  final created = 'approvals.capex_requests.status.created';
  final declined = 'approvals.capex_requests.status.declined';
  final deleted = 'approvals.capex_requests.status.deleted';
  final in_approval = 'approvals.capex_requests.status.in_approval';
  final unknown = 'approvals.capex_requests.status.unknown';
}

class _LabelsApprovalsInvoices {
  const _LabelsApprovalsInvoices();

  final filter = const _LabelsApprovalsInvoicesFilter();
  final invoice = const _LabelsApprovalsInvoicesInvoice();
  final search_available_only_online =
      'approvals.invoices.search_available_only_online';
}

class _LabelsApprovalsInvoicesFilter {
  const _LabelsApprovalsInvoicesFilter();

  final invoice_id = const _LabelsApprovalsInvoicesFilterInvoiceId();
  final match_type = const _LabelsApprovalsInvoicesFilterMatchType();
  final order_id = const _LabelsApprovalsInvoicesFilterOrderId();
  final sorting = const _LabelsApprovalsInvoicesFilterSorting();
  final status = const _LabelsApprovalsInvoicesFilterStatus();
  final supplier_invoice_id =
      const _LabelsApprovalsInvoicesFilterSupplierInvoiceId();
  final supplier_name = const _LabelsApprovalsInvoicesFilterSupplierName();
}

class _LabelsApprovalsInvoicesFilterInvoiceId {
  const _LabelsApprovalsInvoicesFilterInvoiceId();

  final hint_text = 'approvals.invoices.filter.invoice_id.hint_text';
  final label = 'approvals.invoices.filter.invoice_id.label';
}

class _LabelsApprovalsInvoicesFilterMatchType {
  const _LabelsApprovalsInvoicesFilterMatchType();

  final label = 'approvals.invoices.filter.match_type.label';
  final match_all = 'approvals.invoices.filter.match_type.match_all';
  final match_exact = 'approvals.invoices.filter.match_type.match_exact';
  final match_partial = 'approvals.invoices.filter.match_type.match_partial';
  final match_related = 'approvals.invoices.filter.match_type.match_related';
}

class _LabelsApprovalsInvoicesFilterOrderId {
  const _LabelsApprovalsInvoicesFilterOrderId();

  final hint_text = 'approvals.invoices.filter.order_id.hint_text';
  final label = 'approvals.invoices.filter.order_id.label';
}

class _LabelsApprovalsInvoicesFilterSorting {
  const _LabelsApprovalsInvoicesFilterSorting();

  final by_invoice_date_asc =
      'approvals.invoices.filter.sorting.by_invoice_date_asc';
  final by_invoice_date_desc =
      'approvals.invoices.filter.sorting.by_invoice_date_desc';
  final by_requested_at_asc =
      'approvals.invoices.filter.sorting.by_requested_at_asc';
  final by_requested_at_desc =
      'approvals.invoices.filter.sorting.by_requested_at_desc';
  final by_supplier_name_asc =
      'approvals.invoices.filter.sorting.by_supplier_name_asc';
  final by_supplier_name_desc =
      'approvals.invoices.filter.sorting.by_supplier_name_desc';
  final by_upload_date_asc =
      'approvals.invoices.filter.sorting.by_upload_date_asc';
  final by_upload_date_desc =
      'approvals.invoices.filter.sorting.by_upload_date_desc';
}

class _LabelsApprovalsInvoicesFilterStatus {
  const _LabelsApprovalsInvoicesFilterStatus();

  final assigned_to_me = 'approvals.invoices.filter.status.assigned_to_me';
  final ready_to_approve = 'approvals.invoices.filter.status.ready_to_approve';
  final title = 'approvals.invoices.filter.status.title';
}

class _LabelsApprovalsInvoicesFilterSupplierInvoiceId {
  const _LabelsApprovalsInvoicesFilterSupplierInvoiceId();

  final hint_text = 'approvals.invoices.filter.supplier_invoice_id.hint_text';
  final label = 'approvals.invoices.filter.supplier_invoice_id.label';
}

class _LabelsApprovalsInvoicesFilterSupplierName {
  const _LabelsApprovalsInvoicesFilterSupplierName();

  final hint_text = 'approvals.invoices.filter.supplier_name.hint_text';
  final label = 'approvals.invoices.filter.supplier_name.label';
}

class _LabelsApprovalsInvoicesInvoice {
  const _LabelsApprovalsInvoicesInvoice();

  final amount_brutto = 'approvals.invoices.invoice.amount_brutto';
  final amount_netto = 'approvals.invoices.invoice.amount_netto';
  final amount_tax = 'approvals.invoices.invoice.amount_tax';
  final approval_request_confirmation =
      const _LabelsApprovalsInvoicesInvoiceApprovalRequestConfirmation();
  final approval_requested_successfully =
      'approvals.invoices.invoice.approval_requested_successfully';
  final approval_trail_tab = 'approvals.invoices.invoice.approval_trail_tab';
  final approve = 'approvals.invoices.invoice.approve';
  final approve_confirmation =
      const _LabelsApprovalsInvoicesInvoiceApproveConfirmation();
  final approver = 'approvals.invoices.invoice.approver';
  final attachments = const _LabelsApprovalsInvoicesInvoiceAttachments();
  final attachments_label = 'approvals.invoices.invoice.attachments_label';
  final attachments_tab = 'approvals.invoices.invoice.attachments_tab';
  final details_tab = 'approvals.invoices.invoice.details_tab';
  final invoice_approved_successfully =
      'approvals.invoices.invoice.invoice_approved_successfully';
  final invoice_comment = 'approvals.invoices.invoice.invoice_comment';
  final invoice_date = 'approvals.invoices.invoice.invoice_date';
  final log_button_label = 'approvals.invoices.invoice.log_button_label';
  final next_approver_label = 'approvals.invoices.invoice.next_approver_label';
  final request_approval = 'approvals.invoices.invoice.request_approval';
  final reset_button_label = 'approvals.invoices.invoice.reset_button_label';
  final reset_confirmation =
      const _LabelsApprovalsInvoicesInvoiceResetConfirmation();
  final reset_reason_comment_label =
      'approvals.invoices.invoice.reset_reason_comment_label';
  final reset_reason_comment_title =
      'approvals.invoices.invoice.reset_reason_comment_title';
  final reset_reason_title = 'approvals.invoices.invoice.reset_reason_title';
  final resetted_successfully =
      'approvals.invoices.invoice.resetted_successfully';
  final send_to_accounting = 'approvals.invoices.invoice.send_to_accounting';
  final send_to_accounting_confirmation =
      const _LabelsApprovalsInvoicesInvoiceSendToAccountingConfirmation();
  final send_to_accounting_open =
      'approvals.invoices.invoice.send_to_accounting_open';
  final send_to_accounting_successful =
      'approvals.invoices.invoice.send_to_accounting_successful';
  final status = const _LabelsApprovalsInvoicesInvoiceStatus();
  final view_pdf = 'approvals.invoices.invoice.view_pdf';
  final view_pdf_button = 'approvals.invoices.invoice.view_pdf_button';
  final view_pdf_button_label =
      'approvals.invoices.invoice.view_pdf_button_label';
}

class _LabelsApprovalsInvoicesInvoiceApprovalRequestConfirmation {
  const _LabelsApprovalsInvoicesInvoiceApprovalRequestConfirmation();

  final title =
      'approvals.invoices.invoice.approval_request_confirmation.title';
}

class _LabelsApprovalsInvoicesInvoiceApproveConfirmation {
  const _LabelsApprovalsInvoicesInvoiceApproveConfirmation();

  final title = 'approvals.invoices.invoice.approve_confirmation.title';
}

class _LabelsApprovalsInvoicesInvoiceAttachments {
  const _LabelsApprovalsInvoicesInvoiceAttachments();

  final document = 'approvals.invoices.invoice.attachments.document';
  final documents = 'approvals.invoices.invoice.attachments.documents';
}

class _LabelsApprovalsInvoicesInvoiceResetConfirmation {
  const _LabelsApprovalsInvoicesInvoiceResetConfirmation();

  final reset_action =
      'approvals.invoices.invoice.reset_confirmation.reset_action';
}

class _LabelsApprovalsInvoicesInvoiceSendToAccountingConfirmation {
  const _LabelsApprovalsInvoicesInvoiceSendToAccountingConfirmation();

  final send =
      'approvals.invoices.invoice.send_to_accounting_confirmation.send';
  final title =
      'approvals.invoices.invoice.send_to_accounting_confirmation.title';
}

class _LabelsApprovalsInvoicesInvoiceStatus {
  const _LabelsApprovalsInvoicesInvoiceStatus();

  final approval = 'approvals.invoices.invoice.status.approval';
  final approved = 'approvals.invoices.invoice.status.approved';
  final checksum_error = 'approvals.invoices.invoice.status.checksum_error';
  final in_approval = 'approvals.invoices.invoice.status.in_approval';
  final no_errors = 'approvals.invoices.invoice.status.no_errors';
}

class _LabelsApprovalsLog {
  const _LabelsApprovalsLog();

  final available_only_online = 'approvals.log.available_only_online';
  final no_records = 'approvals.log.no_records';
}

class _LabelsApprovalsPurchaseRequest {
  const _LabelsApprovalsPurchaseRequest();

  final accept_reason = 'approvals.purchase_request.accept_reason';
  final added_to_card_at = 'approvals.purchase_request.added_to_card_at';
  final all_purchase_requests_are_processed =
      'approvals.purchase_request.all_purchase_requests_are_processed';
  final approval_last_date = 'approvals.purchase_request.approval_last_date';
  final approval_last_user = 'approvals.purchase_request.approval_last_user';
  final approval_trail = const _LabelsApprovalsPurchaseRequestApprovalTrail();
  final approval_user = 'approvals.purchase_request.approval_user';
  final approve_button_label =
      'approvals.purchase_request.approve_button_label';
  final approver = 'approvals.purchase_request.approver';
  final available_only_online =
      'approvals.purchase_request.available_only_online';
  final better_offers = 'approvals.purchase_request.better_offers';
  final better_price_available =
      'approvals.purchase_request.better_price_available';
  final chat = const _LabelsApprovalsPurchaseRequestChat();
  final comment = 'approvals.purchase_request.comment';
  final decline_button_label =
      'approvals.purchase_request.decline_button_label';
  final decline_reason = 'approvals.purchase_request.decline_reason';
  final delete = 'approvals.purchase_request.delete';
  final details = const _LabelsApprovalsPurchaseRequestDetails();
  final details_button_label =
      'approvals.purchase_request.details_button_label';
  final documents = const _LabelsApprovalsPurchaseRequestDocuments();
  final hide_changes_button_label =
      'approvals.purchase_request.hide_changes_button_label';
  final item = const _LabelsApprovalsPurchaseRequestItem();
  final item_declined = 'approvals.purchase_request.item_declined';
  final last_qty = 'approvals.purchase_request.last_qty';
  final last_supplier = 'approvals.purchase_request.last_supplier';
  final log = const _LabelsApprovalsPurchaseRequestLog();
  final log_button_label = 'approvals.purchase_request.log_button_label';
  final looking_for_the_next =
      'approvals.purchase_request.looking_for_the_next';
  final move_to_cart = 'approvals.purchase_request.move_to_cart';
  final move_to_cart_alert =
      const _LabelsApprovalsPurchaseRequestMoveToCartAlert();
  final move_to_cart_button_label =
      'approvals.purchase_request.move_to_cart_button_label';
  final moved_to_cart_successfully =
      'approvals.purchase_request.moved_to_cart_successfully';
  final offers = 'approvals.purchase_request.offers';
  final orders_sending_result =
      const _LabelsApprovalsPurchaseRequestOrdersSendingResult();
  final original_qty = 'approvals.purchase_request.original_qty';
  final original_supplier = 'approvals.purchase_request.original_supplier';
  final product_offers = const _LabelsApprovalsPurchaseRequestProductOffers();
  final product_transfer_status =
      const _LabelsApprovalsPurchaseRequestProductTransferStatus();
  final qty_changed__named = 'approvals.purchase_request.qty_changed__named';
  final request_approval_button_label =
      'approvals.purchase_request.request_approval_button_label';
  final request_authorization =
      'approvals.purchase_request.request_authorization';
  final requested_at = 'approvals.purchase_request.requested_at';
  final requested_by = 'approvals.purchase_request.requested_by';
  final reset_alert = const _LabelsApprovalsPurchaseRequestResetAlert();
  final reset_button_label = 'approvals.purchase_request.reset_button_label';
  final resetted_successfully =
      'approvals.purchase_request.resetted_successfully';
  final send_after_approval_warning =
      'approvals.purchase_request.send_after_approval_warning';
  final send_directly_to_supplier =
      const _LabelsApprovalsPurchaseRequestSendDirectlyToSupplier();
  final show_changes_button_label =
      'approvals.purchase_request.show_changes_button_label';
  final status = 'approvals.purchase_request.status';
  final status_approved = 'approvals.purchase_request.status_approved';
  final status_declined = 'approvals.purchase_request.status_declined';
  final status_in_progress = 'approvals.purchase_request.status_in_progress';
  final status_resetted = 'approvals.purchase_request.status_resetted';
  final supplier_changed = 'approvals.purchase_request.supplier_changed';
  final total = 'approvals.purchase_request.total';
  final type = 'approvals.purchase_request.type';
  final update = 'approvals.purchase_request.update';
  final update_button_label = 'approvals.purchase_request.update_button_label';
}

class _LabelsApprovalsPurchaseRequestApprovalTrail {
  const _LabelsApprovalsPurchaseRequestApprovalTrail();

  final no_record = 'approvals.purchase_request.approval_trail.no_record';
}

class _LabelsApprovalsPurchaseRequestChat {
  const _LabelsApprovalsPurchaseRequestChat();

  final message_placeholder =
      'approvals.purchase_request.chat.message_placeholder';
  final no_messages = 'approvals.purchase_request.chat.no_messages';
}

class _LabelsApprovalsPurchaseRequestDetails {
  const _LabelsApprovalsPurchaseRequestDetails();

  final title = 'approvals.purchase_request.details.title';
}

class _LabelsApprovalsPurchaseRequestDocuments {
  const _LabelsApprovalsPurchaseRequestDocuments();

  final date = 'approvals.purchase_request.documents.date';
  final name = 'approvals.purchase_request.documents.name';
  final no_documents = 'approvals.purchase_request.documents.no_documents';
  final user = 'approvals.purchase_request.documents.user';
}

class _LabelsApprovalsPurchaseRequestItem {
  const _LabelsApprovalsPurchaseRequestItem();

  final change_quantity_title =
      'approvals.purchase_request.item.change_quantity_title';
  final changing_quantity_not_allowed =
      'approvals.purchase_request.item.changing_quantity_not_allowed';
  final cost_type_updated_successfully =
      'approvals.purchase_request.item.cost_type_updated_successfully';
  final delivery_time = const _LabelsApprovalsPurchaseRequestItemDeliveryTime();
  final delivery_time_label =
      'approvals.purchase_request.item.delivery_time_label';
  final disable_button_label =
      'approvals.purchase_request.item.disable_button_label';
  final edit_quantity_button_label =
      'approvals.purchase_request.item.edit_quantity_button_label';
  final enable_button_label =
      'approvals.purchase_request.item.enable_button_label';
  final last_order_date = 'approvals.purchase_request.item.last_order_date';
  final last_order_price = 'approvals.purchase_request.item.last_order_price';
  final last_order_qty = 'approvals.purchase_request.item.last_order_qty';
  final qty_updated_successfully =
      'approvals.purchase_request.item.qty_updated_successfully';
  final quantity_field_label =
      'approvals.purchase_request.item.quantity_field_label';
  final quantity_in_approval =
      'approvals.purchase_request.item.quantity_in_approval';
  final quantity_update_label =
      'approvals.purchase_request.item.quantity_update_label';
  final status_updated_successfully =
      'approvals.purchase_request.item.status_updated_successfully';
}

class _LabelsApprovalsPurchaseRequestItemDeliveryTime {
  const _LabelsApprovalsPurchaseRequestItemDeliveryTime();

  final days = 'approvals.purchase_request.item.delivery_time.days';
  final directly_available =
      'approvals.purchase_request.item.delivery_time.directly_available';
}

class _LabelsApprovalsPurchaseRequestLog {
  const _LabelsApprovalsPurchaseRequestLog();

  final date = 'approvals.purchase_request.log.date';
  final message = 'approvals.purchase_request.log.message';
  final name = 'approvals.purchase_request.log.name';
  final no_record = 'approvals.purchase_request.log.no_record';
  final user = 'approvals.purchase_request.log.user';
}

class _LabelsApprovalsPurchaseRequestMoveToCartAlert {
  const _LabelsApprovalsPurchaseRequestMoveToCartAlert();

  final content = 'approvals.purchase_request.move_to_cart_alert.content';
  final no_action = 'approvals.purchase_request.move_to_cart_alert.no_action';
  final yes_action = 'approvals.purchase_request.move_to_cart_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestOrdersSendingResult {
  const _LabelsApprovalsPurchaseRequestOrdersSendingResult();

  final order_status =
      'approvals.purchase_request.orders_sending_result.order_status';
  final order_value =
      'approvals.purchase_request.orders_sending_result.order_value';
  final sending_status =
      const _LabelsApprovalsPurchaseRequestOrdersSendingResultSendingStatus();
  final status = 'approvals.purchase_request.orders_sending_result.status';
  final supplier = 'approvals.purchase_request.orders_sending_result.supplier';
  final title = 'approvals.purchase_request.orders_sending_result.title';
}

class _LabelsApprovalsPurchaseRequestOrdersSendingResultSendingStatus {
  const _LabelsApprovalsPurchaseRequestOrdersSendingResultSendingStatus();

  final not_sent =
      'approvals.purchase_request.orders_sending_result.sending_status.not_sent';
  final sent_successful_label =
      'approvals.purchase_request.orders_sending_result.sending_status.sent_successful_label';
}

class _LabelsApprovalsPurchaseRequestProductOffers {
  const _LabelsApprovalsPurchaseRequestProductOffers();

  final changes_available_only_online =
      'approvals.purchase_request.product_offers.changes_available_only_online';
  final offer = const _LabelsApprovalsPurchaseRequestProductOffersOffer();
  final product_change_alert =
      const _LabelsApprovalsPurchaseRequestProductOffersProductChangeAlert();
  final product_changed_successfully =
      'approvals.purchase_request.product_offers.product_changed_successfully';
  final select_offer_button_label =
      'approvals.purchase_request.product_offers.select_offer_button_label';
  final title = 'approvals.purchase_request.product_offers.title';
}

class _LabelsApprovalsPurchaseRequestProductOffersOffer {
  const _LabelsApprovalsPurchaseRequestProductOffersOffer();

  final cannot_be_used =
      'approvals.purchase_request.product_offers.offer.cannot_be_used';
  final not_available =
      'approvals.purchase_request.product_offers.offer.not_available';
  final packing_info =
      'approvals.purchase_request.product_offers.offer.packing_info';
  final price_per_unit =
      'approvals.purchase_request.product_offers.offer.price_per_unit';
}

class _LabelsApprovalsPurchaseRequestProductOffersProductChangeAlert {
  const _LabelsApprovalsPurchaseRequestProductOffersProductChangeAlert();

  final content =
      'approvals.purchase_request.product_offers.product_change_alert.content';
  final no_action =
      'approvals.purchase_request.product_offers.product_change_alert.no_action';
  final yes_action =
      'approvals.purchase_request.product_offers.product_change_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestProductTransferStatus {
  const _LabelsApprovalsPurchaseRequestProductTransferStatus();

  final not_enabled_for_supplier =
      'approvals.purchase_request.product_transfer_status.not_enabled_for_supplier';
  final order_contains_consolidated_basket_items =
      'approvals.purchase_request.product_transfer_status.order_contains_consolidated_basket_items';
  final order_error =
      'approvals.purchase_request.product_transfer_status.order_error';
  final order_sent_to_supplier =
      'approvals.purchase_request.product_transfer_status.order_sent_to_supplier';
  final unknown = 'approvals.purchase_request.product_transfer_status.unknown';
}

class _LabelsApprovalsPurchaseRequestResetAlert {
  const _LabelsApprovalsPurchaseRequestResetAlert();

  final content = 'approvals.purchase_request.reset_alert.content';
  final no_action = 'approvals.purchase_request.reset_alert.no_action';
  final yes_action = 'approvals.purchase_request.reset_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestSendDirectlyToSupplier {
  const _LabelsApprovalsPurchaseRequestSendDirectlyToSupplier();

  final alert =
      const _LabelsApprovalsPurchaseRequestSendDirectlyToSupplierAlert();
  final button_label =
      'approvals.purchase_request.send_directly_to_supplier.button_label';
  final open_log_button_label =
      'approvals.purchase_request.send_directly_to_supplier.open_log_button_label';
  final sending_error_label =
      'approvals.purchase_request.send_directly_to_supplier.sending_error_label';
  final sent_successfully_label =
      'approvals.purchase_request.send_directly_to_supplier.sent_successfully_label';
}

class _LabelsApprovalsPurchaseRequestSendDirectlyToSupplierAlert {
  const _LabelsApprovalsPurchaseRequestSendDirectlyToSupplierAlert();

  final content =
      'approvals.purchase_request.send_directly_to_supplier.alert.content';
  final no_action =
      'approvals.purchase_request.send_directly_to_supplier.alert.no_action';
  final yes_action =
      'approvals.purchase_request.send_directly_to_supplier.alert.yes_action';
}

class _LabelsApprovalsPurchaseRequests {
  const _LabelsApprovalsPurchaseRequests();

  final approval_request_alert =
      const _LabelsApprovalsPurchaseRequestsApprovalRequestAlert();
  final approval_requested_successfully =
      'approvals.purchase_requests.approval_requested_successfully';
  final approved = const _LabelsApprovalsPurchaseRequestsApproved();
  final filter = const _LabelsApprovalsPurchaseRequestsFilter();
  final item = const _LabelsApprovalsPurchaseRequestsItem();
  final move_to_cart_alert =
      const _LabelsApprovalsPurchaseRequestsMoveToCartAlert();
  final purchase_request_delete_alert =
      const _LabelsApprovalsPurchaseRequestsPurchaseRequestDeleteAlert();
  final purchase_request_moved_to_cart_successfully =
      'approvals.purchase_requests.purchase_request_moved_to_cart_successfully';
  final purchase_request_update_alert =
      const _LabelsApprovalsPurchaseRequestsPurchaseRequestUpdateAlert();
  final purchase_request_updated_successfully =
      'approvals.purchase_requests.purchase_request_updated_successfully';
  final requestor_lookup =
      const _LabelsApprovalsPurchaseRequestsRequestorLookup();
  final reset_alert = const _LabelsApprovalsPurchaseRequestsResetAlert();
  final search = const _LabelsApprovalsPurchaseRequestsSearch();
  final search_available_only_online =
      'approvals.purchase_requests.search_available_only_online';
  final sort = const _LabelsApprovalsPurchaseRequestsSort();
}

class _LabelsApprovalsPurchaseRequestsApprovalRequestAlert {
  const _LabelsApprovalsPurchaseRequestsApprovalRequestAlert();

  final content = 'approvals.purchase_requests.approval_request_alert.content';
  final no_action =
      'approvals.purchase_requests.approval_request_alert.no_action';
  final yes_action =
      'approvals.purchase_requests.approval_request_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestsApproved {
  const _LabelsApprovalsPurchaseRequestsApproved();

  final title = 'approvals.purchase_requests.approved.title';
}

class _LabelsApprovalsPurchaseRequestsFilter {
  const _LabelsApprovalsPurchaseRequestsFilter();

  final approved = 'approvals.purchase_requests.filter.approved';
  final assigned_to_me = 'approvals.purchase_requests.filter.assigned_to_me';
  final category = const _LabelsApprovalsPurchaseRequestsFilterCategory();
  final closed = 'approvals.purchase_requests.filter.closed';
  final cost_center = const _LabelsApprovalsPurchaseRequestsFilterCostCenter();
  final description = const _LabelsApprovalsPurchaseRequestsFilterDescription();
  final in_progress = 'approvals.purchase_requests.filter.in_progress';
  final open = 'approvals.purchase_requests.filter.open';
  final requestor = const _LabelsApprovalsPurchaseRequestsFilterRequestor();
  final status = const _LabelsApprovalsPurchaseRequestsFilterStatus();
  final status_label = 'approvals.purchase_requests.filter.status_label';
  final supplier = const _LabelsApprovalsPurchaseRequestsFilterSupplier();
}

class _LabelsApprovalsPurchaseRequestsFilterCategory {
  const _LabelsApprovalsPurchaseRequestsFilterCategory();

  final hint_text = 'approvals.purchase_requests.filter.category.hint_text';
  final label = 'approvals.purchase_requests.filter.category.label';
}

class _LabelsApprovalsPurchaseRequestsFilterCostCenter {
  const _LabelsApprovalsPurchaseRequestsFilterCostCenter();

  final hint_text = 'approvals.purchase_requests.filter.cost_center.hint_text';
  final label = 'approvals.purchase_requests.filter.cost_center.label';
}

class _LabelsApprovalsPurchaseRequestsFilterDescription {
  const _LabelsApprovalsPurchaseRequestsFilterDescription();

  final hint_text = 'approvals.purchase_requests.filter.description.hint_text';
  final label = 'approvals.purchase_requests.filter.description.label';
}

class _LabelsApprovalsPurchaseRequestsFilterRequestor {
  const _LabelsApprovalsPurchaseRequestsFilterRequestor();

  final hint_text = 'approvals.purchase_requests.filter.requestor.hint_text';
  final label = 'approvals.purchase_requests.filter.requestor.label';
}

class _LabelsApprovalsPurchaseRequestsFilterStatus {
  const _LabelsApprovalsPurchaseRequestsFilterStatus();

  final approved = 'approvals.purchase_requests.filter.status.approved';
  final assigned_to_me =
      'approvals.purchase_requests.filter.status.assigned_to_me';
  final assigned_to_my_group =
      'approvals.purchase_requests.filter.status.assigned_to_my_group';
  final closed = 'approvals.purchase_requests.filter.status.closed';
  final closed_approved =
      'approvals.purchase_requests.filter.status.closed_approved';
  final closed_declined =
      'approvals.purchase_requests.filter.status.closed_declined';
  final date_range = 'approvals.purchase_requests.filter.status.date_range';
  final open = 'approvals.purchase_requests.filter.status.open';
}

class _LabelsApprovalsPurchaseRequestsFilterSupplier {
  const _LabelsApprovalsPurchaseRequestsFilterSupplier();

  final hint_text = 'approvals.purchase_requests.filter.supplier.hint_text';
  final label = 'approvals.purchase_requests.filter.supplier.label';
}

class _LabelsApprovalsPurchaseRequestsItem {
  const _LabelsApprovalsPurchaseRequestsItem();

  final attachments_title =
      'approvals.purchase_requests.item.attachments_title';
  final cost_type = 'approvals.purchase_requests.item.cost_type';
  final deleted_message = 'approvals.purchase_requests.item.deleted_message';
  final delivery_date = 'approvals.purchase_requests.item.delivery_date';
  final edit = const _LabelsApprovalsPurchaseRequestsItemEdit();
  final log = const _LabelsApprovalsPurchaseRequestsItemLog();
  final log_button_label = 'approvals.purchase_requests.item.log_button_label';
  final log_title = 'approvals.purchase_requests.item.log_title';
  final max_stock = 'approvals.purchase_requests.item.max_stock';
  final min_stock = 'approvals.purchase_requests.item.min_stock';
  final open_order_action =
      'approvals.purchase_requests.item.open_order_action';
  final read_only_budget = 'approvals.purchase_requests.item.read_only_budget';
  final stock_on_hand = 'approvals.purchase_requests.item.stock_on_hand';
}

class _LabelsApprovalsPurchaseRequestsItemEdit {
  const _LabelsApprovalsPurchaseRequestsItemEdit();

  final article_will_be_duplicated =
      'approvals.purchase_requests.item.edit.article_will_be_duplicated';
  final change_supplier =
      const _LabelsApprovalsPurchaseRequestsItemEditChangeSupplier();
  final change_supplier_title =
      'approvals.purchase_requests.item.edit.change_supplier_title';
  final check_network_connection =
      'approvals.purchase_requests.item.edit.check_network_connection';
  final create_offer = 'approvals.purchase_requests.item.edit.create_offer';
  final create_title = 'approvals.purchase_requests.item.edit.create_title';
  final duplicate = 'approvals.purchase_requests.item.edit.duplicate';
  final edit_title = 'approvals.purchase_requests.item.edit.edit_title';
  final field = const _LabelsApprovalsPurchaseRequestsItemEditField();
  final new_offer_from_supplier =
      'approvals.purchase_requests.item.edit.new_offer_from_supplier';
  final offer_created_successfully =
      'approvals.purchase_requests.item.edit.offer_created_successfully';
  final offer_duplicate_successfully =
      'approvals.purchase_requests.item.edit.offer_duplicate_successfully';
  final offer_replaced_successfully =
      'approvals.purchase_requests.item.edit.offer_replaced_successfully';
  final offer_saved_successfully =
      'approvals.purchase_requests.item.edit.offer_saved_successfully';
  final save = 'approvals.purchase_requests.item.edit.save';
  final suppliers_title =
      'approvals.purchase_requests.item.edit.suppliers_title';
}

class _LabelsApprovalsPurchaseRequestsItemEditChangeSupplier {
  const _LabelsApprovalsPurchaseRequestsItemEditChangeSupplier();

  final cant_find_offer =
      'approvals.purchase_requests.item.edit.change_supplier.cant_find_offer';
  final change_button =
      'approvals.purchase_requests.item.edit.change_supplier.change_button';
  final create_offer_button =
      'approvals.purchase_requests.item.edit.change_supplier.create_offer_button';
}

class _LabelsApprovalsPurchaseRequestsItemEditField {
  const _LabelsApprovalsPurchaseRequestsItemEditField();

  final article_no = 'approvals.purchase_requests.item.edit.field.article_no';
  final content_unit =
      'approvals.purchase_requests.item.edit.field.content_unit';
  final content_units_per_order_unit =
      'approvals.purchase_requests.item.edit.field.content_units_per_order_unit';
  final currency = 'approvals.purchase_requests.item.edit.field.currency';
  final gtin = 'approvals.purchase_requests.item.edit.field.gtin';
  final inventory_unit_factor =
      'approvals.purchase_requests.item.edit.field.inventory_unit_factor';
  final item_price = 'approvals.purchase_requests.item.edit.field.item_price';
  final name = 'approvals.purchase_requests.item.edit.field.name';
  final order_unit = 'approvals.purchase_requests.item.edit.field.order_unit';
  final order_unit_price =
      'approvals.purchase_requests.item.edit.field.order_unit_price';
  final supplier = 'approvals.purchase_requests.item.edit.field.supplier';
  final tax_rate = 'approvals.purchase_requests.item.edit.field.tax_rate';
}

class _LabelsApprovalsPurchaseRequestsItemLog {
  const _LabelsApprovalsPurchaseRequestsItemLog();

  final new_data = 'approvals.purchase_requests.item.log.new_data';
  final no_data = 'approvals.purchase_requests.item.log.no_data';
  final previous_data = 'approvals.purchase_requests.item.log.previous_data';
}

class _LabelsApprovalsPurchaseRequestsMoveToCartAlert {
  const _LabelsApprovalsPurchaseRequestsMoveToCartAlert();

  final content = 'approvals.purchase_requests.move_to_cart_alert.content';
  final no_action = 'approvals.purchase_requests.move_to_cart_alert.no_action';
  final yes_action =
      'approvals.purchase_requests.move_to_cart_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestsPurchaseRequestDeleteAlert {
  const _LabelsApprovalsPurchaseRequestsPurchaseRequestDeleteAlert();

  final content =
      'approvals.purchase_requests.purchase_request_delete_alert.content';
  final no_action =
      'approvals.purchase_requests.purchase_request_delete_alert.no_action';
  final yes_action =
      'approvals.purchase_requests.purchase_request_delete_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestsPurchaseRequestUpdateAlert {
  const _LabelsApprovalsPurchaseRequestsPurchaseRequestUpdateAlert();

  final content =
      'approvals.purchase_requests.purchase_request_update_alert.content';
  final no_action =
      'approvals.purchase_requests.purchase_request_update_alert.no_action';
  final yes_action =
      'approvals.purchase_requests.purchase_request_update_alert.yes_action';
}

class _LabelsApprovalsPurchaseRequestsRequestorLookup {
  const _LabelsApprovalsPurchaseRequestsRequestorLookup();

  final title = 'approvals.purchase_requests.requestor_lookup.title';
}

class _LabelsApprovalsPurchaseRequestsResetAlert {
  const _LabelsApprovalsPurchaseRequestsResetAlert();

  final content = 'approvals.purchase_requests.reset_alert.content';
}

class _LabelsApprovalsPurchaseRequestsSearch {
  const _LabelsApprovalsPurchaseRequestsSearch();

  final date_from = 'approvals.purchase_requests.search.date_from';
  final date_to = 'approvals.purchase_requests.search.date_to';
  final placeholder = 'approvals.purchase_requests.search.placeholder';
}

class _LabelsApprovalsPurchaseRequestsSort {
  const _LabelsApprovalsPurchaseRequestsSort();

  final by_added_to_cart_at_asc =
      'approvals.purchase_requests.sort.by_added_to_cart_at_asc';
  final by_added_to_cart_at_desc =
      'approvals.purchase_requests.sort.by_added_to_cart_at_desc';
  final by_approved_at_asc =
      'approvals.purchase_requests.sort.by_approved_at_asc';
  final by_approved_at_desc =
      'approvals.purchase_requests.sort.by_approved_at_desc';
  final by_closed_at_asc = 'approvals.purchase_requests.sort.by_closed_at_asc';
  final by_closed_at_desc =
      'approvals.purchase_requests.sort.by_closed_at_desc';
  final by_declined_at_asc =
      'approvals.purchase_requests.sort.by_declined_at_asc';
  final by_declined_at_desc =
      'approvals.purchase_requests.sort.by_declined_at_desc';
  final by_requested_at_asc =
      'approvals.purchase_requests.sort.by_requested_at_asc';
  final by_requested_at_desc =
      'approvals.purchase_requests.sort.by_requested_at_desc';
  final by_total_asc = 'approvals.purchase_requests.sort.by_total_asc';
  final by_total_desc = 'approvals.purchase_requests.sort.by_total_desc';
  final by_type_asc = 'approvals.purchase_requests.sort.by_type_asc';
  final by_type_desc = 'approvals.purchase_requests.sort.by_type_desc';
}

class _LabelsApprovalsReceivingRequest {
  const _LabelsApprovalsReceivingRequest();

  final approver = 'approvals.receiving_request.approver';
  final available_only_online =
      'approvals.receiving_request.available_only_online';
  final comment = 'approvals.receiving_request.comment';
  final details = const _LabelsApprovalsReceivingRequestDetails();
  final products = const _LabelsApprovalsReceivingRequestProducts();
  final total = 'approvals.receiving_request.total';
}

class _LabelsApprovalsReceivingRequestDetails {
  const _LabelsApprovalsReceivingRequestDetails();

  final approval_user = 'approvals.receiving_request.details.approval_user';
  final order_id = 'approvals.receiving_request.details.order_id';
  final requested_at = 'approvals.receiving_request.details.requested_at';
  final requested_by = 'approvals.receiving_request.details.requested_by';
  final status = const _LabelsApprovalsReceivingRequestDetailsStatus();
  final status_label = 'approvals.receiving_request.details.status_label';
  final title = 'approvals.receiving_request.details.title';
  final total = 'approvals.receiving_request.details.total';
  final type = 'approvals.receiving_request.details.type';
}

class _LabelsApprovalsReceivingRequestDetailsStatus {
  const _LabelsApprovalsReceivingRequestDetailsStatus();

  final approved = 'approvals.receiving_request.details.status.approved';
  final declined = 'approvals.receiving_request.details.status.declined';
  final in_progress = 'approvals.receiving_request.details.status.in_progress';
  final not_assigned =
      'approvals.receiving_request.details.status.not_assigned';
}

class _LabelsApprovalsReceivingRequestProducts {
  const _LabelsApprovalsReceivingRequestProducts();

  final list_only_online =
      'approvals.receiving_request.products.list_only_online';
}

class _LabelsApprovalsReceivingRequests {
  const _LabelsApprovalsReceivingRequests();

  final approval_request_alert =
      const _LabelsApprovalsReceivingRequestsApprovalRequestAlert();
  final approval_requested_successfully =
      'approvals.receiving_requests.approval_requested_successfully';
  final approve_button_label =
      'approvals.receiving_requests.approve_button_label';
  final booking_approval_request_update_alert =
      const _LabelsApprovalsReceivingRequestsBookingApprovalRequestUpdateAlert();
  final decline_button_label =
      'approvals.receiving_requests.decline_button_label';
  final filter = const _LabelsApprovalsReceivingRequestsFilter();
  final product = const _LabelsApprovalsReceivingRequestsProduct();
  final products = const _LabelsApprovalsReceivingRequestsProducts();
  final receiving_approval_request_approve_alert =
      const _LabelsApprovalsReceivingRequestsReceivingApprovalRequestApproveAlert();
  final receiving_approval_request_decline_alert =
      const _LabelsApprovalsReceivingRequestsReceivingApprovalRequestDeclineAlert();
  final receiving_approval_request_update_alert =
      const _LabelsApprovalsReceivingRequestsReceivingApprovalRequestUpdateAlert();
  final receiving_approval_request_updated_successfully =
      'approvals.receiving_requests.receiving_approval_request_updated_successfully';
  final request_approval_button_label =
      'approvals.receiving_requests.request_approval_button_label';
  final reset_alert = const _LabelsApprovalsReceivingRequestsResetAlert();
  final search_available_only_online =
      'approvals.receiving_requests.search_available_only_online';
  final sort = const _LabelsApprovalsReceivingRequestsSort();
  final update_button_label =
      'approvals.receiving_requests.update_button_label';
}

class _LabelsApprovalsReceivingRequestsApprovalRequestAlert {
  const _LabelsApprovalsReceivingRequestsApprovalRequestAlert();

  final content = 'approvals.receiving_requests.approval_request_alert.content';
  final no_action =
      'approvals.receiving_requests.approval_request_alert.no_action';
  final yes_action =
      'approvals.receiving_requests.approval_request_alert.yes_action';
}

class _LabelsApprovalsReceivingRequestsBookingApprovalRequestUpdateAlert {
  const _LabelsApprovalsReceivingRequestsBookingApprovalRequestUpdateAlert();

  final content =
      'approvals.receiving_requests.booking_approval_request_update_alert.content';
  final no_action =
      'approvals.receiving_requests.booking_approval_request_update_alert.no_action';
  final yes_action =
      'approvals.receiving_requests.booking_approval_request_update_alert.yes_action';
}

class _LabelsApprovalsReceivingRequestsFilter {
  const _LabelsApprovalsReceivingRequestsFilter();

  final status = const _LabelsApprovalsReceivingRequestsFilterStatus();
  final status_label = 'approvals.receiving_requests.filter.status_label';
}

class _LabelsApprovalsReceivingRequestsFilterStatus {
  const _LabelsApprovalsReceivingRequestsFilterStatus();

  final any = 'approvals.receiving_requests.filter.status.any';
  final approved = 'approvals.receiving_requests.filter.status.approved';
  final assigned_to_me =
      'approvals.receiving_requests.filter.status.assigned_to_me';
  final closed = 'approvals.receiving_requests.filter.status.closed';
  final date_range = 'approvals.receiving_requests.filter.status.date_range';
  final in_progress = 'approvals.receiving_requests.filter.status.in_progress';
  final not_assigned =
      'approvals.receiving_requests.filter.status.not_assigned';
}

class _LabelsApprovalsReceivingRequestsProduct {
  const _LabelsApprovalsReceivingRequestsProduct();

  final add_comment =
      const _LabelsApprovalsReceivingRequestsProductAddComment();
  final comment_button_label =
      'approvals.receiving_requests.product.comment_button_label';
  final comment_label = 'approvals.receiving_requests.product.comment_label';
  final delivery_date = 'approvals.receiving_requests.product.delivery_date';
  final iu_delivered = 'approvals.receiving_requests.product.iu_delivered';
  final iu_ordered = 'approvals.receiving_requests.product.iu_ordered';
  final iu_price_delivered =
      'approvals.receiving_requests.product.iu_price_delivered';
  final iu_price_ordered =
      'approvals.receiving_requests.product.iu_price_ordered';
  final quantity_variance =
      'approvals.receiving_requests.product.quantity_variance';
  final total_amount_delivered =
      'approvals.receiving_requests.product.total_amount_delivered';
  final total_amount_ordered =
      'approvals.receiving_requests.product.total_amount_ordered';
  final total_variance = 'approvals.receiving_requests.product.total_variance';
  final value_variance = 'approvals.receiving_requests.product.value_variance';
}

class _LabelsApprovalsReceivingRequestsProductAddComment {
  const _LabelsApprovalsReceivingRequestsProductAddComment();

  final action_label =
      'approvals.receiving_requests.product.add_comment.action_label';
  final field_label =
      'approvals.receiving_requests.product.add_comment.field_label';
  final title = 'approvals.receiving_requests.product.add_comment.title';
}

class _LabelsApprovalsReceivingRequestsProducts {
  const _LabelsApprovalsReceivingRequestsProducts();

  final product_comment_updated_successfully =
      'approvals.receiving_requests.products.product_comment_updated_successfully';
}

class _LabelsApprovalsReceivingRequestsReceivingApprovalRequestApproveAlert {
  const _LabelsApprovalsReceivingRequestsReceivingApprovalRequestApproveAlert();

  final content =
      'approvals.receiving_requests.receiving_approval_request_approve_alert.content';
}

class _LabelsApprovalsReceivingRequestsReceivingApprovalRequestDeclineAlert {
  const _LabelsApprovalsReceivingRequestsReceivingApprovalRequestDeclineAlert();

  final content =
      'approvals.receiving_requests.receiving_approval_request_decline_alert.content';
}

class _LabelsApprovalsReceivingRequestsReceivingApprovalRequestUpdateAlert {
  const _LabelsApprovalsReceivingRequestsReceivingApprovalRequestUpdateAlert();

  final no_action =
      'approvals.receiving_requests.receiving_approval_request_update_alert.no_action';
  final yes_action =
      'approvals.receiving_requests.receiving_approval_request_update_alert.yes_action';
}

class _LabelsApprovalsReceivingRequestsResetAlert {
  const _LabelsApprovalsReceivingRequestsResetAlert();

  final content = 'approvals.receiving_requests.reset_alert.content';
}

class _LabelsApprovalsReceivingRequestsSort {
  const _LabelsApprovalsReceivingRequestsSort();

  final by_approved_at_asc =
      'approvals.receiving_requests.sort.by_approved_at_asc';
  final by_approved_at_desc =
      'approvals.receiving_requests.sort.by_approved_at_desc';
  final by_closed_at_asc = 'approvals.receiving_requests.sort.by_closed_at_asc';
  final by_closed_at_desc =
      'approvals.receiving_requests.sort.by_closed_at_desc';
  final by_declined_at_asc =
      'approvals.receiving_requests.sort.by_declined_at_asc';
  final by_declined_at_desc =
      'approvals.receiving_requests.sort.by_declined_at_desc';
  final by_requested_at_asc =
      'approvals.receiving_requests.sort.by_requested_at_asc';
  final by_requested_at_desc =
      'approvals.receiving_requests.sort.by_requested_at_desc';
  final by_total_amount_asc =
      'approvals.receiving_requests.sort.by_total_amount_asc';
  final by_total_amount_desc =
      'approvals.receiving_requests.sort.by_total_amount_desc';
}

class _LabelsApprovalsRequest {
  const _LabelsApprovalsRequest();

  final approval_trail_menu_item = 'approvals.request.approval_trail_menu_item';
  final attachments_document_count =
      'approvals.request.attachments_document_count';
  final attachments_documents_count =
      'approvals.request.attachments_documents_count';
  final attachments_label = 'approvals.request.attachments_label';
  final documents = const _LabelsApprovalsRequestDocuments();
  final log_menu_item = 'approvals.request.log_menu_item';
  final message_sent = 'approvals.request.message_sent';
  final messages = const _LabelsApprovalsRequestMessages();
  final old_documents_alert = 'approvals.request.old_documents_alert';
}

class _LabelsApprovalsRequestDocuments {
  const _LabelsApprovalsRequestDocuments();

  final can_be_loaded_online =
      'approvals.request.documents.can_be_loaded_online';
}

class _LabelsApprovalsRequestMessages {
  const _LabelsApprovalsRequestMessages();

  final can_be_loaded_online =
      'approvals.request.messages.can_be_loaded_online';
  final empty = 'approvals.request.messages.empty';
}

class _LabelsApprovalsRequests {
  const _LabelsApprovalsRequests();

  final chat = const _LabelsApprovalsRequestsChat();
  final documents = const _LabelsApprovalsRequestsDocuments();
  final reset_alert = const _LabelsApprovalsRequestsResetAlert();
  final resetted_successfully = 'approvals.requests.resetted_successfully';
}

class _LabelsApprovalsRequestsChat {
  const _LabelsApprovalsRequestsChat();

  final no_messages = 'approvals.requests.chat.no_messages';
}

class _LabelsApprovalsRequestsDocuments {
  const _LabelsApprovalsRequestsDocuments();

  final approval_trail_only_online =
      'approvals.requests.documents.approval_trail_only_online';
}

class _LabelsApprovalsRequestsResetAlert {
  const _LabelsApprovalsRequestsResetAlert();

  final no_action = 'approvals.requests.reset_alert.no_action';
  final yes_action = 'approvals.requests.reset_alert.yes_action';
}

class _LabelsAttachments {
  const _LabelsAttachments();

  final action = const _LabelsAttachmentsAction();
  final attach_file = 'attachments.attach_file';
  final default_file_name = 'attachments.default_file_name';
  final delete_confirmation_content = 'attachments.delete_confirmation_content';
  final delete_confirmation_no_label =
      'attachments.delete_confirmation_no_label';
  final delete_confirmation_yes_label =
      'attachments.delete_confirmation_yes_label';
  final delete_success = 'attachments.delete_success';
  final edit_name_confirmation_label =
      'attachments.edit_name_confirmation_label';
  final edit_name_field_label = 'attachments.edit_name_field_label';
  final empty = 'attachments.empty';
  final maximum_file_size_is_10mb = 'attachments.maximum_file_size_is_10mb';
  final rename_cancel_label = 'attachments.rename_cancel_label';
  final rename_confirmation_label = 'attachments.rename_confirmation_label';
  final rename_field_label = 'attachments.rename_field_label';
  final rename_success = 'attachments.rename_success';
  final source = const _LabelsAttachmentsSource();
  final take_a_photo = 'attachments.take_a_photo';
  final too_many_files = 'attachments.too_many_files';
  final upload_confirmation_label = 'attachments.upload_confirmation_label';
  final upload_success = 'attachments.upload_success';
}

class _LabelsAttachmentsAction {
  const _LabelsAttachmentsAction();

  final delete = 'attachments.action.delete';
  final rename = 'attachments.action.rename';
  final view = 'attachments.action.view';
}

class _LabelsAttachmentsSource {
  const _LabelsAttachmentsSource();

  final invoice = 'attachments.source.invoice';
  final other = 'attachments.source.other';
  final purchase_request = 'attachments.source.purchase_request';
  final receiving = 'attachments.source.receiving';
}

class _LabelsBluetooth {
  const _LabelsBluetooth();

  final give_bluetooth_permission_on_the_device =
      'bluetooth.give_bluetooth_permission_on_the_device';
  final give_location_permission_on_the_device =
      'bluetooth.give_location_permission_on_the_device';
  final grant_permissions_button_label =
      'bluetooth.grant_permissions_button_label';
  final location_service_disabled_description =
      'bluetooth.location_service_disabled_description';
  final location_service_disabled_title =
      'bluetooth.location_service_disabled_title';
  final no_permission_description = 'bluetooth.no_permission_description';
  final no_permission_title = 'bluetooth.no_permission_title';
  final turned_off_description = 'bluetooth.turned_off_description';
  final turned_off_title = 'bluetooth.turned_off_title';
  final unsupported_on_the_device = 'bluetooth.unsupported_on_the_device';
}

class _LabelsBluetoothDevices {
  const _LabelsBluetoothDevices();

  final add_button_label = 'bluetooth_devices.add_button_label';
  final connect_button_label = 'bluetooth_devices.connect_button_label';
  final device = const _LabelsBluetoothDevicesDevice();
  final device_type = const _LabelsBluetoothDevicesDeviceType();
  final lookup = const _LabelsBluetoothDevicesLookup();
  final no_devices_title = 'bluetooth_devices.no_devices_title';
}

class _LabelsBluetoothDevicesDevice {
  const _LabelsBluetoothDevicesDevice();

  final connect_button_label = 'bluetooth_devices.device.connect_button_label';
  final connected_successfully_label =
      'bluetooth_devices.device.connected_successfully_label';
  final connecting_to_device_label =
      'bluetooth_devices.device.connecting_to_device_label';
  final connection_label = 'bluetooth_devices.device.connection_label';
  final connection_successful_details =
      'bluetooth_devices.device.connection_successful_details';
  final connection_successful_greeting =
      'bluetooth_devices.device.connection_successful_greeting';
  final delete_button_label = 'bluetooth_devices.device.delete_button_label';
  final delete_confirmation_dialog =
      const _LabelsBluetoothDevicesDeviceDeleteConfirmationDialog();
  final deleted_successfully_label =
      'bluetooth_devices.device.deleted_successfully_label';
  final disconnect_button_label =
      'bluetooth_devices.device.disconnect_button_label';
  final disconnected_successfully_label =
      'bluetooth_devices.device.disconnected_successfully_label';
  final next_button_label = 'bluetooth_devices.device.next_button_label';
  final reconnect = const _LabelsBluetoothDevicesDeviceReconnect();
  final rename = const _LabelsBluetoothDevicesDeviceRename();
  final rename_button_label = 'bluetooth_devices.device.rename_button_label';
  final retry_connect_button_label =
      'bluetooth_devices.device.retry_connect_button_label';
  final retry_connection_button_label =
      'bluetooth_devices.device.retry_connection_button_label';
  final setup = const _LabelsBluetoothDevicesDeviceSetup();
}

class _LabelsBluetoothDevicesDeviceDeleteConfirmationDialog {
  const _LabelsBluetoothDevicesDeviceDeleteConfirmationDialog();

  final cancel_action =
      'bluetooth_devices.device.delete_confirmation_dialog.cancel_action';
  final confirm_action =
      'bluetooth_devices.device.delete_confirmation_dialog.confirm_action';
  final title = 'bluetooth_devices.device.delete_confirmation_dialog.title';
}

class _LabelsBluetoothDevicesDeviceReconnect {
  const _LabelsBluetoothDevicesDeviceReconnect();

  final header = 'bluetooth_devices.device.reconnect.header';
}

class _LabelsBluetoothDevicesDeviceRename {
  const _LabelsBluetoothDevicesDeviceRename();

  final header = 'bluetooth_devices.device.rename.header';
  final renamed_successfully_label =
      'bluetooth_devices.device.rename.renamed_successfully_label';
  final save_button_label = 'bluetooth_devices.device.rename.save_button_label';
}

class _LabelsBluetoothDevicesDeviceSetup {
  const _LabelsBluetoothDevicesDeviceSetup();

  final friendly_name_field_description =
      'bluetooth_devices.device.setup.friendly_name_field_description';
  final friendly_name_field_label =
      'bluetooth_devices.device.setup.friendly_name_field_label';
  final header = 'bluetooth_devices.device.setup.header';
  final measurement_verification =
      const _LabelsBluetoothDevicesDeviceSetupMeasurementVerification();
  final save_button_label = 'bluetooth_devices.device.setup.save_button_label';
}

class _LabelsBluetoothDevicesDeviceSetupMeasurementVerification {
  const _LabelsBluetoothDevicesDeviceSetupMeasurementVerification();

  final confirmation_button_label =
      'bluetooth_devices.device.setup.measurement_verification.confirmation_button_label';
  final first_step =
      'bluetooth_devices.device.setup.measurement_verification.first_step';
  final label = 'bluetooth_devices.device.setup.measurement_verification.label';
  final original_unit =
      'bluetooth_devices.device.setup.measurement_verification.original_unit';
  final second_step =
      'bluetooth_devices.device.setup.measurement_verification.second_step';
}

class _LabelsBluetoothDevicesDeviceType {
  const _LabelsBluetoothDevicesDeviceType();

  final not_compatible_dialog =
      const _LabelsBluetoothDevicesDeviceTypeNotCompatibleDialog();
  final not_compatible_label =
      'bluetooth_devices.device_type.not_compatible_label';
  final scale_label = 'bluetooth_devices.device_type.scale_label';
  final select_type_header = 'bluetooth_devices.device_type.select_type_header';
  final thermometer_label = 'bluetooth_devices.device_type.thermometer_label';
}

class _LabelsBluetoothDevicesDeviceTypeNotCompatibleDialog {
  const _LabelsBluetoothDevicesDeviceTypeNotCompatibleDialog();

  final close_action =
      'bluetooth_devices.device_type.not_compatible_dialog.close_action';
  final message = 'bluetooth_devices.device_type.not_compatible_dialog.message';
  final title = 'bluetooth_devices.device_type.not_compatible_dialog.title';
}

class _LabelsBluetoothDevicesLookup {
  const _LabelsBluetoothDevicesLookup();

  final title = 'bluetooth_devices.lookup.title';
}

class _LabelsBluetoothLookup {
  const _LabelsBluetoothLookup();

  final device_not_found_hint = 'bluetooth_lookup.device_not_found_hint';
  final no_devices_found_label = 'bluetooth_lookup.no_devices_found_label';
  final scan_again_button_label = 'bluetooth_lookup.scan_again_button_label';
  final scanning_error_hint = 'bluetooth_lookup.scanning_error_hint';
  final title = 'bluetooth_lookup.title';
}

class _LabelsBooking {
  const _LabelsBooking();

  final create_button_label = 'booking.create_button_label';
}

class _LabelsBookings {
  const _LabelsBookings();

  final approval_request = const _LabelsBookingsApprovalRequest();
  final book = const _LabelsBookingsBook();
  final booking = const _LabelsBookingsBooking();
  final booking_reason_lookup = const _LabelsBookingsBookingReasonLookup();
  final create = const _LabelsBookingsCreate();
  final filter = const _LabelsBookingsFilter();
  final manage = const _LabelsBookingsManage();
  final qty = 'bookings.qty';
  final reason_lookup = const _LabelsBookingsReasonLookup();
  final search = const _LabelsBookingsSearch();
  final search_available_only_online = 'bookings.search_available_only_online';
}

class _LabelsBookingsApprovalRequest {
  const _LabelsBookingsApprovalRequest();

  final approver = 'bookings.approval_request.approver';
  final no_approvers_found = 'bookings.approval_request.no_approvers_found';
  final title = 'bookings.approval_request.title';
}

class _LabelsBookingsBook {
  const _LabelsBookingsBook();

  final book_button_label = 'bookings.book.book_button_label';
  final booked_successfully_label = 'bookings.book.booked_successfully_label';
  final delivery_date_is_empty_error =
      'bookings.book.delivery_date_is_empty_error';
  final delivery_date_label = 'bookings.book.delivery_date_label';
  final note_is_empty_error = 'bookings.book.note_is_empty_error';
  final note_label = 'bookings.book.note_label';
  final title = 'bookings.book.title';
  final view_booking_log_button_label =
      'bookings.book.view_booking_log_button_label';
}

class _LabelsBookingsBooking {
  const _LabelsBookingsBooking();

  final book_alert = const _LabelsBookingsBookingBookAlert();
  final book_confirmation_dialog = 'bookings.booking.book_confirmation_dialog';
  final book_result = const _LabelsBookingsBookingBookResult();
  final booking_booked_successfully =
      'bookings.booking.booking_booked_successfully';
  final details = const _LabelsBookingsBookingDetails();
  final error = const _LabelsBookingsBookingError();
  final item = const _LabelsBookingsBookingItem();
  final status = const _LabelsBookingsBookingStatus();
  final view_order_pdf_button_label =
      'bookings.booking.view_order_pdf_button_label';
  final voucher_button_label = 'bookings.booking.voucher_button_label';
}

class _LabelsBookingsBookingBookAlert {
  const _LabelsBookingsBookingBookAlert();

  final content = 'bookings.booking.book_alert.content';
  final no_action = 'bookings.booking.book_alert.no_action';
  final yes_action = 'bookings.booking.book_alert.yes_action';
}

class _LabelsBookingsBookingBookResult {
  const _LabelsBookingsBookingBookResult();

  final booking_id = 'bookings.booking.book_result.booking_id';
  final error_status_label = 'bookings.booking.book_result.error_status_label';
  final hades_export_error_label =
      'bookings.booking.book_result.hades_export_error_label';
  final ok_status_label = 'bookings.booking.book_result.ok_status_label';
  final title = 'bookings.booking.book_result.title';
}

class _LabelsBookingsBookingDetails {
  const _LabelsBookingsBookingDetails();

  final booking_date = 'bookings.booking.details.booking_date';
  final booking_reason = 'bookings.booking.details.booking_reason';
  final booking_reason_is_not_specified =
      'bookings.booking.details.booking_reason_is_not_specified';
  final booking_updated_successfully =
      'bookings.booking.details.booking_updated_successfully';
  final button_label = 'bookings.booking.details.button_label';
  final id = 'bookings.booking.details.id';
  final recipe_transfer_label =
      'bookings.booking.details.recipe_transfer_label';
  final reference_edit = const _LabelsBookingsBookingDetailsReferenceEdit();
  final reference_label = 'bookings.booking.details.reference_label';
  final reference_title = 'bookings.booking.details.reference_title';
  final snack_bar_button_label =
      'bookings.booking.details.snack_bar_button_label';
  final source_store = 'bookings.booking.details.source_store';
  final target_store = 'bookings.booking.details.target_store';
  final title = 'bookings.booking.details.title';
  final transfer_type = 'bookings.booking.details.transfer_type';
}

class _LabelsBookingsBookingDetailsReferenceEdit {
  const _LabelsBookingsBookingDetailsReferenceEdit();

  final cancel_button_label =
      'bookings.booking.details.reference_edit.cancel_button_label';
  final update_button_label =
      'bookings.booking.details.reference_edit.update_button_label';
}

class _LabelsBookingsBookingError {
  const _LabelsBookingsBookingError();

  final booking_date_after_open_inventory =
      'bookings.booking.error.booking_date_after_open_inventory';
  final booking_date_before_closed_inventory =
      'bookings.booking.error.booking_date_before_closed_inventory';
  final booking_date_in_the_future =
      'bookings.booking.error.booking_date_in_the_future';
  final booking_date_is_not_valid =
      'bookings.booking.error.booking_date_is_not_valid';
  final booking_reason_is_missing =
      'bookings.booking.error.booking_reason_is_missing';
  final declined = 'bookings.booking.error.declined';
  final declined_by_target = 'bookings.booking.error.declined_by_target';
  final has_unconfirmed_records =
      'bookings.booking.error.has_unconfirmed_records';
  final not_approved = 'bookings.booking.error.not_approved';
  final not_approved_by_target =
      'bookings.booking.error.not_approved_by_target';
}

class _LabelsBookingsBookingItem {
  const _LabelsBookingsBookingItem();

  final can_not_be_confirmed = 'bookings.booking.item.can_not_be_confirmed';
  final can_not_be_updated = 'bookings.booking.item.can_not_be_updated';
  final details = const _LabelsBookingsBookingItemDetails();
  final errors = const _LabelsBookingsBookingItemErrors();
  final show_details_button_label =
      'bookings.booking.item.show_details_button_label';
}

class _LabelsBookingsBookingItemDetails {
  const _LabelsBookingsBookingItemDetails();

  final select_source_cost_center_first_label =
      'bookings.booking.item.details.select_source_cost_center_first_label';
  final select_target_cost_center_first_label =
      'bookings.booking.item.details.select_target_cost_center_first_label';
  final select_target_division_first_label =
      'bookings.booking.item.details.select_target_division_first_label';
}

class _LabelsBookingsBookingItemErrors {
  const _LabelsBookingsBookingItemErrors();

  final inventory_unit_is_not_set =
      'bookings.booking.item.errors.inventory_unit_is_not_set';
  final low_stock_qty = 'bookings.booking.item.errors.low_stock_qty';
  final price_is_not_set = 'bookings.booking.item.errors.price_is_not_set';
  final qty_is_not_set = 'bookings.booking.item.errors.qty_is_not_set';
  final src_cost_center_id_is_not_set =
      'bookings.booking.item.errors.src_cost_center_id_is_not_set';
  final src_cost_type_id_is_not_set =
      'bookings.booking.item.errors.src_cost_type_id_is_not_set';
  final src_store_id_is_not_set =
      'bookings.booking.item.errors.src_store_id_is_not_set';
  final src_warehouse_id_is_not_set =
      'bookings.booking.item.errors.src_warehouse_id_is_not_set';
  final tar_cost_center_id_is_not_set =
      'bookings.booking.item.errors.tar_cost_center_id_is_not_set';
  final tar_cost_type_id_is_not_set =
      'bookings.booking.item.errors.tar_cost_type_id_is_not_set';
  final tar_division_id_is_not_set =
      'bookings.booking.item.errors.tar_division_id_is_not_set';
  final tar_division_item_is_not_mapped =
      'bookings.booking.item.errors.tar_division_item_is_not_mapped';
  final tar_store_id_is_not_set =
      'bookings.booking.item.errors.tar_store_id_is_not_set';
  final tar_warehouse_id_is_not_set =
      'bookings.booking.item.errors.tar_warehouse_id_is_not_set';
  final zero_qty_not_allowed =
      'bookings.booking.item.errors.zero_qty_not_allowed';
}

class _LabelsBookingsBookingStatus {
  const _LabelsBookingsBookingStatus();

  final archived = 'bookings.booking.status.archived';
  final confirmed = 'bookings.booking.status.confirmed';
  final declined = 'bookings.booking.status.declined';
  final unconfirmed = 'bookings.booking.status.unconfirmed';
}

class _LabelsBookingsBookingReasonLookup {
  const _LabelsBookingsBookingReasonLookup();

  final search_available_only_online =
      'bookings.booking_reason_lookup.search_available_only_online';
  final select_button_label =
      'bookings.booking_reason_lookup.select_button_label';
  final title = 'bookings.booking_reason_lookup.title';
}

class _LabelsBookingsCreate {
  const _LabelsBookingsCreate();

  final create_button_label = 'bookings.create.create_button_label';
  final date_label = 'bookings.create.date_label';
  final reason_label = 'bookings.create.reason_label';
  final reference_label = 'bookings.create.reference_label';
  final title = 'bookings.create.title';
  final transaction_type_label = 'bookings.create.transaction_type_label';
  final transaction_type_not_selected =
      'bookings.create.transaction_type_not_selected';
  final transaction_type_reason_and_date_are_required =
      'bookings.create.transaction_type_reason_and_date_are_required';
}

class _LabelsBookingsFilter {
  const _LabelsBookingsFilter();

  final any = 'bookings.filter.any';
  final only_confirmed = 'bookings.filter.only_confirmed';
  final only_this_cost_center = 'bookings.filter.only_this_cost_center';
  final only_unconfirmed = 'bookings.filter.only_unconfirmed';
}

class _LabelsBookingsManage {
  const _LabelsBookingsManage();

  final booking = const _LabelsBookingsManageBooking();
  final booking_cannot_be_deleted = 'bookings.manage.booking_cannot_be_deleted';
  final booking_cannot_be_updated = 'bookings.manage.booking_cannot_be_updated';
  final booking_delete_alert = const _LabelsBookingsManageBookingDeleteAlert();
  final booking_deleted_successfully =
      'bookings.manage.booking_deleted_successfully';
  final booking_will_be_consumed = 'bookings.manage.booking_will_be_consumed';
  final filter = const _LabelsBookingsManageFilter();
  final product = const _LabelsBookingsManageProduct();
  final products = const _LabelsBookingsManageProducts();
}

class _LabelsBookingsManageBooking {
  const _LabelsBookingsManageBooking();

  final declined = 'bookings.manage.booking.declined';
  final declined_at = 'bookings.manage.booking.declined_at';
  final inter_property = const _LabelsBookingsManageBookingInterProperty();
  final st_ip_transfer = const _LabelsBookingsManageBookingStIpTransfer();
}

class _LabelsBookingsManageBookingInterProperty {
  const _LabelsBookingsManageBookingInterProperty();

  final label = 'bookings.manage.booking.inter_property.label';
  final number_of_hotels_confirmed =
      'bookings.manage.booking.inter_property.number_of_hotels_confirmed';
}

class _LabelsBookingsManageBookingStIpTransfer {
  const _LabelsBookingsManageBookingStIpTransfer();

  final label = 'bookings.manage.booking.st_ip_transfer.label';
}

class _LabelsBookingsManageBookingDeleteAlert {
  const _LabelsBookingsManageBookingDeleteAlert();

  final content = 'bookings.manage.booking_delete_alert.content';
  final no_action = 'bookings.manage.booking_delete_alert.no_action';
  final yes_action = 'bookings.manage.booking_delete_alert.yes_action';
}

class _LabelsBookingsManageFilter {
  const _LabelsBookingsManageFilter();

  final cost_center = const _LabelsBookingsManageFilterCostCenter();
  final cost_center_label = 'bookings.manage.filter.cost_center_label';
  final sorting = const _LabelsBookingsManageFilterSorting();
  final status = const _LabelsBookingsManageFilterStatus();
  final status_label = 'bookings.manage.filter.status_label';
  final transfer_type = const _LabelsBookingsManageFilterTransferType();
  final transfer_type_label = 'bookings.manage.filter.transfer_type_label';
}

class _LabelsBookingsManageFilterCostCenter {
  const _LabelsBookingsManageFilterCostCenter();

  final any = 'bookings.manage.filter.cost_center.any';
  final only_this = 'bookings.manage.filter.cost_center.only_this';
}

class _LabelsBookingsManageFilterSorting {
  const _LabelsBookingsManageFilterSorting();

  final by_name_asc = 'bookings.manage.filter.sorting.by_name_asc';
  final by_name_desc = 'bookings.manage.filter.sorting.by_name_desc';
  final by_order_date_asc = 'bookings.manage.filter.sorting.by_order_date_asc';
  final by_order_date_desc =
      'bookings.manage.filter.sorting.by_order_date_desc';
  final by_transfer_date_asc =
      'bookings.manage.filter.sorting.by_transfer_date_asc';
  final by_transfer_date_desc =
      'bookings.manage.filter.sorting.by_transfer_date_desc';
}

class _LabelsBookingsManageFilterStatus {
  const _LabelsBookingsManageFilterStatus();

  final any = 'bookings.manage.filter.status.any';
  final archived = 'bookings.manage.filter.status.archived';
  final booked = 'bookings.manage.filter.status.booked';
  final confirmed = 'bookings.manage.filter.status.confirmed';
  final date_range = 'bookings.manage.filter.status.date_range';
  final declined = 'bookings.manage.filter.status.declined';
  final delivered = 'bookings.manage.filter.status.delivered';
  final empty = 'bookings.manage.filter.status.empty';
  final not_approved = 'bookings.manage.filter.status.not_approved';
  final pending = 'bookings.manage.filter.status.pending';
  final ready_to_book = 'bookings.manage.filter.status.ready_to_book';
  final unconfirmed = 'bookings.manage.filter.status.unconfirmed';
}

class _LabelsBookingsManageFilterTransferType {
  const _LabelsBookingsManageFilterTransferType();

  final any = 'bookings.manage.filter.transfer_type.any';
  final out_not_system = 'bookings.manage.filter.transfer_type.out_not_system';
  final out_order_inhouse =
      'bookings.manage.filter.transfer_type.out_order_inhouse';
  final out_order_inter_property =
      'bookings.manage.filter.transfer_type.out_order_inter_property';
  final stk_ip_transfer =
      'bookings.manage.filter.transfer_type.stk_ip_transfer';
  final stock_correction =
      'bookings.manage.filter.transfer_type.stock_correction';
  final stock_out_recipe =
      'bookings.manage.filter.transfer_type.stock_out_recipe';
  final stock_transfer = 'bookings.manage.filter.transfer_type.stock_transfer';
}

class _LabelsBookingsManageProduct {
  const _LabelsBookingsManageProduct();

  final barcode = 'bookings.manage.product.barcode';
  final confirm_button_label = 'bookings.manage.product.confirm_button_label';
  final confirmed_label = 'bookings.manage.product.confirmed_label';
  final deleted_successfully = 'bookings.manage.product.deleted_successfully';
  final details_button_label = 'bookings.manage.product.details_button_label';
  final edit_button_label = 'bookings.manage.product.edit_button_label';
  final price_per_inventory_unit =
      'bookings.manage.product.price_per_inventory_unit';
  final quantity_field_label = 'bookings.manage.product.quantity_field_label';
  final quantity_update_button_label =
      'bookings.manage.product.quantity_update_button_label';
  final source_cost_center = 'bookings.manage.product.source_cost_center';
  final source_cost_type = 'bookings.manage.product.source_cost_type';
  final source_division = 'bookings.manage.product.source_division';
  final source_store = 'bookings.manage.product.source_store';
  final stock = 'bookings.manage.product.stock';
  final target_cost_center = 'bookings.manage.product.target_cost_center';
  final target_cost_type = 'bookings.manage.product.target_cost_type';
  final target_division = 'bookings.manage.product.target_division';
  final target_store = 'bookings.manage.product.target_store';
  final title = 'bookings.manage.product.title';
  final updated_successfully = 'bookings.manage.product.updated_successfully';
}

class _LabelsBookingsManageProducts {
  const _LabelsBookingsManageProducts();

  final filter = const _LabelsBookingsManageProductsFilter();
  final product_added_successfully =
      'bookings.manage.products.product_added_successfully';
  final product_delete_alert =
      const _LabelsBookingsManageProductsProductDeleteAlert();
  final product_delete_button =
      const _LabelsBookingsManageProductsProductDeleteButton();
  final product_deleted_successfully =
      'bookings.manage.products.product_deleted_successfully';
  final search_available_only_online =
      'bookings.manage.products.search_available_only_online';
}

class _LabelsBookingsManageProductsFilter {
  const _LabelsBookingsManageProductsFilter();

  final status = const _LabelsBookingsManageProductsFilterStatus();
  final status_label = 'bookings.manage.products.filter.status_label';
}

class _LabelsBookingsManageProductsFilterStatus {
  const _LabelsBookingsManageProductsFilterStatus();

  final all = 'bookings.manage.products.filter.status.all';
  final complete = 'bookings.manage.products.filter.status.complete';
  final incomplete = 'bookings.manage.products.filter.status.incomplete';
}

class _LabelsBookingsManageProductsProductDeleteAlert {
  const _LabelsBookingsManageProductsProductDeleteAlert();

  final content = 'bookings.manage.products.product_delete_alert.content';
  final no_action = 'bookings.manage.products.product_delete_alert.no_action';
  final yes_action = 'bookings.manage.products.product_delete_alert.yes_action';
}

class _LabelsBookingsManageProductsProductDeleteButton {
  const _LabelsBookingsManageProductsProductDeleteButton();

  final label = 'bookings.manage.products.product_delete_button.label';
}

class _LabelsBookingsReasonLookup {
  const _LabelsBookingsReasonLookup();

  final search_available_only_online =
      'bookings.reason_lookup.search_available_only_online';
}

class _LabelsBookingsSearch {
  const _LabelsBookingsSearch();

  final placeholder = 'bookings.search.placeholder';
}

class _LabelsBudget {
  const _LabelsBudget();

  final calc = const _LabelsBudgetCalc();
}

class _LabelsBudgetCalc {
  const _LabelsBudgetCalc();

  final actual_annual_expenses = 'budget.calc.actual_annual_expenses';
  final actual_period_expenses = 'budget.calc.actual_period_expenses';
  final annual_only = 'budget.calc.annual_only';
  final approved = 'budget.calc.approved';
  final at_approval_center = 'budget.calc.at_approval_center';
  final at_shopping_cart = 'budget.calc.at_shopping_cart';
  final budget_exited = 'budget.calc.budget_exited';
  final budget_warning = 'budget.calc.budget_warning';
  final budget_warnings = 'budget.calc.budget_warnings';
  final current_request = 'budget.calc.current_request';
  final expected_annual_expenses = 'budget.calc.expected_annual_expenses';
  final expected_annual_remaining = 'budget.calc.expected_annual_remaining';
  final expected_period_expenses = 'budget.calc.expected_period_expenses';
  final expected_period_remaining = 'budget.calc.expected_period_remaining';
  final in_budget = 'budget.calc.in_budget';
  final more_info = 'budget.calc.more_info';
  final no_budgets = 'budget.calc.no_budgets';
  final not_approved = 'budget.calc.not_approved';
  final other_purchase_requests = 'budget.calc.other_purchase_requests';
}

class _LabelsBulkPriceOffers {
  const _LabelsBulkPriceOffers();

  final add_button_label = 'bulk_price_offers.add_button_label';
  final price_label = 'bulk_price_offers.price_label';
  final profit_label = 'bulk_price_offers.profit_label';
  final quantity_label = 'bulk_price_offers.quantity_label';
  final regular_price_label = 'bulk_price_offers.regular_price_label';
  final table = const _LabelsBulkPriceOffersTable();
  final title = 'bulk_price_offers.title';
}

class _LabelsBulkPriceOffersTable {
  const _LabelsBulkPriceOffersTable();

  final price = 'bulk_price_offers.table.price';
  final quantity = 'bulk_price_offers.table.quantity';
}

class _LabelsButton {
  const _LabelsButton();

  final edit = 'button.edit';
}

class _LabelsCamera {
  const _LabelsCamera();

  final no_cameras_available = 'camera.no_cameras_available';
  final title = const _LabelsCameraTitle();
}

class _LabelsCameraTitle {
  const _LabelsCameraTitle();

  final barcode = 'camera.title.barcode';
  final capture = 'camera.title.capture';
}

class _LabelsCart {
  const _LabelsCart();

  final add_free_text_order_button_label =
      'cart.add_free_text_order_button_label';
  final adding_results = const _LabelsCartAddingResults();
  final auto_consolidate = const _LabelsCartAutoConsolidate();
  final auto_consolidate_button_label = 'cart.auto_consolidate_button_label';
  final check_connection_and_try_again_label =
      'cart.check_connection_and_try_again_label';
  final continue_button_label = 'cart.continue_button_label';
  final empty_label = 'cart.empty_label';
  final free_text_order = const _LabelsCartFreeTextOrder();
  final menu_count_label = 'cart.menu_count_label';
  final no_items_to_approve_with_budget =
      'cart.no_items_to_approve_with_budget';
  final no_items_to_send_with_budget = 'cart.no_items_to_send_with_budget';
  final no_permission_to_create_order_message =
      'cart.no_permission_to_create_order_message';
  final note = const _LabelsCartNote();
  final order = const _LabelsCartOrder();
  final order_to_approve = const _LabelsCartOrderToApprove();
  final order_to_merge = const _LabelsCartOrderToMerge();
  final order_to_send = const _LabelsCartOrderToSend();
  final order_value_label = 'cart.order_value_label';
  final orders_to_approve = const _LabelsCartOrdersToApprove();
  final orders_to_merge = const _LabelsCartOrdersToMerge();
  final orders_to_send = const _LabelsCartOrdersToSend();
  final overview = const _LabelsCartOverview();
  final product = const _LabelsCartProduct();
  final product_offers = const _LabelsCartProductOffers();
  final products = const _LabelsCartProducts();
  final products_count_label__named = 'cart.products_count_label__named';
  final products_tab_label = 'cart.products_tab_label';
  final request_approval_button_label = 'cart.request_approval_button_label';
  final sending_result = const _LabelsCartSendingResult();
  final sending_results = const _LabelsCartSendingResults();
  final uploading_result = const _LabelsCartUploadingResult();
  final view_pdf_button_label = 'cart.view_pdf_button_label';
}

class _LabelsCartAddingResults {
  const _LabelsCartAddingResults();

  final title = 'cart.adding_results.title';
}

class _LabelsCartAutoConsolidate {
  const _LabelsCartAutoConsolidate();

  final available_only_online = 'cart.auto_consolidate.available_only_online';
  final consolidate_button = 'cart.auto_consolidate.consolidate_button';
  final cost_centers = const _LabelsCartAutoConsolidateCostCenters();
  final no_cost_centers_selected =
      'cart.auto_consolidate.no_cost_centers_selected';
  final success_message = 'cart.auto_consolidate.success_message';
  final title = 'cart.auto_consolidate.title';
}

class _LabelsCartAutoConsolidateCostCenters {
  const _LabelsCartAutoConsolidateCostCenters();

  final select_all = 'cart.auto_consolidate.cost_centers.select_all';
}

class _LabelsCartFreeTextOrder {
  const _LabelsCartFreeTextOrder();

  final form = const _LabelsCartFreeTextOrderForm();
  final product_lookup = const _LabelsCartFreeTextOrderProductLookup();
  final supplier_lookup = const _LabelsCartFreeTextOrderSupplierLookup();
}

class _LabelsCartFreeTextOrderForm {
  const _LabelsCartFreeTextOrderForm();

  final category_label = 'cart.free_text_order.form.category_label';
  final content_unit_label = 'cart.free_text_order.form.content_unit_label';
  final content_units_per_order_unit_label =
      'cart.free_text_order.form.content_units_per_order_unit_label';
  final create_title = 'cart.free_text_order.form.create_title';
  final created_successfully = 'cart.free_text_order.form.created_successfully';
  final default_price_alert = 'cart.free_text_order.form.default_price_alert';
  final description_label = 'cart.free_text_order.form.description_label';
  final edit_title = 'cart.free_text_order.form.edit_title';
  final inventory_unit_label = 'cart.free_text_order.form.inventory_unit_label';
  final item_approved_in_cart =
      'cart.free_text_order.form.item_approved_in_cart';
  final item_in_cart_field_edit_message =
      'cart.free_text_order.form.item_in_cart_field_edit_message';
  final item_number_label = 'cart.free_text_order.form.item_number_label';
  final optional_details_label =
      'cart.free_text_order.form.optional_details_label';
  final order_unit_label = 'cart.free_text_order.form.order_unit_label';
  final please_fill_all_required_fields =
      'cart.free_text_order.form.please_fill_all_required_fields';
  final price_change_restricted =
      'cart.free_text_order.form.price_change_restricted';
  final quantity_change_restricted =
      'cart.free_text_order.form.quantity_change_restricted';
  final quantity_label = 'cart.free_text_order.form.quantity_label';
  final quantity_set_button = 'cart.free_text_order.form.quantity_set_button';
  final read_only_field_edit_message =
      'cart.free_text_order.form.read_only_field_edit_message';
  final read_only_field_existing_product_message =
      'cart.free_text_order.form.read_only_field_existing_product_message';
  final save_button_label = 'cart.free_text_order.form.save_button_label';
  final select_supplier_first =
      'cart.free_text_order.form.select_supplier_first';
  final supplier_label = 'cart.free_text_order.form.supplier_label';
  final title_field_label = 'cart.free_text_order.form.title_field_label';
  final unit_price_label = 'cart.free_text_order.form.unit_price_label';
  final unit_price_set_button =
      'cart.free_text_order.form.unit_price_set_button';
  final vat_gst_rate_label = 'cart.free_text_order.form.vat_gst_rate_label';
}

class _LabelsCartFreeTextOrderProductLookup {
  const _LabelsCartFreeTextOrderProductLookup();

  final add_button_label =
      'cart.free_text_order.product_lookup.add_button_label';
  final not_found = 'cart.free_text_order.product_lookup.not_found';
  final only_online = 'cart.free_text_order.product_lookup.only_online';
  final previously_ordered_label =
      'cart.free_text_order.product_lookup.previously_ordered_label';
  final title = 'cart.free_text_order.product_lookup.title';
}

class _LabelsCartFreeTextOrderSupplierLookup {
  const _LabelsCartFreeTextOrderSupplierLookup();

  final only_online = 'cart.free_text_order.supplier_lookup.only_online';
  final select_button_label =
      'cart.free_text_order.supplier_lookup.select_button_label';
  final title = 'cart.free_text_order.supplier_lookup.title';
}

class _LabelsCartNote {
  const _LabelsCartNote();

  final add_button_label = 'cart.note.add_button_label';
  final add_title = 'cart.note.add_title';
  final delete_alert_content = 'cart.note.delete_alert_content';
  final delete_alert_no_action = 'cart.note.delete_alert_no_action';
  final delete_alert_yes_action = 'cart.note.delete_alert_yes_action';
  final edit_button_label = 'cart.note.edit_button_label';
  final edit_title = 'cart.note.edit_title';
  final no_note_description = 'cart.note.no_note_description';
  final no_note_title = 'cart.note.no_note_title';
  final note_filed_label = 'cart.note.note_filed_label';
  final save_button_label = 'cart.note.save_button_label';
  final updated_successfully = 'cart.note.updated_successfully';
}

class _LabelsCartOrder {
  const _LabelsCartOrder();

  final attachments = const _LabelsCartOrderAttachments();
  final delivery_date = 'cart.order.delivery_date';
  final delivery_date_override_label =
      'cart.order.delivery_date_override_label';
  final delivery_date_updated_successfully =
      'cart.order.delivery_date_updated_successfully';
  final discount_update_title = 'cart.order.discount_update_title';
  final discount_updated_successfully =
      'cart.order.discount_updated_successfully';
  final external_comment = const _LabelsCartOrderExternalComment();
  final external_comment_updated_successfully =
      'cart.order.external_comment_updated_successfully';
  final minimum_order_amount_label = 'cart.order.minimum_order_amount_label';
  final minimum_quantity_surcharge_label =
      'cart.order.minimum_quantity_surcharge_label';
  final order_until_label = 'cart.order.order_until_label';
  final order_value = 'cart.order.order_value';
  final request_authorization_button_label =
      'cart.order.request_authorization_button_label';
}

class _LabelsCartOrderAttachments {
  const _LabelsCartOrderAttachments();

  final attach_file = 'cart.order.attachments.attach_file';
  final delete = 'cart.order.attachments.delete';
  final delete_confirmation_content =
      'cart.order.attachments.delete_confirmation_content';
  final delete_confirmation_no_label =
      'cart.order.attachments.delete_confirmation_no_label';
  final delete_confirmation_yes_label =
      'cart.order.attachments.delete_confirmation_yes_label';
  final document = 'cart.order.attachments.document';
  final documents = 'cart.order.attachments.documents';
  final file_is_too_large = 'cart.order.attachments.file_is_too_large';
  final no_attachments = 'cart.order.attachments.no_attachments';
  final ordering_title = 'cart.order.attachments.ordering_title';
  final rename = 'cart.order.attachments.rename';
  final rename_cancel_label = 'cart.order.attachments.rename_cancel_label';
  final rename_confirmation_label =
      'cart.order.attachments.rename_confirmation_label';
  final rename_field_label = 'cart.order.attachments.rename_field_label';
  final take_a_photo = 'cart.order.attachments.take_a_photo';
  final title = 'cart.order.attachments.title';
  final too_many_files = 'cart.order.attachments.too_many_files';
}

class _LabelsCartOrderExternalComment {
  const _LabelsCartOrderExternalComment();

  final add = 'cart.order.external_comment.add';
  final articles_count = 'cart.order.external_comment.articles_count';
  final delete = 'cart.order.external_comment.delete';
  final edit = 'cart.order.external_comment.edit';
  final helper_text = 'cart.order.external_comment.helper_text';
  final history = const _LabelsCartOrderExternalCommentHistory();
  final label = 'cart.order.external_comment.label';
  final last_edited = 'cart.order.external_comment.last_edited';
  final lead_time_day = 'cart.order.external_comment.lead_time_day';
  final lead_time_days = 'cart.order.external_comment.lead_time_days';
  final view_history = 'cart.order.external_comment.view_history';
}

class _LabelsCartOrderExternalCommentHistory {
  const _LabelsCartOrderExternalCommentHistory();

  final deleted = 'cart.order.external_comment.history.deleted';
  final title = 'cart.order.external_comment.history.title';
}

class _LabelsCartOrderToApprove {
  const _LabelsCartOrderToApprove();

  final approval_requested_successfully =
      'cart.order_to_approve.approval_requested_successfully';
  final approval_requested_successfully_with_upload_error =
      'cart.order_to_approve.approval_requested_successfully_with_upload_error';
  final approver = 'cart.order_to_approve.approver';
  final approver_required = 'cart.order_to_approve.approver_required';
  final attachment = 'cart.order_to_approve.attachment';
  final attachments = 'cart.order_to_approve.attachments';
  final attachments_label = 'cart.order_to_approve.attachments_label';
  final budget_error = 'cart.order_to_approve.budget_error';
  final budget_required = 'cart.order_to_approve.budget_required';
  final comment = 'cart.order_to_approve.comment';
  final comment_label = 'cart.order_to_approve.comment_label';
  final comment_title = 'cart.order_to_approve.comment_title';
  final comment_update_alert =
      const _LabelsCartOrderToApproveCommentUpdateAlert();
  final no_attachments = 'cart.order_to_approve.no_attachments';
  final request_approval_alert =
      const _LabelsCartOrderToApproveRequestApprovalAlert();
  final send_after_approval_warning =
      'cart.order_to_approve.send_after_approval_warning';
  final value_to_approve = 'cart.order_to_approve.value_to_approve';
}

class _LabelsCartOrderToApproveCommentUpdateAlert {
  const _LabelsCartOrderToApproveCommentUpdateAlert();

  final add_action = 'cart.order_to_approve.comment_update_alert.add_action';
  final cancel_action =
      'cart.order_to_approve.comment_update_alert.cancel_action';
  final update_action =
      'cart.order_to_approve.comment_update_alert.update_action';
}

class _LabelsCartOrderToApproveRequestApprovalAlert {
  const _LabelsCartOrderToApproveRequestApprovalAlert();

  final content = 'cart.order_to_approve.request_approval_alert.content';
  final no_action = 'cart.order_to_approve.request_approval_alert.no_action';
  final yes_action = 'cart.order_to_approve.request_approval_alert.yes_action';
}

class _LabelsCartOrderToMerge {
  const _LabelsCartOrderToMerge();

  final select_to_merge_label = 'cart.order_to_merge.select_to_merge_label';
}

class _LabelsCartOrderToSend {
  const _LabelsCartOrderToSend();

  final comment_update_alert = const _LabelsCartOrderToSendCommentUpdateAlert();
  final date_of_delivery = 'cart.order_to_send.date_of_delivery';
  final delivery_date_updated_successfully =
      'cart.order_to_send.delivery_date_updated_successfully';
  final delivery_instructions = 'cart.order_to_send.delivery_instructions';
  final delivery_instructions_label =
      'cart.order_to_send.delivery_instructions_label';
  final delivery_instructions_title =
      'cart.order_to_send.delivery_instructions_title';
  final delivery_instructions_update_alert =
      const _LabelsCartOrderToSendDeliveryInstructionsUpdateAlert();
  final discount_amount = 'cart.order_to_send.discount_amount';
  final external_comment_updated_successfully =
      'cart.order_to_send.external_comment_updated_successfully';
  final last_order_time = 'cart.order_to_send.last_order_time';
  final merge_orders_button_label =
      'cart.order_to_send.merge_orders_button_label';
  final not_submit_to_supplier_checkbox_label =
      'cart.order_to_send.not_submit_to_supplier_checkbox_label';
  final order_value = 'cart.order_to_send.order_value';
  final send_order_checkbox_label =
      'cart.order_to_send.send_order_checkbox_label';
  final status = const _LabelsCartOrderToSendStatus();
}

class _LabelsCartOrderToSendCommentUpdateAlert {
  const _LabelsCartOrderToSendCommentUpdateAlert();

  final add_action = 'cart.order_to_send.comment_update_alert.add_action';
  final update_action = 'cart.order_to_send.comment_update_alert.update_action';
}

class _LabelsCartOrderToSendDeliveryInstructionsUpdateAlert {
  const _LabelsCartOrderToSendDeliveryInstructionsUpdateAlert();

  final cancel_action =
      'cart.order_to_send.delivery_instructions_update_alert.cancel_action';
}

class _LabelsCartOrderToSendStatus {
  const _LabelsCartOrderToSendStatus();

  final approval_required = 'cart.order_to_send.status.approval_required';
  final backend_error = 'cart.order_to_send.status.backend_error';
  final cost_types_is_not_valid =
      'cart.order_to_send.status.cost_types_is_not_valid';
  final label = 'cart.order_to_send.status.label';
  final minimal_order_value_is_not_reached =
      'cart.order_to_send.status.minimal_order_value_is_not_reached';
  final no_customer_id = 'cart.order_to_send.status.no_customer_id';
  final no_error = 'cart.order_to_send.status.no_error';
  final no_order = 'cart.order_to_send.status.no_order';
  final no_order_email_address =
      'cart.order_to_send.status.no_order_email_address';
  final not_found = 'cart.order_to_send.status.not_found';
  final ok = 'cart.order_to_send.status.ok';
  final supplier_fto_disabled =
      'cart.order_to_send.status.supplier_fto_disabled';
  final unknown = 'cart.order_to_send.status.unknown';
  final wrong_delivery_date = 'cart.order_to_send.status.wrong_delivery_date';
}

class _LabelsCartOrdersToApprove {
  const _LabelsCartOrdersToApprove();

  final no_orders = 'cart.orders_to_approve.no_orders';
  final title = 'cart.orders_to_approve.title';
}

class _LabelsCartOrdersToMerge {
  const _LabelsCartOrdersToMerge();

  final merge_alert = const _LabelsCartOrdersToMergeMergeAlert();
  final merge_button_label = 'cart.orders_to_merge.merge_button_label';
  final no_orders = 'cart.orders_to_merge.no_orders';
  final orders_merged_successfully =
      'cart.orders_to_merge.orders_merged_successfully';
  final title = 'cart.orders_to_merge.title';
}

class _LabelsCartOrdersToMergeMergeAlert {
  const _LabelsCartOrdersToMergeMergeAlert();

  final content = 'cart.orders_to_merge.merge_alert.content';
  final no_action = 'cart.orders_to_merge.merge_alert.no_action';
  final yes_action = 'cart.orders_to_merge.merge_alert.yes_action';
}

class _LabelsCartOrdersToSend {
  const _LabelsCartOrdersToSend();

  final no_orders = 'cart.orders_to_send.no_orders';
  final see_orders_to_approve_button =
      'cart.orders_to_send.see_orders_to_approve_button';
  final send_alert = const _LabelsCartOrdersToSendSendAlert();
  final send_button_label = 'cart.orders_to_send.send_button_label';
  final send_with_only_create_alert =
      const _LabelsCartOrdersToSendSendWithOnlyCreateAlert();
  final title = 'cart.orders_to_send.title';
}

class _LabelsCartOrdersToSendSendAlert {
  const _LabelsCartOrdersToSendSendAlert();

  final content = 'cart.orders_to_send.send_alert.content';
  final no_action = 'cart.orders_to_send.send_alert.no_action';
  final yes_action = 'cart.orders_to_send.send_alert.yes_action';
}

class _LabelsCartOrdersToSendSendWithOnlyCreateAlert {
  const _LabelsCartOrdersToSendSendWithOnlyCreateAlert();

  final content = 'cart.orders_to_send.send_with_only_create_alert.content';
  final no_action = 'cart.orders_to_send.send_with_only_create_alert.no_action';
  final yes_action =
      'cart.orders_to_send.send_with_only_create_alert.yes_action';
}

class _LabelsCartOverview {
  const _LabelsCartOverview();

  final articles_title = 'cart.overview.articles_title';
  final budget_title = 'cart.overview.budget_title';
  final filter = const _LabelsCartOverviewFilter();
  final notes_title = 'cart.overview.notes_title';
  final offline_articles_title = 'cart.overview.offline_articles_title';
  final search_available_only_online =
      'cart.overview.search_available_only_online';
  final suppliers_title = 'cart.overview.suppliers_title';
}

class _LabelsCartOverviewFilter {
  const _LabelsCartOverviewFilter();

  final status = const _LabelsCartOverviewFilterStatus();
}

class _LabelsCartOverviewFilterStatus {
  const _LabelsCartOverviewFilterStatus();

  final all = 'cart.overview.filter.status.all';
  final approved = 'cart.overview.filter.status.approved';
  final label = 'cart.overview.filter.status.label';
  final not_approved = 'cart.overview.filter.status.not_approved';
}

class _LabelsCartProduct {
  const _LabelsCartProduct();

  final already_approved = 'cart.product.already_approved';
  final approval_required = 'cart.product.approval_required';
  final better_price_available = 'cart.product.better_price_available';
  final budget_will_not_be_calculated_for_cost_type =
      'cart.product.budget_will_not_be_calculated_for_cost_type';
  final comment_button_label = 'cart.product.comment_button_label';
  final comment_label = 'cart.product.comment_label';
  final comment_title = 'cart.product.comment_title';
  final comment_update_alert = const _LabelsCartProductCommentUpdateAlert();
  final comment_updated_successfully =
      'cart.product.comment_updated_successfully';
  final content_units_per_order_unit =
      'cart.product.content_units_per_order_unit';
  final cost_type = 'cart.product.cost_type';
  final cost_type_has_no_budget = 'cart.product.cost_type_has_no_budget';
  final cost_type_updated_successfully =
      'cart.product.cost_type_updated_successfully';
  final delete_alert = const _LabelsCartProductDeleteAlert();
  final deleted_successfully = 'cart.product.deleted_successfully';
  final details_button_label = 'cart.product.details_button_label';
  final discount = 'cart.product.discount';
  final edit_quantity_button_label = 'cart.product.edit_quantity_button_label';
  final flat_discount_updated_successfully =
      'cart.product.flat_discount_updated_successfully';
  final no_label = 'cart.product.no_label';
  final origin = 'cart.product.origin';
  final packing_unit = 'cart.product.packing_unit';
  final percentage_discount_updated_successfully =
      'cart.product.percentage_discount_updated_successfully';
  final please_select_cost_type = 'cart.product.please_select_cost_type';
  final price_per_unit = 'cart.product.price_per_unit';
  final qty_updated_successfully = 'cart.product.qty_updated_successfully';
  final quantity = 'cart.product.quantity';
  final requested = 'cart.product.requested';
  final supplier = 'cart.product.supplier';
  final total = 'cart.product.total';
  final yes_label = 'cart.product.yes_label';
}

class _LabelsCartProductCommentUpdateAlert {
  const _LabelsCartProductCommentUpdateAlert();

  final add_action = 'cart.product.comment_update_alert.add_action';
  final cancel_action = 'cart.product.comment_update_alert.cancel_action';
  final update_action = 'cart.product.comment_update_alert.update_action';
}

class _LabelsCartProductDeleteAlert {
  const _LabelsCartProductDeleteAlert();

  final content = 'cart.product.delete_alert.content';
  final no_action = 'cart.product.delete_alert.no_action';
  final yes_action = 'cart.product.delete_alert.yes_action';
}

class _LabelsCartProductOffers {
  const _LabelsCartProductOffers();

  final changes_available_only_online =
      'cart.product_offers.changes_available_only_online';
  final offer = const _LabelsCartProductOffersOffer();
  final title = 'cart.product_offers.title';
}

class _LabelsCartProductOffersOffer {
  const _LabelsCartProductOffersOffer();

  final packing_info = 'cart.product_offers.offer.packing_info';
  final price_per_unit = 'cart.product_offers.offer.price_per_unit';
  final quantity = 'cart.product_offers.offer.quantity';
}

class _LabelsCartProducts {
  const _LabelsCartProducts();

  final cost_type_lookup_title = 'cart.products.cost_type_lookup_title';
  final filter = const _LabelsCartProductsFilter();
  final search_available_only_online =
      'cart.products.search_available_only_online';
}

class _LabelsCartProductsFilter {
  const _LabelsCartProductsFilter();

  final status = const _LabelsCartProductsFilterStatus();
}

class _LabelsCartProductsFilterStatus {
  const _LabelsCartProductsFilterStatus();

  final all = 'cart.products.filter.status.all';
  final approved = 'cart.products.filter.status.approved';
  final label = 'cart.products.filter.status.label';
  final not_approved = 'cart.products.filter.status.not_approved';
}

class _LabelsCartSendingResult {
  const _LabelsCartSendingResult();

  final date_of_delivery = 'cart.sending_result.date_of_delivery';
  final discount_amount = 'cart.sending_result.discount_amount';
  final failed_to_send_label = 'cart.sending_result.failed_to_send_label';
  final order_marked_to_only_create =
      'cart.sending_result.order_marked_to_only_create';
  final order_status = 'cart.sending_result.order_status';
  final order_value = 'cart.sending_result.order_value';
  final sent_successful_label = 'cart.sending_result.sent_successful_label';
}

class _LabelsCartSendingResults {
  const _LabelsCartSendingResults();

  final title = 'cart.sending_results.title';
}

class _LabelsCartUploadingResult {
  const _LabelsCartUploadingResult();

  final add_successful_label = 'cart.uploading_result.add_successful_label';
  final failed_to_add_label = 'cart.uploading_result.failed_to_add_label';
  final product = const _LabelsCartUploadingResultProduct();
  final status = const _LabelsCartUploadingResultStatus();
}

class _LabelsCartUploadingResultProduct {
  const _LabelsCartUploadingResultProduct();

  final label = 'cart.uploading_result.product.label';
}

class _LabelsCartUploadingResultStatus {
  const _LabelsCartUploadingResultStatus();

  final cart_item_can_not_be_modified =
      'cart.uploading_result.status.cart_item_can_not_be_modified';
  final label = 'cart.uploading_result.status.label';
  final missing_customer_id =
      'cart.uploading_result.status.missing_customer_id';
  final oci_can_not_add_product =
      'cart.uploading_result.status.oci_can_not_add_product';
  final ok = 'cart.uploading_result.status.ok';
  final product_not_found = 'cart.uploading_result.status.product_not_found';
  final scan_item_can_not_be_added =
      'cart.uploading_result.status.scan_item_can_not_be_added';
  final unknown = 'cart.uploading_result.status.unknown';
}

class _LabelsCatalog {
  const _LabelsCatalog();

  final index = const _LabelsCatalogIndex();
  final search = const _LabelsCatalogSearch();
}

class _LabelsCatalogIndex {
  const _LabelsCatalogIndex();

  final details = const _LabelsCatalogIndexDetails();
  final status = const _LabelsCatalogIndexStatus();
}

class _LabelsCatalogIndexDetails {
  const _LabelsCatalogIndexDetails();

  final indexing_status_label = 'catalog.index.details.indexing_status_label';
  final last_indexed_label = 'catalog.index.details.last_indexed_label';
  final products_indexed_label = 'catalog.index.details.products_indexed_label';
  final refresh_label = 'catalog.index.details.refresh_label';
  final reindex_label = 'catalog.index.details.reindex_label';
  final reindex_request_disabled =
      'catalog.index.details.reindex_request_disabled';
  final reindex_request_success =
      'catalog.index.details.reindex_request_success';
  final total_products_label = 'catalog.index.details.total_products_label';
}

class _LabelsCatalogIndexStatus {
  const _LabelsCatalogIndexStatus();

  final done = 'catalog.index.status.done';
  final in_progress = 'catalog.index.status.in_progress';
  final unknown = 'catalog.index.status.unknown';
}

class _LabelsCatalogSearch {
  const _LabelsCatalogSearch();

  final index_details_button_label =
      'catalog.search.index_details_button_label';
  final index_details_title = 'catalog.search.index_details_title';
}

class _LabelsCommon {
  const _LabelsCommon();

  final dispatching_cost_center_label = 'common.dispatching_cost_center_label';
  final dispatching_division_label = 'common.dispatching_division_label';
  final dispatching_store_label = 'common.dispatching_store_label';
  final incoming_cost_type_label = 'common.incoming_cost_type_label';
  final outgoing_cost_type_label = 'common.outgoing_cost_type_label';
  final receiving_cost_center_label = 'common.receiving_cost_center_label';
  final receiving_division_label = 'common.receiving_division_label';
  final receiving_store_label = 'common.receiving_store_label';
}

class _LabelsDashboard {
  const _LabelsDashboard();

  final welcome_label__named = 'dashboard.welcome_label__named';
}

class _LabelsDeliveryNotes {
  const _LabelsDeliveryNotes();

  final available_only_online = 'delivery_notes.available_only_online';
  final booking_date = 'delivery_notes.booking_date';
  final booking_voucher = 'delivery_notes.booking_voucher';
  final delivery_note = const _LabelsDeliveryNotesDeliveryNote();
  final delivery_note_date = 'delivery_notes.delivery_note_date';
  final delivery_note_no = 'delivery_notes.delivery_note_no';
  final delivery_note_total = 'delivery_notes.delivery_note_total';
  final delivery_note_total_check = 'delivery_notes.delivery_note_total_check';
  final empty_label = 'delivery_notes.empty_label';
  final filter = const _LabelsDeliveryNotesFilter();
  final invoice = 'delivery_notes.invoice';
  final medius_status = const _LabelsDeliveryNotesMediusStatus();
  final open_receiving_button_label =
      'delivery_notes.open_receiving_button_label';
  final receiving_confirmed_by = 'delivery_notes.receiving_confirmed_by';
  final status = const _LabelsDeliveryNotesStatus();
  final supplier = 'delivery_notes.supplier';
  final title = 'delivery_notes.title';
  final toggle_success = 'delivery_notes.toggle_success';
  final total_delivery_note = 'delivery_notes.total_delivery_note';
  final transaction_type = const _LabelsDeliveryNotesTransactionType();
  final view_pdf_button_label = 'delivery_notes.view_pdf_button_label';
  final warnings_tag = 'delivery_notes.warnings_tag';
}

class _LabelsDeliveryNotesDeliveryNote {
  const _LabelsDeliveryNotesDeliveryNote();

  final medius = const _LabelsDeliveryNotesDeliveryNoteMedius();
  final medius_info = const _LabelsDeliveryNotesDeliveryNoteMediusInfo();
  final medius_status = const _LabelsDeliveryNotesDeliveryNoteMediusStatus();
  final title = 'delivery_notes.delivery_note.title';
  final warning = const _LabelsDeliveryNotesDeliveryNoteWarning();
}

class _LabelsDeliveryNotesDeliveryNoteMedius {
  const _LabelsDeliveryNotesDeliveryNoteMedius();

  final re_export = 'delivery_notes.delivery_note.medius.re_export';
  final reset_success = 'delivery_notes.delivery_note.medius.reset_success';
}

class _LabelsDeliveryNotesDeliveryNoteMediusInfo {
  const _LabelsDeliveryNotesDeliveryNoteMediusInfo();

  final mapping_error =
      'delivery_notes.delivery_note.medius_info.mapping_error';
  final server_error = 'delivery_notes.delivery_note.medius_info.server_error';
}

class _LabelsDeliveryNotesDeliveryNoteMediusStatus {
  const _LabelsDeliveryNotesDeliveryNoteMediusStatus();

  final mapping_error =
      'delivery_notes.delivery_note.medius_status.mapping_error';
  final ok = 'delivery_notes.delivery_note.medius_status.ok';
  final server_error =
      'delivery_notes.delivery_note.medius_status.server_error';
}

class _LabelsDeliveryNotesDeliveryNoteWarning {
  const _LabelsDeliveryNotesDeliveryNoteWarning();

  final archived_items = 'delivery_notes.delivery_note.warning.archived_items';
  final incoming_goods_were_processed_but_not_booked =
      'delivery_notes.delivery_note.warning.incoming_goods_were_processed_but_not_booked';
  final price_differs_from_average_price =
      'delivery_notes.delivery_note.warning.price_differs_from_average_price';
}

class _LabelsDeliveryNotesFilter {
  const _LabelsDeliveryNotesFilter();

  final booking_id = const _LabelsDeliveryNotesFilterBookingId();
  final date_range = 'delivery_notes.filter.date_range';
  final delivery_note_amount =
      const _LabelsDeliveryNotesFilterDeliveryNoteAmount();
  final delivery_note_id = const _LabelsDeliveryNotesFilterDeliveryNoteId();
  final product_name = const _LabelsDeliveryNotesFilterProductName();
  final receiving_confirmed_by =
      const _LabelsDeliveryNotesFilterReceivingConfirmedBy();
  final sorting = const _LabelsDeliveryNotesFilterSorting();
  final supplier_name = const _LabelsDeliveryNotesFilterSupplierName();
}

class _LabelsDeliveryNotesFilterBookingId {
  const _LabelsDeliveryNotesFilterBookingId();

  final hint_text = 'delivery_notes.filter.booking_id.hint_text';
  final label = 'delivery_notes.filter.booking_id.label';
}

class _LabelsDeliveryNotesFilterDeliveryNoteAmount {
  const _LabelsDeliveryNotesFilterDeliveryNoteAmount();

  final hint_text = 'delivery_notes.filter.delivery_note_amount.hint_text';
  final label = 'delivery_notes.filter.delivery_note_amount.label';
}

class _LabelsDeliveryNotesFilterDeliveryNoteId {
  const _LabelsDeliveryNotesFilterDeliveryNoteId();

  final hint_text = 'delivery_notes.filter.delivery_note_id.hint_text';
  final label = 'delivery_notes.filter.delivery_note_id.label';
}

class _LabelsDeliveryNotesFilterProductName {
  const _LabelsDeliveryNotesFilterProductName();

  final hint_text = 'delivery_notes.filter.product_name.hint_text';
  final label = 'delivery_notes.filter.product_name.label';
}

class _LabelsDeliveryNotesFilterReceivingConfirmedBy {
  const _LabelsDeliveryNotesFilterReceivingConfirmedBy();

  final hint_text = 'delivery_notes.filter.receiving_confirmed_by.hint_text';
  final label = 'delivery_notes.filter.receiving_confirmed_by.label';
}

class _LabelsDeliveryNotesFilterSorting {
  const _LabelsDeliveryNotesFilterSorting();

  final by_booking_id_asc = 'delivery_notes.filter.sorting.by_booking_id_asc';
  final by_booking_id_desc = 'delivery_notes.filter.sorting.by_booking_id_desc';
  final by_delivery_note_date_asc =
      'delivery_notes.filter.sorting.by_delivery_note_date_asc';
  final by_delivery_note_date_desc =
      'delivery_notes.filter.sorting.by_delivery_note_date_desc';
  final by_delivery_note_id_asc =
      'delivery_notes.filter.sorting.by_delivery_note_id_asc';
  final by_delivery_note_id_desc =
      'delivery_notes.filter.sorting.by_delivery_note_id_desc';
  final by_delivery_note_total_asc =
      'delivery_notes.filter.sorting.by_delivery_note_total_asc';
  final by_delivery_note_total_desc =
      'delivery_notes.filter.sorting.by_delivery_note_total_desc';
  final by_order_id_asc = 'delivery_notes.filter.sorting.by_order_id_asc';
  final by_order_id_desc = 'delivery_notes.filter.sorting.by_order_id_desc';
  final by_order_received_at_asc =
      'delivery_notes.filter.sorting.by_order_received_at_asc';
  final by_order_received_at_desc =
      'delivery_notes.filter.sorting.by_order_received_at_desc';
  final by_supplier_asc = 'delivery_notes.filter.sorting.by_supplier_asc';
  final by_supplier_desc = 'delivery_notes.filter.sorting.by_supplier_desc';
}

class _LabelsDeliveryNotesFilterSupplierName {
  const _LabelsDeliveryNotesFilterSupplierName();

  final hint_text = 'delivery_notes.filter.supplier_name.hint_text';
  final label = 'delivery_notes.filter.supplier_name.label';
}

class _LabelsDeliveryNotesMediusStatus {
  const _LabelsDeliveryNotesMediusStatus();

  final exporting = 'delivery_notes.medius_status.exporting';
  final mapping_error = 'delivery_notes.medius_status.mapping_error';
  final ok = 'delivery_notes.medius_status.ok';
  final server_error = 'delivery_notes.medius_status.server_error';
}

class _LabelsDeliveryNotesStatus {
  const _LabelsDeliveryNotesStatus();

  final delivery_note_disabled = 'delivery_notes.status.delivery_note_disabled';
  final delivery_note_enabled = 'delivery_notes.status.delivery_note_enabled';
  final in_approval = 'delivery_notes.status.in_approval';
}

class _LabelsDeliveryNotesTransactionType {
  const _LabelsDeliveryNotesTransactionType();

  final in_order_nos = 'delivery_notes.transaction_type.in_order_nos';
  final in_order_wso = 'delivery_notes.transaction_type.in_order_wso';
}

class _LabelsDialog {
  const _LabelsDialog();

  final discount = const _LabelsDialogDiscount();
  final logout_no = 'dialog.logout_no';
  final logout_yes = 'dialog.logout_yes';
  final signout_confirmation = 'dialog.signout_confirmation';
}

class _LabelsDialogDiscount {
  const _LabelsDialogDiscount();

  final flat_amount = 'dialog.discount.flat_amount';
  final label = 'dialog.discount.label';
  final percentage = 'dialog.discount.percentage';
}

class _LabelsError {
  const _LabelsError();

  final session_expire_message = 'error.session_expire_message';
  final session_expire_title = 'error.session_expire_title';
  final session_terminated_message = 'error.session_terminated_message';
}

class _LabelsFilter {
  const _LabelsFilter();

  final dates = const _LabelsFilterDates();
  final filtering = 'filter.filtering';
  final label = 'filter.label';
  final search = 'filter.search';
  final show_less = 'filter.show_less';
  final show_more = 'filter.show_more';
  final sort_by = 'filter.sort_by';
  final sorting = 'filter.sorting';
}

class _LabelsFilterDates {
  const _LabelsFilterDates();

  final select = 'filter.dates.select';
}

class _LabelsFl {
  const _LabelsFl();

  final AQUACULTURE_STEWARDSHIP_COUNCIL = 'fl.AQUACULTURE_STEWARDSHIP_COUNCIL';
  final AUSTRALIAN_HALAL_CERTIFICATION = 'fl.AUSTRALIAN_HALAL_CERTIFICATION';
  final BIO_ORGANIC_EU = 'fl.BIO_ORGANIC_EU';
  final BIO_ORGANIC_GERMANY = 'fl.BIO_ORGANIC_GERMANY';
  final BIO_ORGANIC_SUISSE = 'fl.BIO_ORGANIC_SUISSE';
  final FAIRTRADE_OESTERREICH = 'fl.FAIRTRADE_OESTERREICH';
  final FOREST_STEWARDSHIP_COUNCIL = 'fl.FOREST_STEWARDSHIP_COUNCIL';
  final HALAL_CERTIFICATION = 'fl.HALAL_CERTIFICATION';
  final MARINE_STEWARDSHIP_COUNCIL = 'fl.MARINE_STEWARDSHIP_COUNCIL';
  final PRODUCT_OF_AUSTRALIA = 'fl.PRODUCT_OF_AUSTRALIA';
  final PROGROS = 'fl.PROGROS';
  final RAINFOREST_ALLIANCE = 'fl.RAINFOREST_ALLIANCE';
  final UTZ_CERTIFIED = 'fl.UTZ_CERTIFIED';
  final VEGAN_AUSTRALIA_CERTIFIED = 'fl.VEGAN_AUSTRALIA_CERTIFIED';
}

class _LabelsForm {
  const _LabelsForm();

  final field = const _LabelsFormField();
}

class _LabelsFormField {
  const _LabelsFormField();

  final validation_error = const _LabelsFormFieldValidationError();
}

class _LabelsFormFieldValidationError {
  const _LabelsFormFieldValidationError();

  final empty = 'form.field.validation_error.empty';
  final negative_number = 'form.field.validation_error.negative_number';
  final non_numeric = 'form.field.validation_error.non_numeric';
  final non_whole_number = 'form.field.validation_error.non_whole_number';
  final numeric_too_big = 'form.field.validation_error.numeric_too_big';
  final numeric_too_small = 'form.field.validation_error.numeric_too_small';
  final too_many_decimal_places =
      'form.field.validation_error.too_many_decimal_places';
}

class _LabelsFormat {
  const _LabelsFormat();

  final date = 'format.date';
  final date_time = 'format.date_time';
  final time = 'format.time';
  final weekday = 'format.weekday';
}

class _LabelsFreeTextOrder {
  const _LabelsFreeTextOrder();

  final badge_label = 'free_text_order.badge_label';
}

class _LabelsHelpCenter {
  const _LabelsHelpCenter();

  final available_only_online = 'help_center.available_only_online';
}

class _LabelsHistory {
  const _LabelsHistory();

  final cart_item_deletion_label = 'history.cart_item_deletion_label';
  final deleted_at = 'history.deleted_at';
  final empty = 'history.empty';
  final item_restored_successfully_label =
      'history.item_restored_successfully_label';
  final product_restore_alert = const _LabelsHistoryProductRestoreAlert();
  final restore_button_label = 'history.restore_button_label';
  final sl_item_deletion_label = 'history.sl_item_deletion_label';
}

class _LabelsHistoryProductRestoreAlert {
  const _LabelsHistoryProductRestoreAlert();

  final content = 'history.product_restore_alert.content';
  final no_action = 'history.product_restore_alert.no_action';
  final yes_action = 'history.product_restore_alert.yes_action';
}

class _LabelsImage {
  const _LabelsImage();

  final available_only_online = 'image.available_only_online';
}

class _LabelsImageViewer {
  const _LabelsImageViewer();

  final title = 'image_viewer.title';
}

class _LabelsIms {
  const _LabelsIms();

  final expired_documents = const _LabelsImsExpiredDocuments();
  final ihl = const _LabelsImsIhl();
  final mapping_products = const _LabelsImsMappingProducts();
  final product = const _LabelsImsProduct();
  final store = const _LabelsImsStore();
  final stores = const _LabelsImsStores();
}

class _LabelsImsExpiredDocuments {
  const _LabelsImsExpiredDocuments();

  final is_private = 'ims.expired_documents.is_private';
  final no_files = 'ims.expired_documents.no_files';
  final search_available_only_online =
      'ims.expired_documents.search_available_only_online';
  final title = 'ims.expired_documents.title';
}

class _LabelsImsIhl {
  const _LabelsImsIhl();

  final items = const _LabelsImsIhlItems();
  final lookup_item_to_add = const _LabelsImsIhlLookupItemToAdd();
  final lookup_item_to_add_online_only =
      'ims.ihl.lookup_item_to_add_online_only';
}

class _LabelsImsIhlItems {
  const _LabelsImsIhlItems();

  final filter = const _LabelsImsIhlItemsFilter();
}

class _LabelsImsIhlItemsFilter {
  const _LabelsImsIhlItemsFilter();

  final sorting = const _LabelsImsIhlItemsFilterSorting();
}

class _LabelsImsIhlItemsFilterSorting {
  const _LabelsImsIhlItemsFilterSorting();

  final by_position_asc = 'ims.ihl.items.filter.sorting.by_position_asc';
  final by_position_desc = 'ims.ihl.items.filter.sorting.by_position_desc';
}

class _LabelsImsIhlLookupItemToAdd {
  const _LabelsImsIhlLookupItemToAdd();

  final action_button_label = 'ims.ihl.lookup_item_to_add.action_button_label';
  final already_in_list_message =
      'ims.ihl.lookup_item_to_add.already_in_list_message';
  final cant_be_add_message = 'ims.ihl.lookup_item_to_add.cant_be_add_message';
  final store_not_mapped_message =
      'ims.ihl.lookup_item_to_add.store_not_mapped_message';
  final title = 'ims.ihl.lookup_item_to_add.title';
}

class _LabelsImsMappingProducts {
  const _LabelsImsMappingProducts();

  final filter = const _LabelsImsMappingProductsFilter();
  final item_updated = 'ims.mapping_products.item_updated';
  final product = const _LabelsImsMappingProductsProduct();
  final search_available_only_online =
      'ims.mapping_products.search_available_only_online';
  final title = 'ims.mapping_products.title';
}

class _LabelsImsMappingProductsFilter {
  const _LabelsImsMappingProductsFilter();

  final sorting = const _LabelsImsMappingProductsFilterSorting();
}

class _LabelsImsMappingProductsFilterSorting {
  const _LabelsImsMappingProductsFilterSorting();

  final by_name_asc = 'ims.mapping_products.filter.sorting.by_name_asc';
  final by_name_desc = 'ims.mapping_products.filter.sorting.by_name_desc';
  final by_stock_item_id_asc =
      'ims.mapping_products.filter.sorting.by_stock_item_id_asc';
  final by_stock_item_id_desc =
      'ims.mapping_products.filter.sorting.by_stock_item_id_desc';
}

class _LabelsImsMappingProductsProduct {
  const _LabelsImsMappingProductsProduct();

  final category_label = 'ims.mapping_products.product.category_label';
  final cost_type_label = 'ims.mapping_products.product.cost_type_label';
  final inventory_unit_label =
      'ims.mapping_products.product.inventory_unit_label';
  final inventory_unit_price_label =
      'ims.mapping_products.product.inventory_unit_price_label';
  final item_name_label = 'ims.mapping_products.product.item_name_label';
  final stock_item_id_label =
      'ims.mapping_products.product.stock_item_id_label';
  final store_label = 'ims.mapping_products.product.store_label';
  final update_button_label =
      'ims.mapping_products.product.update_button_label';
  final vat_label = 'ims.mapping_products.product.vat_label';
}

class _LabelsImsProduct {
  const _LabelsImsProduct();

  final duplicate_ean = 'ims.product.duplicate_ean';
  final ean_label = 'ims.product.ean_label';
  final edit_eans_title = 'ims.product.edit_eans_title';
  final no_eans = 'ims.product.no_eans';
}

class _LabelsImsStore {
  const _LabelsImsStore();

  final cost_center_store = 'ims.store.cost_center_store';
  final details_tab = 'ims.store.details_tab';
  final edit_closed_store_warning = 'ims.store.edit_closed_store_warning';
  final last_update = 'ims.store.last_update';
  final main_info = 'ims.store.main_info';
  final months_for_inventories = 'ims.store.months_for_inventories';
  final months_updated_successfully = 'ims.store.months_updated_successfully';
  final offline_no_result_message = 'ims.store.offline_no_result_message';
  final order_list = 'ims.store.order_list';
  final order_list_last_update = 'ims.store.order_list_last_update';
  final products = const _LabelsImsStoreProducts();
  final products_tab = 'ims.store.products_tab';
  final store_section = 'ims.store.store_section';
  final title = 'ims.store.title';
}

class _LabelsImsStoreProducts {
  const _LabelsImsStoreProducts();

  final filter = const _LabelsImsStoreProductsFilter();
  final product = const _LabelsImsStoreProductsProduct();
  final search_available_only_online =
      'ims.store.products.search_available_only_online';
}

class _LabelsImsStoreProductsFilter {
  const _LabelsImsStoreProductsFilter();

  final sorting = const _LabelsImsStoreProductsFilterSorting();
}

class _LabelsImsStoreProductsFilterSorting {
  const _LabelsImsStoreProductsFilterSorting();

  final by_name_asc = 'ims.store.products.filter.sorting.by_name_asc';
  final by_name_desc = 'ims.store.products.filter.sorting.by_name_desc';
  final by_position = 'ims.store.products.filter.sorting.by_position';
  final by_price_asc = 'ims.store.products.filter.sorting.by_price_asc';
  final by_price_desc = 'ims.store.products.filter.sorting.by_price_desc';
  final by_store_asc = 'ims.store.products.filter.sorting.by_store_asc';
  final by_store_desc = 'ims.store.products.filter.sorting.by_store_desc';
}

class _LabelsImsStoreProductsProduct {
  const _LabelsImsStoreProductsProduct();

  final cannot_bee_deleted = 'ims.store.products.product.cannot_bee_deleted';
  final category_label = 'ims.store.products.product.category_label';
  final cost_center_label = 'ims.store.products.product.cost_center_label';
  final cost_type_label = 'ims.store.products.product.cost_type_label';
  final delete_alert_content =
      'ims.store.products.product.delete_alert_content';
  final delete_alert_no_action =
      'ims.store.products.product.delete_alert_no_action';
  final delete_alert_yes_action =
      'ims.store.products.product.delete_alert_yes_action';
  final deleted_successfully =
      'ims.store.products.product.deleted_successfully';
  final edit_eans = 'ims.store.products.product.edit_eans';
  final id_label = 'ims.store.products.product.id_label';
  final inventory_unit_label =
      'ims.store.products.product.inventory_unit_label';
  final min_max_store_label = 'ims.store.products.product.min_max_store_label';
  final price_label = 'ims.store.products.product.price_label';
  final store_label = 'ims.store.products.product.store_label';
}

class _LabelsImsStores {
  const _LabelsImsStores();

  final filter = const _LabelsImsStoresFilter();
  final search_available_only_online =
      'ims.stores.search_available_only_online';
}

class _LabelsImsStoresFilter {
  const _LabelsImsStoresFilter();

  final sorting = const _LabelsImsStoresFilterSorting();
}

class _LabelsImsStoresFilterSorting {
  const _LabelsImsStoresFilterSorting();

  final by_store_name_asc = 'ims.stores.filter.sorting.by_store_name_asc';
  final by_store_name_desc = 'ims.stores.filter.sorting.by_store_name_desc';
  final highest_store_first = 'ims.stores.filter.sorting.highest_store_first';
  final lowest_store_first = 'ims.stores.filter.sorting.lowest_store_first';
}

class _LabelsInfo {
  const _LabelsInfo();

  final delete_local_data_confirmation = 'info.delete_local_data_confirmation';
  final delete_local_data_confirmation_no =
      'info.delete_local_data_confirmation_no';
  final delete_local_data_confirmation_yes =
      'info.delete_local_data_confirmation_yes';
  final ean_scanner_error = 'info.ean_scanner_error';
  final ean_scanner_permission_error = 'info.ean_scanner_permission_error';
  final image_not_loaded = 'info.image_not_loaded';
  final no_changes = 'info.no_changes';
  final no_network_connection = 'info.no_network_connection';
  final online_edit_only = 'info.online_edit_only';
  final online_mode_only = 'info.online_mode_only';
  final pdf_document_not_loaded = 'info.pdf_document_not_loaded';
  final please_fill_all_fields = 'info.please_fill_all_fields';
  final reset_to_default_theme_confirmation =
      'info.reset_to_default_theme_confirmation';
  final saved_successfully = 'info.saved_successfully';
  final theme_reset_confirmation_no = 'info.theme_reset_confirmation_no';
  final theme_reset_confirmation_yes = 'info.theme_reset_confirmation_yes';
  final user_have_no_cost_centers = 'info.user_have_no_cost_centers';
  final you_have_no_access = 'info.you_have_no_access';
}

class _LabelsInhouseList {
  const _LabelsInhouseList();

  final item_added_successfully_label =
      'inhouse_list.item_added_successfully_label';
  final search_available_only_online =
      'inhouse_list.search_available_only_online';
}

class _LabelsInhouseLists {
  const _LabelsInhouseLists();

  final inhouse_list = const _LabelsInhouseListsInhouseList();
  final inv_unit_price = 'inhouse_lists.inv_unit_price';
  final local = const _LabelsInhouseListsLocal();
  final requested_amount = 'inhouse_lists.requested_amount';
  final sort = const _LabelsInhouseListsSort();
  final title = 'inhouse_lists.title';
}

class _LabelsInhouseListsInhouseList {
  const _LabelsInhouseListsInhouseList();

  final local = const _LabelsInhouseListsInhouseListLocal();
  final product = const _LabelsInhouseListsInhouseListProduct();
  final send_request_button = 'inhouse_lists.inhouse_list.send_request_button';
  final update_product_label =
      'inhouse_lists.inhouse_list.update_product_label';
}

class _LabelsInhouseListsInhouseListLocal {
  const _LabelsInhouseListsInhouseListLocal();

  final last_updated_at_label =
      'inhouse_lists.inhouse_list.local.last_updated_at_label';
}

class _LabelsInhouseListsInhouseListProduct {
  const _LabelsInhouseListsInhouseListProduct();

  final all_fields_are_required_message =
      'inhouse_lists.inhouse_list.product.all_fields_are_required_message';
  final incoming_cost_type_label =
      'inhouse_lists.inhouse_list.product.incoming_cost_type_label';
  final product_update =
      const _LabelsInhouseListsInhouseListProductProductUpdate();
  final quantity_add_label =
      'inhouse_lists.inhouse_list.product.quantity_add_label';
  final quantity_edit_label =
      'inhouse_lists.inhouse_list.product.quantity_edit_label';
  final quantity_field_label =
      'inhouse_lists.inhouse_list.product.quantity_field_label';
  final quantity_update_label =
      'inhouse_lists.inhouse_list.product.quantity_update_label';
  final select_source_stock =
      'inhouse_lists.inhouse_list.product.select_source_stock';
  final source_stock_label =
      'inhouse_lists.inhouse_list.product.source_stock_label';
  final target_stock_label =
      'inhouse_lists.inhouse_list.product.target_stock_label';
  final update_action_button =
      'inhouse_lists.inhouse_list.product.update_action_button';
  final update_success = 'inhouse_lists.inhouse_list.product.update_success';
}

class _LabelsInhouseListsInhouseListProductProductUpdate {
  const _LabelsInhouseListsInhouseListProductProductUpdate();

  final title = 'inhouse_lists.inhouse_list.product.product_update.title';
}

class _LabelsInhouseListsLocal {
  const _LabelsInhouseListsLocal();

  final last_updated_at_label = 'inhouse_lists.local.last_updated_at_label';
  final load_button_label = 'inhouse_lists.local.load_button_label';
  final load_successfully = 'inhouse_lists.local.load_successfully';
  final loaded_count = 'inhouse_lists.local.loaded_count';
  final loading_lists = 'inhouse_lists.local.loading_lists';
  final no_lists_in_cost_center = 'inhouse_lists.local.no_lists_in_cost_center';
  final no_lists_saved = 'inhouse_lists.local.no_lists_saved';
  final open_button_label = 'inhouse_lists.local.open_button_label';
  final product = const _LabelsInhouseListsLocalProduct();
  final title = 'inhouse_lists.local.title';
  final update = const _LabelsInhouseListsLocalUpdate();
  final update_button_label = 'inhouse_lists.local.update_button_label';
}

class _LabelsInhouseListsLocalProduct {
  const _LabelsInhouseListsLocalProduct();

  final quantity_updated = 'inhouse_lists.local.product.quantity_updated';
}

class _LabelsInhouseListsLocalUpdate {
  const _LabelsInhouseListsLocalUpdate();

  final alert = const _LabelsInhouseListsLocalUpdateAlert();
}

class _LabelsInhouseListsLocalUpdateAlert {
  const _LabelsInhouseListsLocalUpdateAlert();

  final content = 'inhouse_lists.local.update.alert.content';
  final no_action = 'inhouse_lists.local.update.alert.no_action';
  final yes_action = 'inhouse_lists.local.update.alert.yes_action';
}

class _LabelsInhouseListsSort {
  const _LabelsInhouseListsSort();

  final a_z = 'inhouse_lists.sort.a_z';
  final z_a = 'inhouse_lists.sort.z_a';
}

class _LabelsInit {
  const _LabelsInit();

  final restart_without_data_saving = 'init.restart_without_data_saving';
}

class _LabelsInventory {
  const _LabelsInventory();

  final confirmation = const _LabelsInventoryConfirmation();
  final eans_edit = const _LabelsInventoryEansEdit();
  final filter = const _LabelsInventoryFilter();
  final item = const _LabelsInventoryItem();
  final items = const _LabelsInventoryItems();
  final items_sync_in_progress = 'inventory.items_sync_in_progress';
  final list = const _LabelsInventoryList();
  final list_sync_after_offline_confirmation_label =
      'inventory.list_sync_after_offline_confirmation_label';
  final list_sync_confirmation_no = 'inventory.list_sync_confirmation_no';
  final list_sync_confirmation_yes = 'inventory.list_sync_confirmation_yes';
  final no_lists_found = 'inventory.no_lists_found';
  final no_open_local_inventory_lists_label =
      'inventory.no_open_local_inventory_lists_label';
  final offline_inventories = const _LabelsInventoryOfflineInventories();
  final open_locally_saved_lists = 'inventory.open_locally_saved_lists';
  final search_in_offline_mode_is_not_working =
      'inventory.search_in_offline_mode_is_not_working';
  final select_lists_to_send = 'inventory.select_lists_to_send';
  final send_button = const _LabelsInventorySendButton();
  final signature = const _LabelsInventorySignature();
  final sync = const _LabelsInventorySync();
  final tap_to_sync_label = 'inventory.tap_to_sync_label';
  final warehouse_item_added_successfully =
      'inventory.warehouse_item_added_successfully';
}

class _LabelsInventoryConfirmation {
  const _LabelsInventoryConfirmation();

  final no_lists_to_send = 'inventory.confirmation.no_lists_to_send';
}

class _LabelsInventoryEansEdit {
  const _LabelsInventoryEansEdit();

  final ean_already_in_list = 'inventory.eans_edit.ean_already_in_list';
  final no_eans_label = 'inventory.eans_edit.no_eans_label';
  final save_button = const _LabelsInventoryEansEditSaveButton();
}

class _LabelsInventoryEansEditSaveButton {
  const _LabelsInventoryEansEditSaveButton();

  final label = 'inventory.eans_edit.save_button.label';
}

class _LabelsInventoryFilter {
  const _LabelsInventoryFilter();

  final sorting = const _LabelsInventoryFilterSorting();
  final status = const _LabelsInventoryFilterStatus();
  final status_label = 'inventory.filter.status_label';
}

class _LabelsInventoryFilterSorting {
  const _LabelsInventoryFilterSorting();

  final by_locked_at_asc = 'inventory.filter.sorting.by_locked_at_asc';
  final by_locked_at_desc = 'inventory.filter.sorting.by_locked_at_desc';
}

class _LabelsInventoryFilterStatus {
  const _LabelsInventoryFilterStatus();

  final all = 'inventory.filter.status.all';
  final booked = 'inventory.filter.status.booked';
  final closed = 'inventory.filter.status.closed';
  final locked_to_me = 'inventory.filter.status.locked_to_me';
  final not_transferred = 'inventory.filter.status.not_transferred';
  final open = 'inventory.filter.status.open';
  final signed = 'inventory.filter.status.signed';
  final transferred = 'inventory.filter.status.transferred';
}

class _LabelsInventoryItem {
  const _LabelsInventoryItem();

  final cancel_quantity_button_label =
      'inventory.item.cancel_quantity_button_label';
  final current_quantity = 'inventory.item.current_quantity';
  final eans_button_label = 'inventory.item.eans_button_label';
  final eans_updated_successfully = 'inventory.item.eans_updated_successfully';
  final edit_button_label = 'inventory.item.edit_button_label';
  final inventory_unit = 'inventory.item.inventory_unit';
  final quantity = 'inventory.item.quantity';
  final update_quantity_button_label =
      'inventory.item.update_quantity_button_label';
}

class _LabelsInventoryItems {
  const _LabelsInventoryItems();

  final filter = const _LabelsInventoryItemsFilter();
}

class _LabelsInventoryItemsFilter {
  const _LabelsInventoryItemsFilter();

  final category = const _LabelsInventoryItemsFilterCategory();
  final category_label = 'inventory.items.filter.category_label';
  final sorting = const _LabelsInventoryItemsFilterSorting();
  final status = const _LabelsInventoryItemsFilterStatus();
  final status_label = 'inventory.items.filter.status_label';
}

class _LabelsInventoryItemsFilterCategory {
  const _LabelsInventoryItemsFilterCategory();

  final all = 'inventory.items.filter.category.all';
}

class _LabelsInventoryItemsFilterSorting {
  const _LabelsInventoryItemsFilterSorting();

  final by_name_asc = 'inventory.items.filter.sorting.by_name_asc';
  final by_name_desc = 'inventory.items.filter.sorting.by_name_desc';
  final by_position_asc = 'inventory.items.filter.sorting.by_position_asc';
  final by_position_desc = 'inventory.items.filter.sorting.by_position_desc';
}

class _LabelsInventoryItemsFilterStatus {
  const _LabelsInventoryItemsFilterStatus();

  final all = 'inventory.items.filter.status.all';
  final counted = 'inventory.items.filter.status.counted';
  final not_counted = 'inventory.items.filter.status.not_counted';
}

class _LabelsInventoryList {
  const _LabelsInventoryList();

  final add_item_button_label = 'inventory.list.add_item_button_label';
  final already_locked_by_label__named =
      'inventory.list.already_locked_by_label__named';
  final approve_button_label = 'inventory.list.approve_button_label';
  final approve_menu_item_label = 'inventory.list.approve_menu_item_label';
  final approved_successfully = 'inventory.list.approved_successfully';
  final booking_voucher_button_label =
      'inventory.list.booking_voucher_button_label';
  final closed_offline_message = 'inventory.list.closed_offline_message';
  final force_unlock_alert = const _LabelsInventoryListForceUnlockAlert();
  final force_unlock_button_label = 'inventory.list.force_unlock_button_label';
  final item_not_found_by_ean = const _LabelsInventoryListItemNotFoundByEan();
  final items_captured_label = 'inventory.list.items_captured_label';
  final lock_alert = const _LabelsInventoryListLockAlert();
  final offline_label = 'inventory.list.offline_label';
  final status = const _LabelsInventoryListStatus();
  final unlock_alert = const _LabelsInventoryListUnlockAlert();
  final unlock_button_label = 'inventory.list.unlock_button_label';
  final unlock_menu_item_label = 'inventory.list.unlock_menu_item_label';
  final unlocked_successfully = 'inventory.list.unlocked_successfully';
  final view_list_difference_report_button_label =
      'inventory.list.view_list_difference_report_button_label';
  final view_list_intake_report_button_label =
      'inventory.list.view_list_intake_report_button_label';
  final view_qr_code_pdf_button_label =
      'inventory.list.view_qr_code_pdf_button_label';
}

class _LabelsInventoryListForceUnlockAlert {
  const _LabelsInventoryListForceUnlockAlert();

  final cancel_action = 'inventory.list.force_unlock_alert.cancel_action';
  final confirm_action = 'inventory.list.force_unlock_alert.confirm_action';
  final content = 'inventory.list.force_unlock_alert.content';
}

class _LabelsInventoryListItemNotFoundByEan {
  const _LabelsInventoryListItemNotFoundByEan();

  final cancel_scanning =
      'inventory.list.item_not_found_by_ean.cancel_scanning';
  final message = 'inventory.list.item_not_found_by_ean.message';
  final scan_again = 'inventory.list.item_not_found_by_ean.scan_again';
  final title = 'inventory.list.item_not_found_by_ean.title';
}

class _LabelsInventoryListLockAlert {
  const _LabelsInventoryListLockAlert();

  final cancel_action = 'inventory.list.lock_alert.cancel_action';
  final confirm_action = 'inventory.list.lock_alert.confirm_action';
  final content = 'inventory.list.lock_alert.content';
}

class _LabelsInventoryListStatus {
  const _LabelsInventoryListStatus();

  final closed = 'inventory.list.status.closed';
  final locked_by_another_user = 'inventory.list.status.locked_by_another_user';
}

class _LabelsInventoryListUnlockAlert {
  const _LabelsInventoryListUnlockAlert();

  final cancel_action = 'inventory.list.unlock_alert.cancel_action';
  final confirm_action = 'inventory.list.unlock_alert.confirm_action';
  final content = 'inventory.list.unlock_alert.content';
}

class _LabelsInventoryOfflineInventories {
  const _LabelsInventoryOfflineInventories();

  final title = 'inventory.offline_inventories.title';
}

class _LabelsInventorySendButton {
  const _LabelsInventorySendButton();

  final label = 'inventory.send_button.label';
}

class _LabelsInventorySignature {
  const _LabelsInventorySignature();

  final clear_button_label = 'inventory.signature.clear_button_label';
  final continue_button_label = 'inventory.signature.continue_button_label';
  final enter_name = 'inventory.signature.enter_name';
  final please_sign_with_finger = 'inventory.signature.please_sign_with_finger';
  final signature_examiner_title__named =
      'inventory.signature.signature_examiner_title__named';
  final skip_button_label = 'inventory.signature.skip_button_label';
}

class _LabelsInventorySync {
  const _LabelsInventorySync();

  final list_synced_successfully = 'inventory.sync.list_synced_successfully';
  final sync_now_button_label = 'inventory.sync.sync_now_button_label';
  final sync_required_label = 'inventory.sync.sync_required_label';
}

class _LabelsInvoices {
  const _LabelsInvoices();

  final document_scanner = const _LabelsInvoicesDocumentScanner();
  final scanned = const _LabelsInvoicesScanned();
  final tax_breakdown = const _LabelsInvoicesTaxBreakdown();
  final type = const _LabelsInvoicesType();
}

class _LabelsInvoicesDocumentScanner {
  const _LabelsInvoicesDocumentScanner();

  final crop_black_white_label =
      'invoices.document_scanner.crop_black_white_label';
  final crop_label = 'invoices.document_scanner.crop_label';
  final crop_reset_label = 'invoices.document_scanner.crop_reset_label';
  final scan_label = 'invoices.document_scanner.scan_label';
}

class _LabelsInvoicesScanned {
  const _LabelsInvoicesScanned();

  final invoice = const _LabelsInvoicesScannedInvoice();
  final no_scanned_invoices = 'invoices.scanned.no_scanned_invoices';
  final no_scanned_invoices_offline =
      'invoices.scanned.no_scanned_invoices_offline';
  final no_scanned_pages = 'invoices.scanned.no_scanned_pages';
  final one_a_time_warning = 'invoices.scanned.one_a_time_warning';
  final page = const _LabelsInvoicesScannedPage();
  final page_delete_confirmation = 'invoices.scanned.page_delete_confirmation';
  final page_delete_confirmation_no_action =
      'invoices.scanned.page_delete_confirmation_no_action';
  final page_delete_confirmation_yes_action =
      'invoices.scanned.page_delete_confirmation_yes_action';
  final pages = const _LabelsInvoicesScannedPages();
  final processing_status = const _LabelsInvoicesScannedProcessingStatus();
  final scan = 'invoices.scanned.scan';
}

class _LabelsInvoicesScannedInvoice {
  const _LabelsInvoicesScannedInvoice();

  final attach_button_label = 'invoices.scanned.invoice.attach_button_label';
  final document_unavailable = 'invoices.scanned.invoice.document_unavailable';
  final has_no_pages_label = 'invoices.scanned.invoice.has_no_pages_label';
  final invoice_id_label = 'invoices.scanned.invoice.invoice_id_label';
  final scan_button_label = 'invoices.scanned.invoice.scan_button_label';
  final scanning_id_label = 'invoices.scanned.invoice.scanning_id_label';
  final upload_alert = const _LabelsInvoicesScannedInvoiceUploadAlert();
  final upload_button_label = 'invoices.scanned.invoice.upload_button_label';
  final uploaded_successfully_label =
      'invoices.scanned.invoice.uploaded_successfully_label';
  final web_info = const _LabelsInvoicesScannedInvoiceWebInfo();
}

class _LabelsInvoicesScannedInvoiceUploadAlert {
  const _LabelsInvoicesScannedInvoiceUploadAlert();

  final no_action = 'invoices.scanned.invoice.upload_alert.no_action';
  final title = 'invoices.scanned.invoice.upload_alert.title';
  final yes_action = 'invoices.scanned.invoice.upload_alert.yes_action';
}

class _LabelsInvoicesScannedInvoiceWebInfo {
  const _LabelsInvoicesScannedInvoiceWebInfo();

  final description = const _LabelsInvoicesScannedInvoiceWebInfoDescription();
  final title = 'invoices.scanned.invoice.web_info.title';
}

class _LabelsInvoicesScannedInvoiceWebInfoDescription {
  const _LabelsInvoicesScannedInvoiceWebInfoDescription();

  final deleted = 'invoices.scanned.invoice.web_info.description.deleted';
  final doubled = 'invoices.scanned.invoice.web_info.description.doubled';
  final error = 'invoices.scanned.invoice.web_info.description.error';
  final ok = 'invoices.scanned.invoice.web_info.description.ok';
  final overdue = 'invoices.scanned.invoice.web_info.description.overdue';
  final unknown = 'invoices.scanned.invoice.web_info.description.unknown';
}

class _LabelsInvoicesScannedPage {
  const _LabelsInvoicesScannedPage();

  final count = 'invoices.scanned.page.count';
  final edit = const _LabelsInvoicesScannedPageEdit();
  final position = 'invoices.scanned.page.position';
  final title = 'invoices.scanned.page.title';
}

class _LabelsInvoicesScannedPageEdit {
  const _LabelsInvoicesScannedPageEdit();

  final save = 'invoices.scanned.page.edit.save';
  final title = 'invoices.scanned.page.edit.title';
}

class _LabelsInvoicesScannedPages {
  const _LabelsInvoicesScannedPages();

  final count = 'invoices.scanned.pages.count';
}

class _LabelsInvoicesScannedProcessingStatus {
  const _LabelsInvoicesScannedProcessingStatus();

  final extended = const _LabelsInvoicesScannedProcessingStatusExtended();
  final short = const _LabelsInvoicesScannedProcessingStatusShort();
}

class _LabelsInvoicesScannedProcessingStatusExtended {
  const _LabelsInvoicesScannedProcessingStatusExtended();

  final deleted = 'invoices.scanned.processing_status.extended.deleted';
  final doubled = 'invoices.scanned.processing_status.extended.doubled';
  final error = 'invoices.scanned.processing_status.extended.error';
  final ok = 'invoices.scanned.processing_status.extended.ok';
  final overdue = 'invoices.scanned.processing_status.extended.overdue';
  final processing = 'invoices.scanned.processing_status.extended.processing';
  final unknown = 'invoices.scanned.processing_status.extended.unknown';
}

class _LabelsInvoicesScannedProcessingStatusShort {
  const _LabelsInvoicesScannedProcessingStatusShort();

  final deleted = 'invoices.scanned.processing_status.short.deleted';
  final doubled = 'invoices.scanned.processing_status.short.doubled';
  final error = 'invoices.scanned.processing_status.short.error';
  final ok = 'invoices.scanned.processing_status.short.ok';
  final overdue = 'invoices.scanned.processing_status.short.overdue';
  final processing = 'invoices.scanned.processing_status.short.processing';
  final unknown = 'invoices.scanned.processing_status.short.unknown';
}

class _LabelsInvoicesTaxBreakdown {
  const _LabelsInvoicesTaxBreakdown();

  final amount_gross = 'invoices.tax_breakdown.amount_gross';
  final amount_net = 'invoices.tax_breakdown.amount_net';
  final title = 'invoices.tax_breakdown.title';
  final total_tax = 'invoices.tax_breakdown.total_tax';
}

class _LabelsInvoicesType {
  const _LabelsInvoicesType();

  final credit_note = 'invoices.type.credit_note';
  final delivery_note_not_found = 'invoices.type.delivery_note_not_found';
  final with_delivery_note = 'invoices.type.with_delivery_note';
  final without_delivery_note = 'invoices.type.without_delivery_note';
}

class _LabelsInvoicesArchive {
  const _LabelsInvoicesArchive();

  final activity_log_tab = 'invoices_archive.activity_log_tab';
  final attachments_tab = 'invoices_archive.attachments_tab';
  final details_tab = 'invoices_archive.details_tab';
  final filter = const _LabelsInvoicesArchiveFilter();
  final invoice = const _LabelsInvoicesArchiveInvoice();
  final search_available_only_online =
      'invoices_archive.search_available_only_online';
}

class _LabelsInvoicesArchiveFilter {
  const _LabelsInvoicesArchiveFilter();

  final sorting = const _LabelsInvoicesArchiveFilterSorting();
  final status = const _LabelsInvoicesArchiveFilterStatus();
  final status_label = 'invoices_archive.filter.status_label';
}

class _LabelsInvoicesArchiveFilterSorting {
  const _LabelsInvoicesArchiveFilterSorting();

  final by_invoice_date_asc =
      'invoices_archive.filter.sorting.by_invoice_date_asc';
  final by_invoice_date_desc =
      'invoices_archive.filter.sorting.by_invoice_date_desc';
  final by_supplier_name_asc =
      'invoices_archive.filter.sorting.by_supplier_name_asc';
  final by_supplier_name_desc =
      'invoices_archive.filter.sorting.by_supplier_name_desc';
}

class _LabelsInvoicesArchiveFilterStatus {
  const _LabelsInvoicesArchiveFilterStatus();

  final assigned_to_me = 'invoices_archive.filter.status.assigned_to_me';
  final date_range = 'invoices_archive.filter.status.date_range';
  final in_approval = 'invoices_archive.filter.status.in_approval';
  final open = 'invoices_archive.filter.status.open';
  final ready_to_approve = 'invoices_archive.filter.status.ready_to_approve';
  final ready_to_transfer_to_accounting =
      'invoices_archive.filter.status.ready_to_transfer_to_accounting';
  final sepa_payment = 'invoices_archive.filter.status.sepa_payment';
}

class _LabelsInvoicesArchiveInvoice {
  const _LabelsInvoicesArchiveInvoice();

  final accounting_assignment_information_title =
      'invoices_archive.invoice.accounting_assignment_information_title';
  final accounting_instruction =
      'invoices_archive.invoice.accounting_instruction';
  final amount_gross = 'invoices_archive.invoice.amount_gross';
  final amount_net = 'invoices_archive.invoice.amount_net';
  final booking_date = 'invoices_archive.invoice.booking_date';
  final cost_center = 'invoices_archive.invoice.cost_center';
  final cost_type = 'invoices_archive.invoice.cost_type';
  final division = 'invoices_archive.invoice.division';
  final id = 'invoices_archive.invoice.id';
  final information_title = 'invoices_archive.invoice.information_title';
  final invoice_comment = 'invoices_archive.invoice.invoice_comment';
  final invoice_date = 'invoices_archive.invoice.invoice_date';
  final no_account_assignment_records =
      'invoices_archive.invoice.no_account_assignment_records';
  final requested_by_user = 'invoices_archive.invoice.requested_by_user';
  final supplier = 'invoices_archive.invoice.supplier';
  final tax_amount = 'invoices_archive.invoice.tax_amount';
  final view_pdf_button_label =
      'invoices_archive.invoice.view_pdf_button_label';
}

class _LabelsLanguage {
  const _LabelsLanguage();

  final de_de = 'language.de_de';
  final de_de_en_name = 'language.de_de_en_name';
  final en_gb = 'language.en_gb';
  final en_gb_en_name = 'language.en_gb_en_name';
  final es_es = 'language.es_es';
  final es_es_en_name = 'language.es_es_en_name';
  final fr_fr = 'language.fr_fr';
  final fr_fr_en_name = 'language.fr_fr_en_name';
  final hu_hu = 'language.hu_hu';
  final hu_hu_en_name = 'language.hu_hu_en_name';
  final it_it = 'language.it_it';
  final it_it_en_name = 'language.it_it_en_name';
  final ja_jp = 'language.ja_jp';
  final ja_jp_en_name = 'language.ja_jp_en_name';
  final nl_nl = 'language.nl_nl';
  final nl_nl_en_name = 'language.nl_nl_en_name';
  final pt_pt = 'language.pt_pt';
  final pt_pt_en_name = 'language.pt_pt_en_name';
  final th_th = 'language.th_th';
  final th_th_en_name = 'language.th_th_en_name';
  final tr_tr = 'language.tr_tr';
  final tr_tr_en_name = 'language.tr_tr_en_name';
  final vi_vn = 'language.vi_vn';
  final vi_vn_en_name = 'language.vi_vn_en_name';
  final zh_cn = 'language.zh_cn';
  final zh_cn_en_name = 'language.zh_cn_en_name';
  final zh_hk = 'language.zh_hk';
  final zh_hk_en_name = 'language.zh_hk_en_name';
}

class _LabelsLicenses {
  const _LabelsLicenses();

  final count__named = 'licenses.count__named';
  final open_screen_button_label = 'licenses.open_screen_button_label';
  final title = 'licenses.title';
}

class _LabelsLists {
  const _LabelsLists();

  final error_during_update = 'lists.error_during_update';
  final name_has_been_updated = 'lists.name_has_been_updated';
}

class _LabelsLocalCart {
  const _LabelsLocalCart();

  final empty_label = 'local_cart.empty_label';
  final processing_products = 'local_cart.processing_products';
  final product = const _LabelsLocalCartProduct();
  final products_processed = 'local_cart.products_processed';
  final upload_alert = const _LabelsLocalCartUploadAlert();
  final upload_button = 'local_cart.upload_button';
  final uploaded_partially = 'local_cart.uploaded_partially';
  final uploaded_successfully = 'local_cart.uploaded_successfully';
  final uploaded_view_log_button_label =
      'local_cart.uploaded_view_log_button_label';
}

class _LabelsLocalCartProduct {
  const _LabelsLocalCartProduct();

  final content_units_per_order_unit =
      'local_cart.product.content_units_per_order_unit';
  final delete_alert = const _LabelsLocalCartProductDeleteAlert();
  final deleted_successfully = 'local_cart.product.deleted_successfully';
  final details_button_label = 'local_cart.product.details_button_label';
  final origin = 'local_cart.product.origin';
  final qty_updated_successfully =
      'local_cart.product.qty_updated_successfully';
  final quantity = 'local_cart.product.quantity';
  final supplier = 'local_cart.product.supplier';
  final total = 'local_cart.product.total';
}

class _LabelsLocalCartProductDeleteAlert {
  const _LabelsLocalCartProductDeleteAlert();

  final content = 'local_cart.product.delete_alert.content';
  final no_action = 'local_cart.product.delete_alert.no_action';
  final yes_action = 'local_cart.product.delete_alert.yes_action';
}

class _LabelsLocalCartUploadAlert {
  const _LabelsLocalCartUploadAlert();

  final content = 'local_cart.upload_alert.content';
  final no_action = 'local_cart.upload_alert.no_action';
  final yes_action = 'local_cart.upload_alert.yes_action';
}

class _LabelsLogin {
  const _LabelsLogin();

  final cost_center_lookup = const _LabelsLoginCostCenterLookup();
  final create_pin_phrase = 'login.create_pin_phrase';
  final enter_pin_code = 'login.enter_pin_code';
  final forgot_password = 'login.forgot_password';
  final forgot_pin_code = 'login.forgot_pin_code';
  final info = 'login.info';
  final log_in = 'login.log_in';
  final login_or_email_placeholder = 'login.login_or_email_placeholder';
  final login_placeholder = 'login.login_placeholder';
  final new_password_placeholder = 'login.new_password_placeholder';
  final no_connection = 'login.no_connection';
  final or = 'login.or';
  final password_placeholder = 'login.password_placeholder';
  final password_recovery_continue_button_label =
      'login.password_recovery_continue_button_label';
  final password_recovery_hint = 'login.password_recovery_hint';
  final password_recovery_hint_accept_button_label =
      'login.password_recovery_hint_accept_button_label';
  final password_recovery_login_button_label =
      'login.password_recovery_login_button_label';
  final password_recovery_phrase = 'login.password_recovery_phrase';
  final password_recovery_success_header =
      'login.password_recovery_success_header';
  final password_recovery_success_message =
      'login.password_recovery_success_message';
  final password_recovery_title = 'login.password_recovery_title';
  final password_reset_continue_button_label =
      'login.password_reset_continue_button_label';
  final password_reset_login_button_label =
      'login.password_reset_login_button_label';
  final password_reset_success_header = 'login.password_reset_success_header';
  final password_reset_success_message = 'login.password_reset_success_message';
  final password_reset_title = 'login.password_reset_title';
  final password_reset_validation = const _LabelsLoginPasswordResetValidation();
  final retype_pin_phrase = 'login.retype_pin_phrase';
  final sso = const _LabelsLoginSso();
  final sso_login = 'login.sso_login';
  final two_factor_auth_code_has_been_sent =
      'login.two_factor_auth_code_has_been_sent';
  final two_factor_auth_code_have_not_received_it =
      'login.two_factor_auth_code_have_not_received_it';
  final two_factor_auth_code_it_may_take_a_minute =
      'login.two_factor_auth_code_it_may_take_a_minute';
  final two_factor_auth_placeholder = 'login.two_factor_auth_placeholder';
  final two_factor_auth_resend_a_new_code =
      'login.two_factor_auth_resend_a_new_code';
  final two_factor_auth_seconds_to_resend_code =
      'login.two_factor_auth_seconds_to_resend_code';
  final two_factor_auth_verify_button_label =
      'login.two_factor_auth_verify_button_label';
  final two_factor_authentication_code_valid_for =
      'login.two_factor_authentication_code_valid_for';
  final two_factor_authentication_title =
      'login.two_factor_authentication_title';
  final wrong_pin_message__named = 'login.wrong_pin_message__named';
}

class _LabelsLoginCostCenterLookup {
  const _LabelsLoginCostCenterLookup();

  final title = 'login.cost_center_lookup.title';
}

class _LabelsLoginPasswordResetValidation {
  const _LabelsLoginPasswordResetValidation();

  final contains_number_or_symbol =
      'login.password_reset_validation.contains_number_or_symbol';
  final length = 'login.password_reset_validation.length';
  final lowercase = 'login.password_reset_validation.lowercase';
  final not_start_with_number =
      'login.password_reset_validation.not_start_with_number';
  final uppercase = 'login.password_reset_validation.uppercase';
}

class _LabelsLoginSso {
  const _LabelsLoginSso();

  final continue_btn = 'login.sso.continue_btn';
  final external_provider_error = 'login.sso.external_provider_error';
  final hotel_name_hint = 'login.sso.hotel_name_hint';
  final hotel_name_label = 'login.sso.hotel_name_label';
  final title = 'login.sso.title';
}

class _LabelsMeasurementTaking {
  const _LabelsMeasurementTaking();

  final bluetooth_device_not_connected_label =
      'measurement_taking.bluetooth_device_not_connected_label';
  final enter_manually_button_label =
      'measurement_taking.enter_manually_button_label';
  final lost_connection_to_the_device_label =
      'measurement_taking.lost_connection_to_the_device_label';
  final save_button_label = 'measurement_taking.save_button_label';
  final save_measurement_button_label =
      'measurement_taking.save_measurement_button_label';
  final select_device_label = 'measurement_taking.select_device_label';
  final switch_to_manual_mode_button_label =
      'measurement_taking.switch_to_manual_mode_button_label';
  final take_measurement_label = 'measurement_taking.take_measurement_label';
  final temperature_label = 'measurement_taking.temperature_label';
  final weight_label = 'measurement_taking.weight_label';
}

class _LabelsNavigation {
  const _LabelsNavigation();

  final approval_center_section = 'navigation.approval_center_section';
  final capex_approval_requests = 'navigation.capex_approval_requests';
  final cart = 'navigation.cart';
  final catalog = 'navigation.catalog';
  final dashboard = 'navigation.dashboard';
  final delivery_notes = 'navigation.delivery_notes';
  final help_center = 'navigation.help_center';
  final imprint_and_legal_notice = 'navigation.imprint_and_legal_notice';
  final incomplete_bookings = 'navigation.incomplete_bookings';
  final inventories = 'navigation.inventories';
  final invoices = 'navigation.invoices';
  final invoices_archive = 'navigation.invoices_archive';
  final logout = 'navigation.logout';
  final manage_devices = 'navigation.manage_devices';
  final master_data_section = 'navigation.master_data_section';
  final notifications = 'navigation.notifications';
  final order_lists = 'navigation.order_lists';
  final ordering_section = 'navigation.ordering_section';
  final orders = 'navigation.orders';
  final process_receivings = 'navigation.process_receivings';
  final purchase_requests = 'navigation.purchase_requests';
  final receiving_approval_requests = 'navigation.receiving_approval_requests';
  final receivings_section = 'navigation.receivings_section';
  final recipe_categories = 'navigation.recipe_categories';
  final reports = 'navigation.reports';
  final scanned_documents = 'navigation.scanned_documents';
  final settings = 'navigation.settings';
  final settings_section = 'navigation.settings_section';
  final store_master_data = 'navigation.store_master_data';
  final suppliers = 'navigation.suppliers';
  final transactions_section = 'navigation.transactions_section';
  final transfer_list_orders = 'navigation.transfer_list_orders';
  final transfer_lists = 'navigation.transfer_lists';
  final transfer_orders_approval = 'navigation.transfer_orders_approval';
}

class _LabelsNotifications {
  const _LabelsNotifications();

  final clear_all = 'notifications.clear_all';
  final content = 'notifications.content';
  final delete_all_alert = const _LabelsNotificationsDeleteAllAlert();
  final empty = 'notifications.empty';
}

class _LabelsNotificationsDeleteAllAlert {
  const _LabelsNotificationsDeleteAllAlert();

  final cancel_action = 'notifications.delete_all_alert.cancel_action';
  final content = 'notifications.delete_all_alert.content';
  final yes_action = 'notifications.delete_all_alert.yes_action';
}

class _LabelsOperation {
  const _LabelsOperation();

  final success = 'operation.success';
}

class _LabelsOrderLists {
  const _LabelsOrderLists();

  final add_to_list = const _LabelsOrderListsAddToList();
  final copy_to_list = const _LabelsOrderListsCopyToList();
  final create = const _LabelsOrderListsCreate();
  final filter = const _LabelsOrderListsFilter();
  final local = const _LabelsOrderListsLocal();
  final move_to_list = const _LabelsOrderListsMoveToList();
  final new_list_added_successfully_label =
      'order_lists.new_list_added_successfully_label';
  final new_list_name_label = 'order_lists.new_list_name_label';
  final order_list = const _LabelsOrderListsOrderList();
  final products_change_order = const _LabelsOrderListsProductsChangeOrder();
  final products_reorder = const _LabelsOrderListsProductsReorder();
  final search_available_only_online =
      'order_lists.search_available_only_online';
  final selection = const _LabelsOrderListsSelection();
  final supplier_change = const _LabelsOrderListsSupplierChange();
}

class _LabelsOrderListsAddToList {
  const _LabelsOrderListsAddToList();

  final button_label = 'order_lists.add_to_list.button_label';
  final title = 'order_lists.add_to_list.title';
}

class _LabelsOrderListsCopyToList {
  const _LabelsOrderListsCopyToList();

  final button_label = 'order_lists.copy_to_list.button_label';
  final title = 'order_lists.copy_to_list.title';
}

class _LabelsOrderListsCreate {
  const _LabelsOrderListsCreate();

  final create_button_label = 'order_lists.create.create_button_label';
  final name_should_not_be_empty =
      'order_lists.create.name_should_not_be_empty';
  final title = 'order_lists.create.title';
}

class _LabelsOrderListsFilter {
  const _LabelsOrderListsFilter();

  final sorting = const _LabelsOrderListsFilterSorting();
  final type = const _LabelsOrderListsFilterType();
  final type_label = 'order_lists.filter.type_label';
}

class _LabelsOrderListsFilterSorting {
  const _LabelsOrderListsFilterSorting();

  final by_name_asc = 'order_lists.filter.sorting.by_name_asc';
  final by_name_desc = 'order_lists.filter.sorting.by_name_desc';
  final last_modified_first = 'order_lists.filter.sorting.last_modified_first';
  final most_used_first = 'order_lists.filter.sorting.most_used_first';
}

class _LabelsOrderListsFilterType {
  const _LabelsOrderListsFilterType();

  final all = 'order_lists.filter.type.all';
  final cost_center_only = 'order_lists.filter.type.cost_center_only';
  final read_only = 'order_lists.filter.type.read_only';
  final recipes = 'order_lists.filter.type.recipes';
}

class _LabelsOrderListsLocal {
  const _LabelsOrderListsLocal();

  final last_updated_at_label = 'order_lists.local.last_updated_at_label';
  final load_button_label = 'order_lists.local.load_button_label';
  final load_successfully = 'order_lists.local.load_successfully';
  final loaded_count = 'order_lists.local.loaded_count';
  final loading_lists = 'order_lists.local.loading_lists';
  final need_catalog_rebuild = 'order_lists.local.need_catalog_rebuild';
  final no_lists_in_cost_center = 'order_lists.local.no_lists_in_cost_center';
  final no_lists_saved = 'order_lists.local.no_lists_saved';
  final open_button_label = 'order_lists.local.open_button_label';
  final title = 'order_lists.local.title';
  final update = const _LabelsOrderListsLocalUpdate();
  final update_button_label = 'order_lists.local.update_button_label';
}

class _LabelsOrderListsLocalUpdate {
  const _LabelsOrderListsLocalUpdate();

  final alert = const _LabelsOrderListsLocalUpdateAlert();
}

class _LabelsOrderListsLocalUpdateAlert {
  const _LabelsOrderListsLocalUpdateAlert();

  final content = 'order_lists.local.update.alert.content';
  final no_action = 'order_lists.local.update.alert.no_action';
  final yes_action = 'order_lists.local.update.alert.yes_action';
}

class _LabelsOrderListsMoveToList {
  const _LabelsOrderListsMoveToList();

  final button_label = 'order_lists.move_to_list.button_label';
  final title = 'order_lists.move_to_list.title';
}

class _LabelsOrderListsOrderList {
  const _LabelsOrderListsOrderList();

  final already_downloaded_message =
      'order_lists.order_list.already_downloaded_message';
  final download_list_label = 'order_lists.order_list.download_list_label';
  final downloaded_successfully_label =
      'order_lists.order_list.downloaded_successfully_label';
  final edit_name_label = 'order_lists.order_list.edit_name_label';
  final list_overview_label = 'order_lists.order_list.list_overview_label';
  final local = const _LabelsOrderListsOrderListLocal();
  final open_catalog_button_label =
      'order_lists.order_list.open_catalog_button_label';
  final open_downloaded_list_button_label =
      'order_lists.order_list.open_downloaded_list_button_label';
  final product = const _LabelsOrderListsOrderListProduct();
  final product_delete_alert =
      const _LabelsOrderListsOrderListProductDeleteAlert();
  final product_delete_from_cart_alert =
      const _LabelsOrderListsOrderListProductDeleteFromCartAlert();
  final product_deleted_successfully_label =
      'order_lists.order_list.product_deleted_successfully_label';
  final product_quantity_in_cart_updated_successfully_label =
      'order_lists.order_list.product_quantity_in_cart_updated_successfully_label';
  final product_removed_from_cart_successfully_label =
      'order_lists.order_list.product_removed_from_cart_successfully_label';
  final product_supplier_updated_successfully_label =
      'order_lists.order_list.product_supplier_updated_successfully_label';
  final products_search_available_only_online =
      'order_lists.order_list.products_search_available_only_online';
  final read_note = const _LabelsOrderListsOrderListReadNote();
  final renamed_successfully_label =
      'order_lists.order_list.renamed_successfully_label';
  final reorder_products_label =
      'order_lists.order_list.reorder_products_label';
  final undo_product_deletion_successfully_label =
      'order_lists.order_list.undo_product_deletion_successfully_label';
}

class _LabelsOrderListsOrderListLocal {
  const _LabelsOrderListsOrderListLocal();

  final last_updated_at_label =
      'order_lists.order_list.local.last_updated_at_label';
}

class _LabelsOrderListsOrderListProduct {
  const _LabelsOrderListsOrderListProduct();

  final added_on = 'order_lists.order_list.product.added_on';
  final barcode = 'order_lists.order_list.product.barcode';
  final change_supplier_button_label =
      'order_lists.order_list.product.change_supplier_button_label';
  final comment = const _LabelsOrderListsOrderListProductComment();
  final comment_added_successfully_label =
      'order_lists.order_list.product.comment_added_successfully_label';
  final comment_deleted_successfully_label =
      'order_lists.order_list.product.comment_deleted_successfully_label';
  final comment_edited_successfully_label =
      'order_lists.order_list.product.comment_edited_successfully_label';
  final content_units_per_order_unit =
      'order_lists.order_list.product.content_units_per_order_unit';
  final copied_successfully_label =
      'order_lists.order_list.product.copied_successfully_label';
  final copy_to_list_button_label =
      'order_lists.order_list.product.copy_to_list_button_label';
  final country_of_the_origin =
      'order_lists.order_list.product.country_of_the_origin';
  final delete_button_label =
      'order_lists.order_list.product.delete_button_label';
  final details_button_label =
      'order_lists.order_list.product.details_button_label';
  final move_to_list_button_label =
      'order_lists.order_list.product.move_to_list_button_label';
  final moved_successfully_label =
      'order_lists.order_list.product.moved_successfully_label';
  final price_last_updated_at =
      'order_lists.order_list.product.price_last_updated_at';
  final product_id = 'order_lists.order_list.product.product_id';
  final quantity_add_label =
      'order_lists.order_list.product.quantity_add_label';
  final quantity_field_label =
      'order_lists.order_list.product.quantity_field_label';
  final quantity_update_label =
      'order_lists.order_list.product.quantity_update_label';
  final supplier = 'order_lists.order_list.product.supplier';
  final supplier_changed_successfully_label =
      'order_lists.order_list.product.supplier_changed_successfully_label';
  final units_in_cart_label =
      'order_lists.order_list.product.units_in_cart_label';
}

class _LabelsOrderListsOrderListProductComment {
  const _LabelsOrderListsOrderListProductComment();

  final add = const _LabelsOrderListsOrderListProductCommentAdd();
  final add_button_label =
      'order_lists.order_list.product.comment.add_button_label';
  final comment_label = 'order_lists.order_list.product.comment.comment_label';
  final delete_alert =
      const _LabelsOrderListsOrderListProductCommentDeleteAlert();
  final delete_button_label =
      'order_lists.order_list.product.comment.delete_button_label';
  final edit = const _LabelsOrderListsOrderListProductCommentEdit();
  final edit_button_label =
      'order_lists.order_list.product.comment.edit_button_label';
  final no_comment_label =
      'order_lists.order_list.product.comment.no_comment_label';
}

class _LabelsOrderListsOrderListProductCommentAdd {
  const _LabelsOrderListsOrderListProductCommentAdd();

  final title = 'order_lists.order_list.product.comment.add.title';
}

class _LabelsOrderListsOrderListProductCommentDeleteAlert {
  const _LabelsOrderListsOrderListProductCommentDeleteAlert();

  final content = 'order_lists.order_list.product.comment.delete_alert.content';
  final no_action =
      'order_lists.order_list.product.comment.delete_alert.no_action';
  final yes_action =
      'order_lists.order_list.product.comment.delete_alert.yes_action';
}

class _LabelsOrderListsOrderListProductCommentEdit {
  const _LabelsOrderListsOrderListProductCommentEdit();

  final field_label = 'order_lists.order_list.product.comment.edit.field_label';
  final save_button_label =
      'order_lists.order_list.product.comment.edit.save_button_label';
  final title = 'order_lists.order_list.product.comment.edit.title';
}

class _LabelsOrderListsOrderListProductDeleteAlert {
  const _LabelsOrderListsOrderListProductDeleteAlert();

  final content = 'order_lists.order_list.product_delete_alert.content';
  final no_action = 'order_lists.order_list.product_delete_alert.no_action';
  final yes_action = 'order_lists.order_list.product_delete_alert.yes_action';
}

class _LabelsOrderListsOrderListProductDeleteFromCartAlert {
  const _LabelsOrderListsOrderListProductDeleteFromCartAlert();

  final content =
      'order_lists.order_list.product_delete_from_cart_alert.content';
  final no_action =
      'order_lists.order_list.product_delete_from_cart_alert.no_action';
  final yes_action =
      'order_lists.order_list.product_delete_from_cart_alert.yes_action';
}

class _LabelsOrderListsOrderListReadNote {
  const _LabelsOrderListsOrderListReadNote();

  final add = const _LabelsOrderListsOrderListReadNoteAdd();
  final add_button_label = 'order_lists.order_list.read_note.add_button_label';
  final added_successfully =
      'order_lists.order_list.read_note.added_successfully';
  final delete_alert = const _LabelsOrderListsOrderListReadNoteDeleteAlert();
  final delete_button_label =
      'order_lists.order_list.read_note.delete_button_label';
  final deleted_successfully =
      'order_lists.order_list.read_note.deleted_successfully';
  final edit = const _LabelsOrderListsOrderListReadNoteEdit();
  final edit_button_label =
      'order_lists.order_list.read_note.edit_button_label';
  final edited_successfully =
      'order_lists.order_list.read_note.edited_successfully';
  final no_note_label = 'order_lists.order_list.read_note.no_note_label';
  final note_label = 'order_lists.order_list.read_note.note_label';
}

class _LabelsOrderListsOrderListReadNoteAdd {
  const _LabelsOrderListsOrderListReadNoteAdd();

  final title = 'order_lists.order_list.read_note.add.title';
}

class _LabelsOrderListsOrderListReadNoteDeleteAlert {
  const _LabelsOrderListsOrderListReadNoteDeleteAlert();

  final content = 'order_lists.order_list.read_note.delete_alert.content';
  final no_action = 'order_lists.order_list.read_note.delete_alert.no_action';
  final yes_action = 'order_lists.order_list.read_note.delete_alert.yes_action';
}

class _LabelsOrderListsOrderListReadNoteEdit {
  const _LabelsOrderListsOrderListReadNoteEdit();

  final field_label = 'order_lists.order_list.read_note.edit.field_label';
  final save_button_label =
      'order_lists.order_list.read_note.edit.save_button_label';
  final title = 'order_lists.order_list.read_note.edit.title';
}

class _LabelsOrderListsProductsChangeOrder {
  const _LabelsOrderListsProductsChangeOrder();

  final available_only_online =
      'order_lists.products_change_order.available_only_online';
  final save_button = const _LabelsOrderListsProductsChangeOrderSaveButton();
  final title = 'order_lists.products_change_order.title';
}

class _LabelsOrderListsProductsChangeOrderSaveButton {
  const _LabelsOrderListsProductsChangeOrderSaveButton();

  final label = 'order_lists.products_change_order.save_button.label';
}

class _LabelsOrderListsProductsReorder {
  const _LabelsOrderListsProductsReorder();

  final successful_label = 'order_lists.products_reorder.successful_label';
}

class _LabelsOrderListsSelection {
  const _LabelsOrderListsSelection();

  final item_is_already_on_list =
      'order_lists.selection.item_is_already_on_list';
}

class _LabelsOrderListsSupplierChange {
  const _LabelsOrderListsSupplierChange();

  final action_available_only_online =
      'order_lists.supplier_change.action_available_only_online';
  final button_label = 'order_lists.supplier_change.button_label';
  final search_available_only_online =
      'order_lists.supplier_change.search_available_only_online';
  final title = 'order_lists.supplier_change.title';
}

class _LabelsOrders {
  const _LabelsOrders();

  final amount = 'orders.amount';
  final astore = const _LabelsOrdersAstore();
  final breakdown_by_cost_centers = const _LabelsOrdersBreakdownByCostCenters();
  final delivery_date = 'orders.delivery_date';
  final external_comment = const _LabelsOrdersExternalComment();
  final filter = const _LabelsOrdersFilter();
  final order = const _LabelsOrdersOrder();
  final order_articles_merge_needed = 'orders.order_articles_merge_needed';
  final order_articles_to_merge = 'orders.order_articles_to_merge';
  final order_date = 'orders.order_date';
  final product = const _LabelsOrdersProduct();
  final search_available_only_online = 'orders.search_available_only_online';
  final target_cost_centers = const _LabelsOrdersTargetCostCenters();
}

class _LabelsOrdersAstore {
  const _LabelsOrdersAstore();

  final can_be_merged_only_in_web_label =
      'orders.astore.can_be_merged_only_in_web_label';
  final can_be_merged_only_in_web_path =
      'orders.astore.can_be_merged_only_in_web_path';
  final filter = const _LabelsOrdersAstoreFilter();
  final title = 'orders.astore.title';
}

class _LabelsOrdersAstoreFilter {
  const _LabelsOrdersAstoreFilter();

  final date_range = 'orders.astore.filter.date_range';
  final order_number_hint = 'orders.astore.filter.order_number_hint';
  final order_number_label = 'orders.astore.filter.order_number_label';
  final sorting = const _LabelsOrdersAstoreFilterSorting();
  final supplier_hint = 'orders.astore.filter.supplier_hint';
  final supplier_label = 'orders.astore.filter.supplier_label';
}

class _LabelsOrdersAstoreFilterSorting {
  const _LabelsOrdersAstoreFilterSorting();

  final by_order_date_asc = 'orders.astore.filter.sorting.by_order_date_asc';
  final by_order_date_desc = 'orders.astore.filter.sorting.by_order_date_desc';
  final by_supplier_asc = 'orders.astore.filter.sorting.by_supplier_asc';
  final by_supplier_desc = 'orders.astore.filter.sorting.by_supplier_desc';
}

class _LabelsOrdersBreakdownByCostCenters {
  const _LabelsOrdersBreakdownByCostCenters();

  final title = 'orders.breakdown_by_cost_centers.title';
}

class _LabelsOrdersExternalComment {
  const _LabelsOrdersExternalComment();

  final none = 'orders.external_comment.none';
}

class _LabelsOrdersFilter {
  const _LabelsOrdersFilter();

  final cost_center = const _LabelsOrdersFilterCostCenter();
  final cost_center_label = 'orders.filter.cost_center_label';
  final date_range = 'orders.filter.date_range';
  final orderer = const _LabelsOrdersFilterOrderer();
  final orderer_label = 'orders.filter.orderer_label';
  final sorting = const _LabelsOrdersFilterSorting();
  final status = const _LabelsOrdersFilterStatus();
  final status_label = 'orders.filter.status_label';
}

class _LabelsOrdersFilterCostCenter {
  const _LabelsOrdersFilterCostCenter();

  final any = 'orders.filter.cost_center.any';
}

class _LabelsOrdersFilterOrderer {
  const _LabelsOrdersFilterOrderer();

  final any = 'orders.filter.orderer.any';
}

class _LabelsOrdersFilterSorting {
  const _LabelsOrdersFilterSorting();

  final by_order_date_asc = 'orders.filter.sorting.by_order_date_asc';
  final by_order_date_desc = 'orders.filter.sorting.by_order_date_desc';
}

class _LabelsOrdersFilterStatus {
  const _LabelsOrdersFilterStatus();

  final all = 'orders.filter.status.all';
  final completed = 'orders.filter.status.completed';
  final open = 'orders.filter.status.open';
}

class _LabelsOrdersOrder {
  const _LabelsOrdersOrder();

  final articles = const _LabelsOrdersOrderArticles();
  final content_unit_price = 'orders.order.content_unit_price';
  final discount_amount = 'orders.order.discount_amount';
  final external_comment = const _LabelsOrdersOrderExternalComment();
  final marked_to_only_create_label =
      'orders.order.marked_to_only_create_label';
  final only_create_status = 'orders.order.only_create_status';
  final order_again = const _LabelsOrdersOrderOrderAgain();
  final order_quantity = 'orders.order.order_quantity';
  final order_total = 'orders.order.order_total';
  final order_unit_price = 'orders.order.order_unit_price';
  final products_search_available_only_online =
      'orders.order.products_search_available_only_online';
  final send_message_to_supplier =
      const _LabelsOrdersOrderSendMessageToSupplier();
  final target_cost_centers = const _LabelsOrdersOrderTargetCostCenters();
  final title = 'orders.order.title';
  final total_after_discount = 'orders.order.total_after_discount';
  final view_pdf_button_label = 'orders.order.view_pdf_button_label';
}

class _LabelsOrdersOrderArticles {
  const _LabelsOrdersOrderArticles();

  final title = 'orders.order.articles.title';
}

class _LabelsOrdersOrderExternalComment {
  const _LabelsOrdersOrderExternalComment();

  final title = 'orders.order.external_comment.title';
}

class _LabelsOrdersOrderOrderAgain {
  const _LabelsOrdersOrderOrderAgain();

  final button_label = 'orders.order.order_again.button_label';
  final ordered_again_partially_label =
      'orders.order.order_again.ordered_again_partially_label';
  final ordered_again_successfully_label =
      'orders.order.order_again.ordered_again_successfully_label';
  final processing_products = 'orders.order.order_again.processing_products';
  final product = const _LabelsOrdersOrderOrderAgainProduct();
  final products_not_found_label =
      'orders.order.order_again.products_not_found_label';
  final products_processed = 'orders.order.order_again.products_processed';
  final send_alert = const _LabelsOrdersOrderOrderAgainSendAlert();
  final send_to_the_cart_button_label =
      'orders.order.order_again.send_to_the_cart_button_label';
  final sent_view_log_button_label =
      'orders.order.order_again.sent_view_log_button_label';
  final title = 'orders.order.order_again.title';
  final total_value_label = 'orders.order.order_again.total_value_label';
}

class _LabelsOrdersOrderOrderAgainProduct {
  const _LabelsOrdersOrderOrderAgainProduct();

  final actual = 'orders.order.order_again.product.actual';
  final edit_button_label =
      'orders.order.order_again.product.edit_button_label';
  final package_ordered = 'orders.order.order_again.product.package_ordered';
  final price_ordered = 'orders.order.order_again.product.price_ordered';
  final quantity_field_label =
      'orders.order.order_again.product.quantity_field_label';
  final quantity_update_button_label =
      'orders.order.order_again.product.quantity_update_button_label';
  final sum = 'orders.order.order_again.product.sum';
}

class _LabelsOrdersOrderOrderAgainSendAlert {
  const _LabelsOrdersOrderOrderAgainSendAlert();

  final content = 'orders.order.order_again.send_alert.content';
  final no_action = 'orders.order.order_again.send_alert.no_action';
  final yes_action = 'orders.order.order_again.send_alert.yes_action';
}

class _LabelsOrdersOrderSendMessageToSupplier {
  const _LabelsOrdersOrderSendMessageToSupplier();

  final button_label = 'orders.order.send_message_to_supplier.button_label';
  final discard_message_alert =
      const _LabelsOrdersOrderSendMessageToSupplierDiscardMessageAlert();
  final email_format_error =
      'orders.order.send_message_to_supplier.email_format_error';
  final email_is_empty_error =
      'orders.order.send_message_to_supplier.email_is_empty_error';
  final label = 'orders.order.send_message_to_supplier.label';
  final message_is_empty_error =
      'orders.order.send_message_to_supplier.message_is_empty_error';
  final message_label = 'orders.order.send_message_to_supplier.message_label';
  final message_sent_successfully =
      'orders.order.send_message_to_supplier.message_sent_successfully';
  final reply_to_email_label =
      'orders.order.send_message_to_supplier.reply_to_email_label';
  final send_button_label =
      'orders.order.send_message_to_supplier.send_button_label';
}

class _LabelsOrdersOrderSendMessageToSupplierDiscardMessageAlert {
  const _LabelsOrdersOrderSendMessageToSupplierDiscardMessageAlert();

  final content =
      'orders.order.send_message_to_supplier.discard_message_alert.content';
  final no_action =
      'orders.order.send_message_to_supplier.discard_message_alert.no_action';
  final title =
      'orders.order.send_message_to_supplier.discard_message_alert.title';
  final yes_action =
      'orders.order.send_message_to_supplier.discard_message_alert.yes_action';
}

class _LabelsOrdersOrderTargetCostCenters {
  const _LabelsOrdersOrderTargetCostCenters();

  final button_label = 'orders.order.target_cost_centers.button_label';
}

class _LabelsOrdersProduct {
  const _LabelsOrdersProduct();

  final order_history = 'orders.product.order_history';
  final order_history_title = 'orders.product.order_history_title';
}

class _LabelsOrdersTargetCostCenters {
  const _LabelsOrdersTargetCostCenters();

  final only_online = 'orders.target_cost_centers.only_online';
  final order_number = 'orders.target_cost_centers.order_number';
  final total_label = 'orders.target_cost_centers.total_label';
}

class _LabelsPdf {
  const _LabelsPdf();

  final available_only_online = 'pdf.available_only_online';
}

class _LabelsPdfView {
  const _LabelsPdfView();

  final title = 'pdf_view.title';
}

class _LabelsPinConfirmation {
  const _LabelsPinConfirmation();

  final label = 'pin_confirmation.label';
}

class _LabelsProcessReceiving {
  const _LabelsProcessReceiving();

  final disabled_store = 'process_receiving.disabled_store';
  final order_item = const _LabelsProcessReceivingOrderItem();
}

class _LabelsProcessReceivingOrderItem {
  const _LabelsProcessReceivingOrderItem();

  final editing_not_allowed =
      'process_receiving.order_item.editing_not_allowed';
  final error = const _LabelsProcessReceivingOrderItemError();
  final warning = const _LabelsProcessReceivingOrderItemWarning();
}

class _LabelsProcessReceivingOrderItemError {
  const _LabelsProcessReceivingOrderItemError();

  final content_unit_is_missing =
      'process_receiving.order_item.error.content_unit_is_missing';
  final content_units_per_order_unit_is_missing =
      'process_receiving.order_item.error.content_units_per_order_unit_is_missing';
  final inventory_factor_is_missing =
      'process_receiving.order_item.error.inventory_factor_is_missing';
  final inventory_unit_is_missing =
      'process_receiving.order_item.error.inventory_unit_is_missing';
  final item_archived = 'process_receiving.order_item.error.item_archived';
  final item_price_is_missing =
      'process_receiving.order_item.error.item_price_is_missing';
  final item_qty_is_missing =
      'process_receiving.order_item.error.item_qty_is_missing';
  final item_stock_below_zero_error =
      'process_receiving.order_item.error.item_stock_below_zero_error';
  final order_unit_is_missing =
      'process_receiving.order_item.error.order_unit_is_missing';
  final split_delivery_missing_cost_center =
      'process_receiving.order_item.error.split_delivery_missing_cost_center';
  final split_delivery_missing_cost_type =
      'process_receiving.order_item.error.split_delivery_missing_cost_type';
  final split_delivery_missing_store =
      'process_receiving.order_item.error.split_delivery_missing_store';
  final vat_is_missing = 'process_receiving.order_item.error.vat_is_missing';
}

class _LabelsProcessReceivingOrderItemWarning {
  const _LabelsProcessReceivingOrderItemWarning();

  final cost_center_does_not_match =
      'process_receiving.order_item.warning.cost_center_does_not_match';
  final price_does_not_match =
      'process_receiving.order_item.warning.price_does_not_match';
  final receiving_variance =
      'process_receiving.order_item.warning.receiving_variance';
}

class _LabelsProcessReceivings {
  const _LabelsProcessReceivings();

  final approved_status = 'process_receivings.approved_status';
  final can_be_booked_status = 'process_receivings.can_be_booked_status';
  final create_provisional_booking_button_label =
      'process_receivings.create_provisional_booking_button_label';
  final declined_status = 'process_receivings.declined_status';
  final delivery_note_tax_invoice_number =
      'process_receivings.delivery_note_tax_invoice_number';
  final details = const _LabelsProcessReceivingsDetails();
  final freight_cost = const _LabelsProcessReceivingsFreightCost();
  final in_progress_status = 'process_receivings.in_progress_status';
  final non_po = const _LabelsProcessReceivingsNonPo();
  final non_po_receivings = const _LabelsProcessReceivingsNonPoReceivings();
  final order = const _LabelsProcessReceivingsOrder();
  final order_items = const _LabelsProcessReceivingsOrderItems();
  final orders = const _LabelsProcessReceivingsOrders();
  final processing_status = 'process_receivings.processing_status';
  final provisional_booking =
      const _LabelsProcessReceivingsProvisionalBooking();
  final provisional_bookings =
      const _LabelsProcessReceivingsProvisionalBookings();
  final view_pdf = const _LabelsProcessReceivingsViewPdf();
}

class _LabelsProcessReceivingsDetails {
  const _LabelsProcessReceivingsDetails();

  final amount = 'process_receivings.details.amount';
  final attachments_tab_title =
      'process_receivings.details.attachments_tab_title';
  final checksum = 'process_receivings.details.checksum';
  final delivery = const _LabelsProcessReceivingsDetailsDelivery();
  final delivery_tab_title = 'process_receivings.details.delivery_tab_title';
  final details_tab_title = 'process_receivings.details.details_tab_title';
  final discount_total_updated_successfully =
      'process_receivings.details.discount_total_updated_successfully';
  final edit_discount = const _LabelsProcessReceivingsDetailsEditDiscount();
  final error = const _LabelsProcessReceivingsDetailsError();
  final errors_hint = 'process_receivings.details.errors_hint';
  final errors_label = 'process_receivings.details.errors_label';
  final exchange_rate = 'process_receivings.details.exchange_rate';
  final exchange_rate_date = 'process_receivings.details.exchange_rate_date';
  final gross_amount = 'process_receivings.details.gross_amount';
  final net_amount = 'process_receivings.details.net_amount';
  final order_info = 'process_receivings.details.order_info';
  final order_items = const _LabelsProcessReceivingsDetailsOrderItems();
  final order_items_tab_title =
      'process_receivings.details.order_items_tab_title';
  final split_delivery = 'process_receivings.details.split_delivery';
  final summary = 'process_receivings.details.summary';
  final title = 'process_receivings.details.title';
  final total_amount = 'process_receivings.details.total_amount';
  final total_tax = 'process_receivings.details.total_tax';
  final total_value = 'process_receivings.details.total_value';
  final total_vat_amount = 'process_receivings.details.total_vat_amount';
  final vat_amount = 'process_receivings.details.vat_amount';
  final warning = const _LabelsProcessReceivingsDetailsWarning();
  final warnings_label = 'process_receivings.details.warnings_label';
}

class _LabelsProcessReceivingsDetailsDelivery {
  const _LabelsProcessReceivingsDetailsDelivery();

  final additional_delivery =
      const _LabelsProcessReceivingsDetailsDeliveryAdditionalDelivery();
  final const_centers_breakdown =
      const _LabelsProcessReceivingsDetailsDeliveryConstCentersBreakdown();
  final cost_centers =
      const _LabelsProcessReceivingsDetailsDeliveryCostCenters();
  final delivery_note_data =
      const _LabelsProcessReceivingsDetailsDeliveryDeliveryNoteData();
  final deposit_items =
      const _LabelsProcessReceivingsDetailsDeliveryDepositItems();
  final partial_history =
      const _LabelsProcessReceivingsDetailsDeliveryPartialHistory();
  final related_orders =
      const _LabelsProcessReceivingsDetailsDeliveryRelatedOrders();
}

class _LabelsProcessReceivingsDetailsDeliveryAdditionalDelivery {
  const _LabelsProcessReceivingsDetailsDeliveryAdditionalDelivery();

  final description =
      'process_receivings.details.delivery.additional_delivery.description';
  final title = 'process_receivings.details.delivery.additional_delivery.title';
}

class _LabelsProcessReceivingsDetailsDeliveryConstCentersBreakdown {
  const _LabelsProcessReceivingsDetailsDeliveryConstCentersBreakdown();

  final title =
      'process_receivings.details.delivery.const_centers_breakdown.title';
}

class _LabelsProcessReceivingsDetailsDeliveryCostCenters {
  const _LabelsProcessReceivingsDetailsDeliveryCostCenters();

  final description =
      'process_receivings.details.delivery.cost_centers.description';
  final title = 'process_receivings.details.delivery.cost_centers.title';
}

class _LabelsProcessReceivingsDetailsDeliveryDeliveryNoteData {
  const _LabelsProcessReceivingsDetailsDeliveryDeliveryNoteData();

  final description =
      'process_receivings.details.delivery.delivery_note_data.description';
  final title = 'process_receivings.details.delivery.delivery_note_data.title';
}

class _LabelsProcessReceivingsDetailsDeliveryDepositItems {
  const _LabelsProcessReceivingsDetailsDeliveryDepositItems();

  final description =
      'process_receivings.details.delivery.deposit_items.description';
  final title = 'process_receivings.details.delivery.deposit_items.title';
}

class _LabelsProcessReceivingsDetailsDeliveryPartialHistory {
  const _LabelsProcessReceivingsDetailsDeliveryPartialHistory();

  final description =
      'process_receivings.details.delivery.partial_history.description';
  final title = 'process_receivings.details.delivery.partial_history.title';
}

class _LabelsProcessReceivingsDetailsDeliveryRelatedOrders {
  const _LabelsProcessReceivingsDetailsDeliveryRelatedOrders();

  final description =
      'process_receivings.details.delivery.related_orders.description';
  final title = 'process_receivings.details.delivery.related_orders.title';
}

class _LabelsProcessReceivingsDetailsEditDiscount {
  const _LabelsProcessReceivingsDetailsEditDiscount();

  final discount_label =
      'process_receivings.details.edit_discount.discount_label';
  final update_button =
      'process_receivings.details.edit_discount.update_button';
}

class _LabelsProcessReceivingsDetailsError {
  const _LabelsProcessReceivingsDetailsError();

  final booking_date_after_open_inventory =
      'process_receivings.details.error.booking_date_after_open_inventory';
  final booking_date_before_closed_inventory =
      'process_receivings.details.error.booking_date_before_closed_inventory';
  final booking_date_in_the_future =
      'process_receivings.details.error.booking_date_in_the_future';
  final booking_date_is_not_valid =
      'process_receivings.details.error.booking_date_is_not_valid';
  final checksum_error = 'process_receivings.details.error.checksum_error';
  final cost_center_is_not_set =
      'process_receivings.details.error.cost_center_is_not_set';
  final currency_exchange_rate_error =
      'process_receivings.details.error.currency_exchange_rate_error';
  final delivery_note_already_assigned =
      'process_receivings.details.error.delivery_note_already_assigned';
  final delivery_note_date_in_the_future =
      'process_receivings.details.error.delivery_note_date_in_the_future';
  final delivery_note_date_in_the_past =
      'process_receivings.details.error.delivery_note_date_in_the_past';
  final delivery_note_date_is_not_valid =
      'process_receivings.details.error.delivery_note_date_is_not_valid';
  final delivery_note_is_empty =
      'process_receivings.details.error.delivery_note_is_empty';
  final delivery_note_is_missing =
      'process_receivings.details.error.delivery_note_is_missing';
  final deposit_item_error =
      'process_receivings.details.error.deposit_item_error';
  final inventory_was_opened_same_day =
      'process_receivings.details.error.inventory_was_opened_same_day';
  final invoice_assigned_but_delivery_note_is_not_invoice =
      'process_receivings.details.error.invoice_assigned_but_delivery_note_is_not_invoice';
  final item_stock_below_zero_error =
      'process_receivings.details.error.item_stock_below_zero_error';
  final one_or_more_order_items_incomplete =
      'process_receivings.details.error.one_or_more_order_items_incomplete';
  final previous_inventory_opened =
      'process_receivings.details.error.previous_inventory_opened';
  final provisional_booking_after_open_inventory =
      'process_receivings.details.error.provisional_booking_after_open_inventory';
  final provisional_booking_exists =
      'process_receivings.details.error.provisional_booking_exists';
}

class _LabelsProcessReceivingsDetailsOrderItems {
  const _LabelsProcessReceivingsDetailsOrderItems();

  final delete_alert =
      const _LabelsProcessReceivingsDetailsOrderItemsDeleteAlert();
  final deleted_successfully =
      'process_receivings.details.order_items.deleted_successfully';
}

class _LabelsProcessReceivingsDetailsOrderItemsDeleteAlert {
  const _LabelsProcessReceivingsDetailsOrderItemsDeleteAlert();

  final content = 'process_receivings.details.order_items.delete_alert.content';
  final no_action =
      'process_receivings.details.order_items.delete_alert.no_action';
  final yes_action =
      'process_receivings.details.order_items.delete_alert.yes_action';
}

class _LabelsProcessReceivingsDetailsWarning {
  const _LabelsProcessReceivingsDetailsWarning();

  final only_provisional_booking_is_applicable =
      'process_receivings.details.warning.only_provisional_booking_is_applicable';
}

class _LabelsProcessReceivingsFreightCost {
  const _LabelsProcessReceivingsFreightCost();

  final amount = 'process_receivings.freight_cost.amount';
  final currency = 'process_receivings.freight_cost.currency';
  final custom_duty_label = 'process_receivings.freight_cost.custom_duty_label';
  final exchange = 'process_receivings.freight_cost.exchange';
  final exchange_rate = 'process_receivings.freight_cost.exchange_rate';
  final handling_duty_label =
      'process_receivings.freight_cost.handling_duty_label';
  final main_label = 'process_receivings.freight_cost.main_label';
  final supplier = 'process_receivings.freight_cost.supplier';
  final supplier_currency = 'process_receivings.freight_cost.supplier_currency';
  final supplier_label = 'process_receivings.freight_cost.supplier_label';
  final title = 'process_receivings.freight_cost.title';
  final total = const _LabelsProcessReceivingsFreightCostTotal();
}

class _LabelsProcessReceivingsFreightCostTotal {
  const _LabelsProcessReceivingsFreightCostTotal();

  final apply_button = 'process_receivings.freight_cost.total.apply_button';
  final delivery_note = 'process_receivings.freight_cost.total.delivery_note';
  final deposit_items = 'process_receivings.freight_cost.total.deposit_items';
  final discounts = 'process_receivings.freight_cost.total.discounts';
  final freight_and_import_cost =
      'process_receivings.freight_cost.total.freight_and_import_cost';
  final gross = 'process_receivings.freight_cost.total.gross';
  final items = 'process_receivings.freight_cost.total.items';
  final save_button = 'process_receivings.freight_cost.total.save_button';
  final save_successful =
      'process_receivings.freight_cost.total.save_successful';
  final tax = 'process_receivings.freight_cost.total.tax';
  final title = 'process_receivings.freight_cost.total.title';
}

class _LabelsProcessReceivingsNonPo {
  const _LabelsProcessReceivingsNonPo();

  final filter = const _LabelsProcessReceivingsNonPoFilter();
}

class _LabelsProcessReceivingsNonPoFilter {
  const _LabelsProcessReceivingsNonPoFilter();

  final sorting = const _LabelsProcessReceivingsNonPoFilterSorting();
}

class _LabelsProcessReceivingsNonPoFilterSorting {
  const _LabelsProcessReceivingsNonPoFilterSorting();

  final by_creation_date_asc =
      'process_receivings.non_po.filter.sorting.by_creation_date_asc';
  final by_creation_date_desc =
      'process_receivings.non_po.filter.sorting.by_creation_date_desc';
}

class _LabelsProcessReceivingsNonPoReceivings {
  const _LabelsProcessReceivingsNonPoReceivings();

  final title = 'process_receivings.non_po_receivings.title';
}

class _LabelsProcessReceivingsOrder {
  const _LabelsProcessReceivingsOrder();

  final request_approval = const _LabelsProcessReceivingsOrderRequestApproval();
}

class _LabelsProcessReceivingsOrderRequestApproval {
  const _LabelsProcessReceivingsOrderRequestApproval();

  final button_label = 'process_receivings.order.request_approval.button_label';
}

class _LabelsProcessReceivingsOrderItems {
  const _LabelsProcessReceivingsOrderItems();

  final discount = 'process_receivings.order_items.discount';
  final empty = 'process_receivings.order_items.empty';
  final offline = 'process_receivings.order_items.offline';
  final quantity = 'process_receivings.order_items.quantity';
}

class _LabelsProcessReceivingsOrders {
  const _LabelsProcessReceivingsOrders();

  final booking_date = 'process_receivings.orders.booking_date';
  final create_button = 'process_receivings.orders.create_button';
  final delivery_date = 'process_receivings.orders.delivery_date';
  final provisional_booking_date =
      'process_receivings.orders.provisional_booking_date';
  final title = 'process_receivings.orders.title';
  final total = 'process_receivings.orders.total';
  final view_pdf = const _LabelsProcessReceivingsOrdersViewPdf();
  final view_pdf_button = 'process_receivings.orders.view_pdf_button';
}

class _LabelsProcessReceivingsOrdersViewPdf {
  const _LabelsProcessReceivingsOrdersViewPdf();

  final close_button = 'process_receivings.orders.view_pdf.close_button';
}

class _LabelsProcessReceivingsProvisionalBooking {
  const _LabelsProcessReceivingsProvisionalBooking();

  final title = 'process_receivings.provisional_booking.title';
}

class _LabelsProcessReceivingsProvisionalBookings {
  const _LabelsProcessReceivingsProvisionalBookings();

  final booking_in_web_body =
      'process_receivings.provisional_bookings.booking_in_web_body';
  final booking_in_web_title =
      'process_receivings.provisional_bookings.booking_in_web_title';
  final can_be_booked_message =
      'process_receivings.provisional_bookings.can_be_booked_message';
  final filter = const _LabelsProcessReceivingsProvisionalBookingsFilter();
  final open_inventory_warning =
      'process_receivings.provisional_bookings.open_inventory_warning';
  final processing_warning =
      'process_receivings.provisional_bookings.processing_warning';
  final search_available_only_online =
      'process_receivings.provisional_bookings.search_available_only_online';
  final search_no_result =
      'process_receivings.provisional_bookings.search_no_result';
}

class _LabelsProcessReceivingsProvisionalBookingsFilter {
  const _LabelsProcessReceivingsProvisionalBookingsFilter();

  final order_date_range =
      'process_receivings.provisional_bookings.filter.order_date_range';
  final product_name =
      const _LabelsProcessReceivingsProvisionalBookingsFilterProductName();
  final requested_cost_center_name =
      const _LabelsProcessReceivingsProvisionalBookingsFilterRequestedCostCenterName();
  final sent_cost_center_name =
      const _LabelsProcessReceivingsProvisionalBookingsFilterSentCostCenterName();
  final sorting =
      const _LabelsProcessReceivingsProvisionalBookingsFilterSorting();
  final status =
      const _LabelsProcessReceivingsProvisionalBookingsFilterStatus();
  final status_label =
      'process_receivings.provisional_bookings.filter.status_label';
  final supplier_name =
      const _LabelsProcessReceivingsProvisionalBookingsFilterSupplierName();
  final user_name =
      const _LabelsProcessReceivingsProvisionalBookingsFilterUserName();
}

class _LabelsProcessReceivingsProvisionalBookingsFilterProductName {
  const _LabelsProcessReceivingsProvisionalBookingsFilterProductName();

  final hint_text =
      'process_receivings.provisional_bookings.filter.product_name.hint_text';
  final label =
      'process_receivings.provisional_bookings.filter.product_name.label';
}

class _LabelsProcessReceivingsProvisionalBookingsFilterRequestedCostCenterName {
  const _LabelsProcessReceivingsProvisionalBookingsFilterRequestedCostCenterName();

  final hint_text =
      'process_receivings.provisional_bookings.filter.requested_cost_center_name.hint_text';
  final label =
      'process_receivings.provisional_bookings.filter.requested_cost_center_name.label';
}

class _LabelsProcessReceivingsProvisionalBookingsFilterSentCostCenterName {
  const _LabelsProcessReceivingsProvisionalBookingsFilterSentCostCenterName();

  final hint_text =
      'process_receivings.provisional_bookings.filter.sent_cost_center_name.hint_text';
  final label =
      'process_receivings.provisional_bookings.filter.sent_cost_center_name.label';
}

class _LabelsProcessReceivingsProvisionalBookingsFilterSorting {
  const _LabelsProcessReceivingsProvisionalBookingsFilterSorting();

  final by_delivery_date_asc =
      'process_receivings.provisional_bookings.filter.sorting.by_delivery_date_asc';
  final by_delivery_date_desc =
      'process_receivings.provisional_bookings.filter.sorting.by_delivery_date_desc';
  final by_order_date_asc =
      'process_receivings.provisional_bookings.filter.sorting.by_order_date_asc';
  final by_order_date_desc =
      'process_receivings.provisional_bookings.filter.sorting.by_order_date_desc';
}

class _LabelsProcessReceivingsProvisionalBookingsFilterStatus {
  const _LabelsProcessReceivingsProvisionalBookingsFilterStatus();

  final active = 'process_receivings.provisional_bookings.filter.status.active';
  final can_be_booked =
      'process_receivings.provisional_bookings.filter.status.can_be_booked';
  final error = 'process_receivings.provisional_bookings.filter.status.error';
  final processed =
      'process_receivings.provisional_bookings.filter.status.processed';
  final processing =
      'process_receivings.provisional_bookings.filter.status.processing';
}

class _LabelsProcessReceivingsProvisionalBookingsFilterSupplierName {
  const _LabelsProcessReceivingsProvisionalBookingsFilterSupplierName();

  final hint_text =
      'process_receivings.provisional_bookings.filter.supplier_name.hint_text';
  final label =
      'process_receivings.provisional_bookings.filter.supplier_name.label';
}

class _LabelsProcessReceivingsProvisionalBookingsFilterUserName {
  const _LabelsProcessReceivingsProvisionalBookingsFilterUserName();

  final hint_text =
      'process_receivings.provisional_bookings.filter.user_name.hint_text';
  final label =
      'process_receivings.provisional_bookings.filter.user_name.label';
}

class _LabelsProcessReceivingsViewPdf {
  const _LabelsProcessReceivingsViewPdf();

  final subtitle = 'process_receivings.view_pdf.subtitle';
}

class _LabelsProduct {
  const _LabelsProduct();

  final change_offer_action = 'product.change_offer_action';
  final core_tag = 'product.core_tag';
  final current_offer_tag = 'product.current_offer_tag';
  final price_per_content_unit = 'product.price_per_content_unit';
  final price_per_order_unit = 'product.price_per_order_unit';
  final quantity = 'product.quantity';
  final total = 'product.total';
}

class _LabelsQuantityBottomSheet {
  const _LabelsQuantityBottomSheet();

  final quantity_error = const _LabelsQuantityBottomSheetQuantityError();
}

class _LabelsQuantityBottomSheetQuantityError {
  const _LabelsQuantityBottomSheetQuantityError();

  final fractions_number_not_allowed =
      'quantity_bottom_sheet.quantity_error.fractions_number_not_allowed';
  final infinity = 'quantity_bottom_sheet.quantity_error.infinity';
  final maximum_allowed_quantity =
      'quantity_bottom_sheet.quantity_error.maximum_allowed_quantity';
  final maximum_fraction_digits_exited =
      'quantity_bottom_sheet.quantity_error.maximum_fraction_digits_exited';
  final minimum_quantity =
      'quantity_bottom_sheet.quantity_error.minimum_quantity';
  final negative_value_is_not_allowed =
      'quantity_bottom_sheet.quantity_error.negative_value_is_not_allowed';
  final zero_value_not_allowed =
      'quantity_bottom_sheet.quantity_error.zero_value_not_allowed';
}

class _LabelsQuantitySelector {
  const _LabelsQuantitySelector();

  final speech = const _LabelsQuantitySelectorSpeech();
}

class _LabelsQuantitySelectorSpeech {
  const _LabelsQuantitySelectorSpeech();

  final error_unavailable =
      const _LabelsQuantitySelectorSpeechErrorUnavailable();
  final how_many_items = const _LabelsQuantitySelectorSpeechHowManyItems();
  final try_again_button = const _LabelsQuantitySelectorSpeechTryAgainButton();
  final unable_to_recognize_quantity =
      const _LabelsQuantitySelectorSpeechUnableToRecognizeQuantity();
}

class _LabelsQuantitySelectorSpeechErrorUnavailable {
  const _LabelsQuantitySelectorSpeechErrorUnavailable();

  final label = 'quantity_selector.speech.error_unavailable.label';
}

class _LabelsQuantitySelectorSpeechHowManyItems {
  const _LabelsQuantitySelectorSpeechHowManyItems();

  final label = 'quantity_selector.speech.how_many_items.label';
}

class _LabelsQuantitySelectorSpeechTryAgainButton {
  const _LabelsQuantitySelectorSpeechTryAgainButton();

  final label = 'quantity_selector.speech.try_again_button.label';
}

class _LabelsQuantitySelectorSpeechUnableToRecognizeQuantity {
  const _LabelsQuantitySelectorSpeechUnableToRecognizeQuantity();

  final label = 'quantity_selector.speech.unable_to_recognize_quantity.label';
}

class _LabelsReceiving {
  const _LabelsReceiving();

  final receiving_booking_result =
      const _LabelsReceivingReceivingBookingResult();
}

class _LabelsReceivingReceivingBookingResult {
  const _LabelsReceivingReceivingBookingResult();

  final booked_successfully =
      'receiving.receiving_booking_result.booked_successfully';
  final booking_error = 'receiving.receiving_booking_result.booking_error';
  final booking_status_unknown =
      'receiving.receiving_booking_result.booking_status_unknown';
  final export_failed_message =
      'receiving.receiving_booking_result.export_failed_message';
  final export_ok_message =
      'receiving.receiving_booking_result.export_ok_message';
  final label = 'receiving.receiving_booking_result.label';
  final open_delivery_note_button =
      const _LabelsReceivingReceivingBookingResultOpenDeliveryNoteButton();
}

class _LabelsReceivingReceivingBookingResultOpenDeliveryNoteButton {
  const _LabelsReceivingReceivingBookingResultOpenDeliveryNoteButton();

  final no_permissions_message =
      'receiving.receiving_booking_result.open_delivery_note_button.no_permissions_message';
  final title =
      'receiving.receiving_booking_result.open_delivery_note_button.title';
}

class _LabelsReceivings {
  const _LabelsReceivings();

  final astore = const _LabelsReceivingsAstore();
  final create = const _LabelsReceivingsCreate();
  final create_button_label = 'receivings.create_button_label';
  final deposit_items = const _LabelsReceivingsDepositItems();
  final filter = const _LabelsReceivingsFilter();
  final item_lookup = const _LabelsReceivingsItemLookup();
  final non_po_receivings = const _LabelsReceivingsNonPoReceivings();
  final order = const _LabelsReceivingsOrder();
  final order_item = const _LabelsReceivingsOrderItem();
  final order_items = const _LabelsReceivingsOrderItems();
  final orders = const _LabelsReceivingsOrders();
  final receiving = const _LabelsReceivingsReceiving();
  final receiving_cant_be_deleted_message =
      'receivings.receiving_cant_be_deleted_message';
  final receiving_delete_confirmation =
      const _LabelsReceivingsReceivingDeleteConfirmation();
  final receiving_delete_success_message =
      'receivings.receiving_delete_success_message';
  final search_available_only_online =
      'receivings.search_available_only_online';
  final title = 'receivings.title';
}

class _LabelsReceivingsAstore {
  const _LabelsReceivingsAstore();

  final order_merge_needed = 'receivings.astore.order_merge_needed';
  final orders_merge_needed = 'receivings.astore.orders_merge_needed';
}

class _LabelsReceivingsCreate {
  const _LabelsReceivingsCreate();

  final accountancy_cost_center_label =
      'receivings.create.accountancy_cost_center_label';
  final all_fields_are_required = 'receivings.create.all_fields_are_required';
  final button_label = 'receivings.create.button_label';
  final supplier_label = 'receivings.create.supplier_label';
  final title = 'receivings.create.title';
}

class _LabelsReceivingsDepositItems {
  const _LabelsReceivingsDepositItems();

  final available_only_online =
      'receivings.deposit_items.available_only_online';
  final delete_item_alert =
      const _LabelsReceivingsDepositItemsDeleteItemAlert();
  final item = const _LabelsReceivingsDepositItemsItem();
  final item_added_successfully_label =
      'receivings.deposit_items.item_added_successfully_label';
  final item_deleted_successfully_label =
      'receivings.deposit_items.item_deleted_successfully_label';
  final item_price_updated_successfully_label =
      'receivings.deposit_items.item_price_updated_successfully_label';
  final item_received_qty_updated_successfully_label =
      'receivings.deposit_items.item_received_qty_updated_successfully_label';
  final item_returned_qty_updated_successfully_label =
      'receivings.deposit_items.item_returned_qty_updated_successfully_label';
  final missing_info_alert = 'receivings.deposit_items.missing_info_alert';
  final no_items = 'receivings.deposit_items.no_items';
  final title = 'receivings.deposit_items.title';
}

class _LabelsReceivingsDepositItemsDeleteItemAlert {
  const _LabelsReceivingsDepositItemsDeleteItemAlert();

  final content = 'receivings.deposit_items.delete_item_alert.content';
  final no_action = 'receivings.deposit_items.delete_item_alert.no_action';
  final yes_action = 'receivings.deposit_items.delete_item_alert.yes_action';
}

class _LabelsReceivingsDepositItemsItem {
  const _LabelsReceivingsDepositItemsItem();

  final delete_item = const _LabelsReceivingsDepositItemsItemDeleteItem();
  final details = const _LabelsReceivingsDepositItemsItemDetails();
  final details_menu_button_label =
      'receivings.deposit_items.item.details_menu_button_label';
  final order_value = 'receivings.deposit_items.item.order_value';
  final price = 'receivings.deposit_items.item.price';
  final price_update_button_label =
      'receivings.deposit_items.item.price_update_button_label';
  final received_quantity = 'receivings.deposit_items.item.received_quantity';
  final returned_quantity = 'receivings.deposit_items.item.returned_quantity';
}

class _LabelsReceivingsDepositItemsItemDeleteItem {
  const _LabelsReceivingsDepositItemsItemDeleteItem();

  final button_label = 'receivings.deposit_items.item.delete_item.button_label';
}

class _LabelsReceivingsDepositItemsItemDetails {
  const _LabelsReceivingsDepositItemsItemDetails();

  final receiving_cost_type =
      'receivings.deposit_items.item.details.receiving_cost_type';
  final save_button_label =
      'receivings.deposit_items.item.details.save_button_label';
  final saved_successfully_label =
      'receivings.deposit_items.item.details.saved_successfully_label';
  final stock = 'receivings.deposit_items.item.details.stock';
  final title = 'receivings.deposit_items.item.details.title';
  final vat = 'receivings.deposit_items.item.details.vat';
}

class _LabelsReceivingsFilter {
  const _LabelsReceivingsFilter();

  final date_range = 'receivings.filter.date_range';
  final order_date = 'receivings.filter.order_date';
  final product_name = const _LabelsReceivingsFilterProductName();
  final requested_cost_center_name =
      const _LabelsReceivingsFilterRequestedCostCenterName();
  final sent_cost_center_name =
      const _LabelsReceivingsFilterSentCostCenterName();
  final supplier_name = const _LabelsReceivingsFilterSupplierName();
  final user_name = const _LabelsReceivingsFilterUserName();
}

class _LabelsReceivingsFilterProductName {
  const _LabelsReceivingsFilterProductName();

  final hint_text = 'receivings.filter.product_name.hint_text';
  final label = 'receivings.filter.product_name.label';
}

class _LabelsReceivingsFilterRequestedCostCenterName {
  const _LabelsReceivingsFilterRequestedCostCenterName();

  final hint_text = 'receivings.filter.requested_cost_center_name.hint_text';
  final label = 'receivings.filter.requested_cost_center_name.label';
}

class _LabelsReceivingsFilterSentCostCenterName {
  const _LabelsReceivingsFilterSentCostCenterName();

  final hint_text = 'receivings.filter.sent_cost_center_name.hint_text';
  final label = 'receivings.filter.sent_cost_center_name.label';
}

class _LabelsReceivingsFilterSupplierName {
  const _LabelsReceivingsFilterSupplierName();

  final hint_text = 'receivings.filter.supplier_name.hint_text';
  final label = 'receivings.filter.supplier_name.label';
}

class _LabelsReceivingsFilterUserName {
  const _LabelsReceivingsFilterUserName();

  final hint_text = 'receivings.filter.user_name.hint_text';
  final label = 'receivings.filter.user_name.label';
}

class _LabelsReceivingsItemLookup {
  const _LabelsReceivingsItemLookup();

  final action_button_label = 'receivings.item_lookup.action_button_label';
  final filter = const _LabelsReceivingsItemLookupFilter();
  final inventory_management_title =
      'receivings.item_lookup.inventory_management_title';
  final item_added_successfully_label =
      'receivings.item_lookup.item_added_successfully_label';
  final order_title = 'receivings.item_lookup.order_title';
  final search_available_only_online =
      'receivings.item_lookup.search_available_only_online';
  final supplier_title = 'receivings.item_lookup.supplier_title';
}

class _LabelsReceivingsItemLookupFilter {
  const _LabelsReceivingsItemLookupFilter();

  final sorting = const _LabelsReceivingsItemLookupFilterSorting();
}

class _LabelsReceivingsItemLookupFilterSorting {
  const _LabelsReceivingsItemLookupFilterSorting();

  final by_name_asc = 'receivings.item_lookup.filter.sorting.by_name_asc';
  final by_name_desc = 'receivings.item_lookup.filter.sorting.by_name_desc';
}

class _LabelsReceivingsNonPoReceivings {
  const _LabelsReceivingsNonPoReceivings();

  final filter = const _LabelsReceivingsNonPoReceivingsFilter();
}

class _LabelsReceivingsNonPoReceivingsFilter {
  const _LabelsReceivingsNonPoReceivingsFilter();

  final status = const _LabelsReceivingsNonPoReceivingsFilterStatus();
  final status_label = 'receivings.non_po_receivings.filter.status_label';
}

class _LabelsReceivingsNonPoReceivingsFilterStatus {
  const _LabelsReceivingsNonPoReceivingsFilterStatus();

  final all = 'receivings.non_po_receivings.filter.status.all';
  final in_approval = 'receivings.non_po_receivings.filter.status.in_approval';
  final not_in_approval =
      'receivings.non_po_receivings.filter.status.not_in_approval';
}

class _LabelsReceivingsOrder {
  const _LabelsReceivingsOrder();

  final confirm_alert = const _LabelsReceivingsOrderConfirmAlert();
  final confirmed_successfully = 'receivings.order.confirmed_successfully';
  final delivery_details = const _LabelsReceivingsOrderDeliveryDetails();
  final in_progress_label = 'receivings.order.in_progress_label';
  final provisional_book_alert =
      const _LabelsReceivingsOrderProvisionalBookAlert();
  final provisional_booking_successful =
      'receivings.order.provisional_booking_successful';
  final request_approval = const _LabelsReceivingsOrderRequestApproval();
  final start_receiving_process_alert =
      const _LabelsReceivingsOrderStartReceivingProcessAlert();
  final view_pdf_button = const _LabelsReceivingsOrderViewPdfButton();
}

class _LabelsReceivingsOrderConfirmAlert {
  const _LabelsReceivingsOrderConfirmAlert();

  final content = 'receivings.order.confirm_alert.content';
  final no_action = 'receivings.order.confirm_alert.no_action';
  final yes_action = 'receivings.order.confirm_alert.yes_action';
}

class _LabelsReceivingsOrderDeliveryDetails {
  const _LabelsReceivingsOrderDeliveryDetails();

  final booking_date_label =
      'receivings.order.delivery_details.booking_date_label';
  final booking_date_updated_successfully =
      'receivings.order.delivery_details.booking_date_updated_successfully';
  final checksum = 'receivings.order.delivery_details.checksum';
  final cost_center = 'receivings.order.delivery_details.cost_center';
  final cost_center_updated_successfully =
      'receivings.order.delivery_details.cost_center_updated_successfully';
  final date_dialog = const _LabelsReceivingsOrderDeliveryDetailsDateDialog();
  final delivery_note_date =
      'receivings.order.delivery_details.delivery_note_date';
  final delivery_note_date_updated_successfully =
      'receivings.order.delivery_details.delivery_note_date_updated_successfully';
  final delivery_note_edit =
      const _LabelsReceivingsOrderDeliveryDetailsDeliveryNoteEdit();
  final delivery_note_id_updated_successfully =
      'receivings.order.delivery_details.delivery_note_id_updated_successfully';
  final delivery_note_is_invoice =
      'receivings.order.delivery_details.delivery_note_is_invoice';
  final delivery_note_is_invoice_updated_successfully =
      'receivings.order.delivery_details.delivery_note_is_invoice_updated_successfully';
  final delivery_note_number =
      'receivings.order.delivery_details.delivery_note_number';
  final delivery_note_total =
      'receivings.order.delivery_details.delivery_note_total';
  final delivery_note_total_update_button_label =
      'receivings.order.delivery_details.delivery_note_total_update_button_label';
  final delivery_note_total_updated_successfully =
      'receivings.order.delivery_details.delivery_note_total_updated_successfully';
  final provisional_booking_date =
      'receivings.order.delivery_details.provisional_booking_date';
  final read_only = 'receivings.order.delivery_details.read_only';
  final title = 'receivings.order.delivery_details.title';
  final total_delivery_note =
      'receivings.order.delivery_details.total_delivery_note';
}

class _LabelsReceivingsOrderDeliveryDetailsDateDialog {
  const _LabelsReceivingsOrderDeliveryDetailsDateDialog();

  final cancel = 'receivings.order.delivery_details.date_dialog.cancel';
  final ok = 'receivings.order.delivery_details.date_dialog.ok';
  final select_date =
      'receivings.order.delivery_details.date_dialog.select_date';
}

class _LabelsReceivingsOrderDeliveryDetailsDeliveryNoteEdit {
  const _LabelsReceivingsOrderDeliveryDetailsDeliveryNoteEdit();

  final label = 'receivings.order.delivery_details.delivery_note_edit.label';
  final update = 'receivings.order.delivery_details.delivery_note_edit.update';
}

class _LabelsReceivingsOrderProvisionalBookAlert {
  const _LabelsReceivingsOrderProvisionalBookAlert();

  final book_action = 'receivings.order.provisional_book_alert.book_action';
  final content = 'receivings.order.provisional_book_alert.content';
  final order_number_label =
      'receivings.order.provisional_book_alert.order_number_label';
  final title = 'receivings.order.provisional_book_alert.title';
  final total_amount_label =
      'receivings.order.provisional_book_alert.total_amount_label';
}

class _LabelsReceivingsOrderRequestApproval {
  const _LabelsReceivingsOrderRequestApproval();

  final approver = 'receivings.order.request_approval.approver';
  final confirmation =
      const _LabelsReceivingsOrderRequestApprovalConfirmation();
  final cost_center = 'receivings.order.request_approval.cost_center';
  final requested_successfully =
      'receivings.order.request_approval.requested_successfully';
  final title = 'receivings.order.request_approval.title';
  final total = 'receivings.order.request_approval.total';
  final type = 'receivings.order.request_approval.type';
}

class _LabelsReceivingsOrderRequestApprovalConfirmation {
  const _LabelsReceivingsOrderRequestApprovalConfirmation();

  final title = 'receivings.order.request_approval.confirmation.title';
  final yes_action =
      'receivings.order.request_approval.confirmation.yes_action';
}

class _LabelsReceivingsOrderStartReceivingProcessAlert {
  const _LabelsReceivingsOrderStartReceivingProcessAlert();

  final content = 'receivings.order.start_receiving_process_alert.content';
  final no_action = 'receivings.order.start_receiving_process_alert.no_action';
  final yes_action =
      'receivings.order.start_receiving_process_alert.yes_action';
}

class _LabelsReceivingsOrderViewPdfButton {
  const _LabelsReceivingsOrderViewPdfButton();

  final label = 'receivings.order.view_pdf_button.label';
}

class _LabelsReceivingsOrderItem {
  const _LabelsReceivingsOrderItem();

  final add_comment_label = 'receivings.order_item.add_comment_label';
  final additional = const _LabelsReceivingsOrderItemAdditional();
  final additional_tab_title = 'receivings.order_item.additional_tab_title';
  final article = const _LabelsReceivingsOrderItemArticle();
  final article_number = 'receivings.order_item.article_number';
  final article_tab_title = 'receivings.order_item.article_tab_title';
  final article_temperature_updated_successfully =
      'receivings.order_item.article_temperature_updated_successfully';
  final calculation_unit_updated_successfully =
      'receivings.order_item.calculation_unit_updated_successfully';
  final calculation_units_per_order_unit_updated_successfully =
      'receivings.order_item.calculation_units_per_order_unit_updated_successfully';
  final category = 'receivings.order_item.category';
  final category_updated_successfully =
      'receivings.order_item.category_updated_successfully';
  final check = const _LabelsReceivingsOrderItemCheck();
  final check_tab_title = 'receivings.order_item.check_tab_title';
  final content_unit_updated_successfully =
      'receivings.order_item.content_unit_updated_successfully';
  final content_units = 'receivings.order_item.content_units';
  final content_units_per_order_unit_number =
      'receivings.order_item.content_units_per_order_unit_number';
  final cost_center = 'receivings.order_item.cost_center';
  final cost_type = 'receivings.order_item.cost_type';
  final discount = 'receivings.order_item.discount';
  final discount_updated_successfully =
      'receivings.order_item.discount_updated_successfully';
  final ean = 'receivings.order_item.ean';
  final expiration_date_updated_successfully =
      'receivings.order_item.expiration_date_updated_successfully';
  final field_is_read_only = 'receivings.order_item.field_is_read_only';
  final files = const _LabelsReceivingsOrderItemFiles();
  final inventory_unit = 'receivings.order_item.inventory_unit';
  final inventory_unit_factor = 'receivings.order_item.inventory_unit_factor';
  final inventory_unit_updated_successfully =
      'receivings.order_item.inventory_unit_updated_successfully';
  final inventory_units_lookup =
      const _LabelsReceivingsOrderItemInventoryUnitsLookup();
  final inventory_units_per_order_unit_updated_successfully =
      'receivings.order_item.inventory_units_per_order_unit_updated_successfully';
  final item_updated_successfully =
      'receivings.order_item.item_updated_successfully';
  final measure_button_label = 'receivings.order_item.measure_button_label';
  final number_of_content_units_per_order_unit_updated_successfully =
      'receivings.order_item.number_of_content_units_per_order_unit_updated_successfully';
  final order_unit = 'receivings.order_item.order_unit';
  final order_unit_updated_successfully =
      'receivings.order_item.order_unit_updated_successfully';
  final price_per_order_unit = 'receivings.order_item.price_per_order_unit';
  final quantity = 'receivings.order_item.quantity';
  final quantity_update_button_label =
      'receivings.order_item.quantity_update_button_label';
  final split_delivery = const _LabelsReceivingsOrderItemSplitDelivery();
  final split_tab_title = 'receivings.order_item.split_tab_title';
  final store = 'receivings.order_item.store';
  final tax_rate = 'receivings.order_item.tax_rate';
  final title = 'receivings.order_item.title';
  final total_amount_updated_successfully =
      'receivings.order_item.total_amount_updated_successfully';
  final truck_temperature_updated_successfully =
      'receivings.order_item.truck_temperature_updated_successfully';
  final unit_of_calculation = 'receivings.order_item.unit_of_calculation';
  final unit_of_calculation_factor =
      'receivings.order_item.unit_of_calculation_factor';
  final update_warning = 'receivings.order_item.update_warning';
  final vat_updated_successfully =
      'receivings.order_item.vat_updated_successfully';
  final weight_updated_successfully =
      'receivings.order_item.weight_updated_successfully';
}

class _LabelsReceivingsOrderItemAdditional {
  const _LabelsReceivingsOrderItemAdditional();

  final additional = const _LabelsReceivingsOrderItemAdditionalAdditional();
  final article_temperature_label =
      'receivings.order_item.additional.article_temperature_label';
  final attachment = const _LabelsReceivingsOrderItemAdditionalAttachment();
  final comment = const _LabelsReceivingsOrderItemAdditionalComment();
  final expiration_date_label =
      'receivings.order_item.additional.expiration_date_label';
  final truck_temperature_label =
      'receivings.order_item.additional.truck_temperature_label';
  final weight_label = 'receivings.order_item.additional.weight_label';
}

class _LabelsReceivingsOrderItemAdditionalAdditional {
  const _LabelsReceivingsOrderItemAdditionalAdditional();

  final title = 'receivings.order_item.additional.additional.title';
}

class _LabelsReceivingsOrderItemAdditionalAttachment {
  const _LabelsReceivingsOrderItemAdditionalAttachment();

  final title = 'receivings.order_item.additional.attachment.title';
}

class _LabelsReceivingsOrderItemAdditionalComment {
  const _LabelsReceivingsOrderItemAdditionalComment();

  final add_button_label =
      'receivings.order_item.additional.comment.add_button_label';
  final add_title = 'receivings.order_item.additional.comment.add_title';
  final added_successfully =
      'receivings.order_item.additional.comment.added_successfully';
  final delete_alert =
      const _LabelsReceivingsOrderItemAdditionalCommentDeleteAlert();
  final delete_button_label =
      'receivings.order_item.additional.comment.delete_button_label';
  final deleted_successfully =
      'receivings.order_item.additional.comment.deleted_successfully';
  final edit_button_label =
      'receivings.order_item.additional.comment.edit_button_label';
  final edit_title = 'receivings.order_item.additional.comment.edit_title';
  final edited_successfully =
      'receivings.order_item.additional.comment.edited_successfully';
  final field_label = 'receivings.order_item.additional.comment.field_label';
  final title = 'receivings.order_item.additional.comment.title';
}

class _LabelsReceivingsOrderItemAdditionalCommentDeleteAlert {
  const _LabelsReceivingsOrderItemAdditionalCommentDeleteAlert();

  final title = 'receivings.order_item.additional.comment.delete_alert.title';
}

class _LabelsReceivingsOrderItemArticle {
  const _LabelsReceivingsOrderItemArticle();

  final article_details = 'receivings.order_item.article.article_details';
  final price_per_inventory_unit =
      'receivings.order_item.article.price_per_inventory_unit';
}

class _LabelsReceivingsOrderItemCheck {
  const _LabelsReceivingsOrderItemCheck();

  final additional_label = 'receivings.order_item.check.additional_label';
  final content_unit = 'receivings.order_item.check.content_unit';
  final contents_label = 'receivings.order_item.check.contents_label';
  final delivery_quantity = 'receivings.order_item.check.delivery_quantity';
  final deposit_item_label = 'receivings.order_item.check.deposit_item_label';
  final discount = 'receivings.order_item.check.discount';
  final discount_value_description =
      'receivings.order_item.check.discount_value_description';
  final inventory_label = 'receivings.order_item.check.inventory_label';
  final inventory_unit = 'receivings.order_item.check.inventory_unit';
  final netto_amount = 'receivings.order_item.check.netto_amount';
  final order_info_label = 'receivings.order_item.check.order_info_label';
  final order_quantity = 'receivings.order_item.check.order_quantity';
  final order_unit = 'receivings.order_item.check.order_unit';
  final price_per_inventory_unit =
      'receivings.order_item.check.price_per_inventory_unit';
  final price_per_order_unit =
      'receivings.order_item.check.price_per_order_unit';
  final total_amount = 'receivings.order_item.check.total_amount';
  final total_update_button_label =
      'receivings.order_item.check.total_update_button_label';
}

class _LabelsReceivingsOrderItemFiles {
  const _LabelsReceivingsOrderItemFiles();

  final add_button_label = 'receivings.order_item.files.add_button_label';
  final delete_button_label = 'receivings.order_item.files.delete_button_label';
  final file_added_successfully =
      'receivings.order_item.files.file_added_successfully';
  final file_delete_confirmation =
      const _LabelsReceivingsOrderItemFilesFileDeleteConfirmation();
  final file_deleted_successfully =
      'receivings.order_item.files.file_deleted_successfully';
  final file_name_field_label =
      'receivings.order_item.files.file_name_field_label';
  final file_name_title = 'receivings.order_item.files.file_name_title';
}

class _LabelsReceivingsOrderItemFilesFileDeleteConfirmation {
  const _LabelsReceivingsOrderItemFilesFileDeleteConfirmation();

  final file_name =
      'receivings.order_item.files.file_delete_confirmation.file_name';
  final item_name =
      'receivings.order_item.files.file_delete_confirmation.item_name';
  final title = 'receivings.order_item.files.file_delete_confirmation.title';
}

class _LabelsReceivingsOrderItemInventoryUnitsLookup {
  const _LabelsReceivingsOrderItemInventoryUnitsLookup();

  final title = 'receivings.order_item.inventory_units_lookup.title';
}

class _LabelsReceivingsOrderItemSplitDelivery {
  const _LabelsReceivingsOrderItemSplitDelivery();

  final add_dialog = const _LabelsReceivingsOrderItemSplitDeliveryAddDialog();
  final added_successfully =
      'receivings.order_item.split_delivery.added_successfully';
  final cost_center_updated_successfully =
      'receivings.order_item.split_delivery.cost_center_updated_successfully';
  final cost_type_updated_successfully =
      'receivings.order_item.split_delivery.cost_type_updated_successfully';
  final delete_dialog =
      const _LabelsReceivingsOrderItemSplitDeliveryDeleteDialog();
  final deleted_successfully =
      'receivings.order_item.split_delivery.deleted_successfully';
  final no_items = 'receivings.order_item.split_delivery.no_items';
  final qty_updated_successfully =
      'receivings.order_item.split_delivery.qty_updated_successfully';
  final quantity = 'receivings.order_item.split_delivery.quantity';
  final quantity_update_button =
      'receivings.order_item.split_delivery.quantity_update_button';
  final remove = 'receivings.order_item.split_delivery.remove';
  final select_cost_center_first =
      'receivings.order_item.split_delivery.select_cost_center_first';
  final split_delivery_item_title =
      'receivings.order_item.split_delivery.split_delivery_item_title';
  final split_delivery_items_title =
      'receivings.order_item.split_delivery.split_delivery_items_title';
  final store_updated_successfully =
      'receivings.order_item.split_delivery.store_updated_successfully';
}

class _LabelsReceivingsOrderItemSplitDeliveryAddDialog {
  const _LabelsReceivingsOrderItemSplitDeliveryAddDialog();

  final no_action = 'receivings.order_item.split_delivery.add_dialog.no_action';
  final title = 'receivings.order_item.split_delivery.add_dialog.title';
  final yes_action =
      'receivings.order_item.split_delivery.add_dialog.yes_action';
}

class _LabelsReceivingsOrderItemSplitDeliveryDeleteDialog {
  const _LabelsReceivingsOrderItemSplitDeliveryDeleteDialog();

  final no_action =
      'receivings.order_item.split_delivery.delete_dialog.no_action';
  final title = 'receivings.order_item.split_delivery.delete_dialog.title';
  final yes_action =
      'receivings.order_item.split_delivery.delete_dialog.yes_action';
}

class _LabelsReceivingsOrderItems {
  const _LabelsReceivingsOrderItems();

  final add = const _LabelsReceivingsOrderItemsAdd();
  final add_all_receiving_items_button_label =
      'receivings.order_items.add_all_receiving_items_button_label';
  final add_discount = const _LabelsReceivingsOrderItemsAddDiscount();
  final add_discount_button_label =
      'receivings.order_items.add_discount_button_label';
  final delete_all_alert = const _LabelsReceivingsOrderItemsDeleteAllAlert();
  final delete_all_receiving_items_button_label =
      'receivings.order_items.delete_all_receiving_items_button_label';
  final deleted_all_successfully =
      'receivings.order_items.deleted_all_successfully';
  final discount_updated_successfully_label =
      'receivings.order_items.discount_updated_successfully_label';
  final filter = const _LabelsReceivingsOrderItemsFilter();
  final item_added_successfully_label =
      'receivings.order_items.item_added_successfully_label';
  final items_added_successfully_label =
      'receivings.order_items.items_added_successfully_label';
  final number_of_items = 'receivings.order_items.number_of_items';
  final title = 'receivings.order_items.title';
  final total_amount = 'receivings.order_items.total_amount';
}

class _LabelsReceivingsOrderItemsAdd {
  const _LabelsReceivingsOrderItemsAdd();

  final item_from_inventory_management =
      'receivings.order_items.add.item_from_inventory_management';
  final item_from_order = 'receivings.order_items.add.item_from_order';
  final item_from_supplier = 'receivings.order_items.add.item_from_supplier';
}

class _LabelsReceivingsOrderItemsAddDiscount {
  const _LabelsReceivingsOrderItemsAddDiscount();

  final discount_field_label =
      'receivings.order_items.add_discount.discount_field_label';
  final discount_value_description =
      'receivings.order_items.add_discount.discount_value_description';
  final update_button_label =
      'receivings.order_items.add_discount.update_button_label';
}

class _LabelsReceivingsOrderItemsDeleteAllAlert {
  const _LabelsReceivingsOrderItemsDeleteAllAlert();

  final content = 'receivings.order_items.delete_all_alert.content';
  final title = 'receivings.order_items.delete_all_alert.title';
}

class _LabelsReceivingsOrderItemsFilter {
  const _LabelsReceivingsOrderItemsFilter();

  final sorting = const _LabelsReceivingsOrderItemsFilterSorting();
}

class _LabelsReceivingsOrderItemsFilterSorting {
  const _LabelsReceivingsOrderItemsFilterSorting();

  final by_id_asc = 'receivings.order_items.filter.sorting.by_id_asc';
  final by_id_desc = 'receivings.order_items.filter.sorting.by_id_desc';
  final by_name_asc = 'receivings.order_items.filter.sorting.by_name_asc';
  final by_name_desc = 'receivings.order_items.filter.sorting.by_name_desc';
}

class _LabelsReceivingsOrders {
  const _LabelsReceivingsOrders();

  final filter = const _LabelsReceivingsOrdersFilter();
  final search_available_only_online =
      'receivings.orders.search_available_only_online';
  final search_no_result = 'receivings.orders.search_no_result';
  final title = 'receivings.orders.title';
}

class _LabelsReceivingsOrdersFilter {
  const _LabelsReceivingsOrdersFilter();

  final delivery_date = 'receivings.orders.filter.delivery_date';
  final order_date = 'receivings.orders.filter.order_date';
  final order_id = const _LabelsReceivingsOrdersFilterOrderId();
  final order_type = const _LabelsReceivingsOrdersFilterOrderType();
  final product_name = const _LabelsReceivingsOrdersFilterProductName();
  final requested_cost_center_name =
      const _LabelsReceivingsOrdersFilterRequestedCostCenterName();
  final sent_cost_center_name =
      const _LabelsReceivingsOrdersFilterSentCostCenterName();
  final sorting = const _LabelsReceivingsOrdersFilterSorting();
  final status = const _LabelsReceivingsOrdersFilterStatus();
  final status_label = 'receivings.orders.filter.status_label';
  final supplier_name = const _LabelsReceivingsOrdersFilterSupplierName();
  final user_name = const _LabelsReceivingsOrdersFilterUserName();
}

class _LabelsReceivingsOrdersFilterOrderId {
  const _LabelsReceivingsOrdersFilterOrderId();

  final hint_text = 'receivings.orders.filter.order_id.hint_text';
  final label = 'receivings.orders.filter.order_id.label';
}

class _LabelsReceivingsOrdersFilterOrderType {
  const _LabelsReceivingsOrdersFilterOrderType();

  final all = 'receivings.orders.filter.order_type.all';
  final exact_match = 'receivings.orders.filter.order_type.exact_match';
  final hint_text = 'receivings.orders.filter.order_type.hint_text';
  final label = 'receivings.orders.filter.order_type.label';
  final partial = 'receivings.orders.filter.order_type.partial';
  final related = 'receivings.orders.filter.order_type.related';
}

class _LabelsReceivingsOrdersFilterProductName {
  const _LabelsReceivingsOrdersFilterProductName();

  final hint_text = 'receivings.orders.filter.product_name.hint_text';
  final label = 'receivings.orders.filter.product_name.label';
}

class _LabelsReceivingsOrdersFilterRequestedCostCenterName {
  const _LabelsReceivingsOrdersFilterRequestedCostCenterName();

  final hint_text =
      'receivings.orders.filter.requested_cost_center_name.hint_text';
  final label = 'receivings.orders.filter.requested_cost_center_name.label';
}

class _LabelsReceivingsOrdersFilterSentCostCenterName {
  const _LabelsReceivingsOrdersFilterSentCostCenterName();

  final hint_text = 'receivings.orders.filter.sent_cost_center_name.hint_text';
  final label = 'receivings.orders.filter.sent_cost_center_name.label';
}

class _LabelsReceivingsOrdersFilterSorting {
  const _LabelsReceivingsOrdersFilterSorting();

  final by_delivery_date_asc =
      'receivings.orders.filter.sorting.by_delivery_date_asc';
  final by_delivery_date_desc =
      'receivings.orders.filter.sorting.by_delivery_date_desc';
  final by_order_date_asc =
      'receivings.orders.filter.sorting.by_order_date_asc';
  final by_order_date_desc =
      'receivings.orders.filter.sorting.by_order_date_desc';
}

class _LabelsReceivingsOrdersFilterStatus {
  const _LabelsReceivingsOrdersFilterStatus();

  final all = 'receivings.orders.filter.status.all';
  final in_progress = 'receivings.orders.filter.status.in_progress';
  final pending = 'receivings.orders.filter.status.pending';
  final today = 'receivings.orders.filter.status.today';
}

class _LabelsReceivingsOrdersFilterSupplierName {
  const _LabelsReceivingsOrdersFilterSupplierName();

  final hint_text = 'receivings.orders.filter.supplier_name.hint_text';
  final label = 'receivings.orders.filter.supplier_name.label';
}

class _LabelsReceivingsOrdersFilterUserName {
  const _LabelsReceivingsOrdersFilterUserName();

  final hint_text = 'receivings.orders.filter.user_name.hint_text';
  final label = 'receivings.orders.filter.user_name.label';
}

class _LabelsReceivingsReceiving {
  const _LabelsReceivingsReceiving();

  final add_order = const _LabelsReceivingsReceivingAddOrder();
  final additional_delivery =
      const _LabelsReceivingsReceivingAdditionalDelivery();
  final attachments = const _LabelsReceivingsReceivingAttachments();
  final available_orders = const _LabelsReceivingsReceivingAvailableOrders();
  final details = const _LabelsReceivingsReceivingDetails();
  final order = const _LabelsReceivingsReceivingOrder();
  final partial_history = const _LabelsReceivingsReceivingPartialHistory();
  final related_orders = const _LabelsReceivingsReceivingRelatedOrders();
  final subsequent_delivery =
      const _LabelsReceivingsReceivingSubsequentDelivery();
}

class _LabelsReceivingsReceivingAddOrder {
  const _LabelsReceivingsReceivingAddOrder();

  final title = 'receivings.receiving.add_order.title';
}

class _LabelsReceivingsReceivingAdditionalDelivery {
  const _LabelsReceivingsReceivingAdditionalDelivery();

  final label = 'receivings.receiving.additional_delivery.label';
}

class _LabelsReceivingsReceivingAttachments {
  const _LabelsReceivingsReceivingAttachments();

  final online_only = 'receivings.receiving.attachments.online_only';
  final title = 'receivings.receiving.attachments.title';
}

class _LabelsReceivingsReceivingAvailableOrders {
  const _LabelsReceivingsReceivingAvailableOrders();

  final available_only_online =
      'receivings.receiving.available_orders.available_only_online';
  final no_orders = 'receivings.receiving.available_orders.no_orders';
}

class _LabelsReceivingsReceivingDetails {
  const _LabelsReceivingsReceivingDetails();

  final add_freight_costs_button_label =
      'receivings.receiving.details.add_freight_costs_button_label';
  final additional_orders_description =
      'receivings.receiving.details.additional_orders_description';
  final additional_orders_label =
      'receivings.receiving.details.additional_orders_label';
  final attachments_description =
      'receivings.receiving.details.attachments_description';
  final attachments_label = 'receivings.receiving.details.attachments_label';
  final available_only_online_label =
      'receivings.receiving.details.available_only_online_label';
  final checksum_section = 'receivings.receiving.details.checksum_section';
  final confirm_data_button_label =
      'receivings.receiving.details.confirm_data_button_label';
  final confirm_data_dialog =
      const _LabelsReceivingsReceivingDetailsConfirmDataDialog();
  final confirm_data_info = 'receivings.receiving.details.confirm_data_info';
  final confirm_data_info_label =
      'receivings.receiving.details.confirm_data_info_label';
  final cost_center = 'receivings.receiving.details.cost_center';
  final delivery_date = 'receivings.receiving.details.delivery_date';
  final delivery_details_description =
      'receivings.receiving.details.delivery_details_description';
  final delivery_details_label =
      'receivings.receiving.details.delivery_details_label';
  final delivery_section = 'receivings.receiving.details.delivery_section';
  final deposit_items_description =
      'receivings.receiving.details.deposit_items_description';
  final deposit_items_label =
      'receivings.receiving.details.deposit_items_label';
  final edit_unavailable = 'receivings.receiving.details.edit_unavailable';
  final edit_unavailable_due_to_approval_in_progress =
      'receivings.receiving.details.edit_unavailable_due_to_approval_in_progress';
  final exchange_rate = 'receivings.receiving.details.exchange_rate';
  final last_po = 'receivings.receiving.details.last_po';
  final number_of_items = 'receivings.receiving.details.number_of_items';
  final order_date_and_time =
      'receivings.receiving.details.order_date_and_time';
  final order_number = 'receivings.receiving.details.order_number';
  final orderer = 'receivings.receiving.details.orderer';
  final po = 'receivings.receiving.details.po';
  final split_delivery_report_section =
      'receivings.receiving.details.split_delivery_report_section';
  final subsequent_delivery_description =
      'receivings.receiving.details.subsequent_delivery_description';
  final subsequent_delivery_items_warning_label =
      'receivings.receiving.details.subsequent_delivery_items_warning_label';
  final subsequent_delivery_label =
      'receivings.receiving.details.subsequent_delivery_label';
  final supplier = 'receivings.receiving.details.supplier';
  final target_cost_centers_button_label =
      'receivings.receiving.details.target_cost_centers_button_label';
  final target_cost_centers_description =
      'receivings.receiving.details.target_cost_centers_description';
  final target_cost_centers_label =
      'receivings.receiving.details.target_cost_centers_label';
  final total_delivery_note =
      'receivings.receiving.details.total_delivery_note';
  final total_deposit_items =
      'receivings.receiving.details.total_deposit_items';
  final total_discount = 'receivings.receiving.details.total_discount';
  final total_items = 'receivings.receiving.details.total_items';
  final total_value_of_order =
      'receivings.receiving.details.total_value_of_order';
  final view_last_pdf_button_label =
      'receivings.receiving.details.view_last_pdf_button_label';
  final view_pdf_button_label =
      'receivings.receiving.details.view_pdf_button_label';
}

class _LabelsReceivingsReceivingDetailsConfirmDataDialog {
  const _LabelsReceivingsReceivingDetailsConfirmDataDialog();

  final content = 'receivings.receiving.details.confirm_data_dialog.content';
  final title = 'receivings.receiving.details.confirm_data_dialog.title';
}

class _LabelsReceivingsReceivingOrder {
  const _LabelsReceivingsReceivingOrder();

  final add_btn_label = 'receivings.receiving.order.add_btn_label';
  final delivery_date = 'receivings.receiving.order.delivery_date';
  final order_date = 'receivings.receiving.order.order_date';
  final order_value = 'receivings.receiving.order.order_value';
  final original_order = 'receivings.receiving.order.original_order';
  final original_order_view_btn_label =
      'receivings.receiving.order.original_order_view_btn_label';
  final partial_history_btn_label =
      'receivings.receiving.order.partial_history_btn_label';
  final pdf_view_btn_label = 'receivings.receiving.order.pdf_view_btn_label';
}

class _LabelsReceivingsReceivingPartialHistory {
  const _LabelsReceivingsReceivingPartialHistory();

  final available_only_online =
      'receivings.receiving.partial_history.available_only_online';
  final date_of_booking =
      'receivings.receiving.partial_history.date_of_booking';
  final no_history = 'receivings.receiving.partial_history.no_history';
  final receiving_amount =
      'receivings.receiving.partial_history.receiving_amount';
  final title = 'receivings.receiving.partial_history.title';
}

class _LabelsReceivingsReceivingRelatedOrders {
  const _LabelsReceivingsReceivingRelatedOrders();

  final add_order_alert =
      const _LabelsReceivingsReceivingRelatedOrdersAddOrderAlert();
  final add_order_confirmation =
      const _LabelsReceivingsReceivingRelatedOrdersAddOrderConfirmation();
  final add_orders_confirmation =
      const _LabelsReceivingsReceivingRelatedOrdersAddOrdersConfirmation();
  final available_only_online =
      'receivings.receiving.related_orders.available_only_online';
  final delete_order_alert =
      const _LabelsReceivingsReceivingRelatedOrdersDeleteOrderAlert();
  final no_orders = 'receivings.receiving.related_orders.no_orders';
  final order_added_successfully =
      'receivings.receiving.related_orders.order_added_successfully';
  final order_deleted_successfully =
      'receivings.receiving.related_orders.order_deleted_successfully';
  final orders_added_partially =
      'receivings.receiving.related_orders.orders_added_partially';
  final orders_added_successfully =
      'receivings.receiving.related_orders.orders_added_successfully';
  final title = 'receivings.receiving.related_orders.title';
}

class _LabelsReceivingsReceivingRelatedOrdersAddOrderAlert {
  const _LabelsReceivingsReceivingRelatedOrdersAddOrderAlert();

  final no_order_selected =
      'receivings.receiving.related_orders.add_order_alert.no_order_selected';
}

class _LabelsReceivingsReceivingRelatedOrdersAddOrderConfirmation {
  const _LabelsReceivingsReceivingRelatedOrdersAddOrderConfirmation();

  final add_action =
      'receivings.receiving.related_orders.add_order_confirmation.add_action';
  final body =
      'receivings.receiving.related_orders.add_order_confirmation.body';
  final title =
      'receivings.receiving.related_orders.add_order_confirmation.title';
}

class _LabelsReceivingsReceivingRelatedOrdersAddOrdersConfirmation {
  const _LabelsReceivingsReceivingRelatedOrdersAddOrdersConfirmation();

  final title =
      'receivings.receiving.related_orders.add_orders_confirmation.title';
}

class _LabelsReceivingsReceivingRelatedOrdersDeleteOrderAlert {
  const _LabelsReceivingsReceivingRelatedOrdersDeleteOrderAlert();

  final content =
      'receivings.receiving.related_orders.delete_order_alert.content';
  final no_action =
      'receivings.receiving.related_orders.delete_order_alert.no_action';
  final yes_action =
      'receivings.receiving.related_orders.delete_order_alert.yes_action';
}

class _LabelsReceivingsReceivingSubsequentDelivery {
  const _LabelsReceivingsReceivingSubsequentDelivery();

  final all_articles_will_be_delivered_as_scheduled =
      'receivings.receiving.subsequent_delivery.all_articles_will_be_delivered_as_scheduled';
  final all_articles_will_be_delivered_later =
      'receivings.receiving.subsequent_delivery.all_articles_will_be_delivered_later';
  final all_will_be_delivered_later =
      'receivings.receiving.subsequent_delivery.all_will_be_delivered_later';
  final available_only_online =
      'receivings.receiving.subsequent_delivery.available_only_online';
  final item_quantity_difference =
      'receivings.receiving.subsequent_delivery.item_quantity_difference';
  final item_quantity_ordered =
      'receivings.receiving.subsequent_delivery.item_quantity_ordered';
  final item_quantity_received =
      'receivings.receiving.subsequent_delivery.item_quantity_received';
  final no_additional_delivered_articles =
      'receivings.receiving.subsequent_delivery.no_additional_delivered_articles';
  final no_items = 'receivings.receiving.subsequent_delivery.no_items';
  final will_be_delivered_as_scheduled =
      'receivings.receiving.subsequent_delivery.will_be_delivered_as_scheduled';
  final will_be_delivered_later =
      'receivings.receiving.subsequent_delivery.will_be_delivered_later';
}

class _LabelsReceivingsReceivingDeleteConfirmation {
  const _LabelsReceivingsReceivingDeleteConfirmation();

  final title = 'receivings.receiving_delete_confirmation.title';
}

class _LabelsRecipes {
  const _LabelsRecipes();

  final add_to_favorites_error = 'recipes.add_to_favorites_error';
  final categories = const _LabelsRecipesCategories();
  final categories_tab = 'recipes.categories_tab';
  final favorites_tab = 'recipes.favorites_tab';
  final filter = const _LabelsRecipesFilter();
  final notes = const _LabelsRecipesNotes();
  final online_search_only = 'recipes.online_search_only';
  final recipe = const _LabelsRecipesRecipe();
  final recipes_tab = 'recipes.recipes_tab';
}

class _LabelsRecipesCategories {
  const _LabelsRecipesCategories();

  final category = const _LabelsRecipesCategoriesCategory();
  final filter = const _LabelsRecipesCategoriesFilter();
  final online_search_only = 'recipes.categories.online_search_only';
}

class _LabelsRecipesCategoriesCategory {
  const _LabelsRecipesCategoriesCategory();

  final recipes_count = 'recipes.categories.category.recipes_count';
}

class _LabelsRecipesCategoriesFilter {
  const _LabelsRecipesCategoriesFilter();

  final sorting = const _LabelsRecipesCategoriesFilterSorting();
}

class _LabelsRecipesCategoriesFilterSorting {
  const _LabelsRecipesCategoriesFilterSorting();

  final by_name_asc = 'recipes.categories.filter.sorting.by_name_asc';
  final by_name_decs = 'recipes.categories.filter.sorting.by_name_decs';
}

class _LabelsRecipesFilter {
  const _LabelsRecipesFilter();

  final category_any_label = 'recipes.filter.category_any_label';
  final category_label = 'recipes.filter.category_label';
  final favorites_all_label = 'recipes.filter.favorites_all_label';
  final favorites_label = 'recipes.filter.favorites_label';
  final favorites_only_label = 'recipes.filter.favorites_only_label';
  final sorting = const _LabelsRecipesFilterSorting();
}

class _LabelsRecipesFilterSorting {
  const _LabelsRecipesFilterSorting();

  final by_last_updated_asc = 'recipes.filter.sorting.by_last_updated_asc';
  final by_last_updated_desc = 'recipes.filter.sorting.by_last_updated_desc';
  final by_name_asc = 'recipes.filter.sorting.by_name_asc';
  final by_name_desc = 'recipes.filter.sorting.by_name_desc';
  final by_sell_price_asc = 'recipes.filter.sorting.by_sell_price_asc';
  final by_sell_price_desc = 'recipes.filter.sorting.by_sell_price_desc';
}

class _LabelsRecipesNotes {
  const _LabelsRecipesNotes();

  final add_label = 'recipes.notes.add_label';
  final add_success = 'recipes.notes.add_success';
  final delete_confirm_content = 'recipes.notes.delete_confirm_content';
  final delete_confirm_no_label = 'recipes.notes.delete_confirm_no_label';
  final delete_confirm_yes_label = 'recipes.notes.delete_confirm_yes_label';
  final delete_success = 'recipes.notes.delete_success';
  final edit_note = const _LabelsRecipesNotesEditNote();
  final edit_success = 'recipes.notes.edit_success';
  final filter = const _LabelsRecipesNotesFilter();
  final new_note = const _LabelsRecipesNotesNewNote();
  final only_online = 'recipes.notes.only_online';
  final title = 'recipes.notes.title';
}

class _LabelsRecipesNotesEditNote {
  const _LabelsRecipesNotesEditNote();

  final button_label = 'recipes.notes.edit_note.button_label';
  final label = 'recipes.notes.edit_note.label';
  final title = 'recipes.notes.edit_note.title';
}

class _LabelsRecipesNotesFilter {
  const _LabelsRecipesNotesFilter();

  final sorting = const _LabelsRecipesNotesFilterSorting();
}

class _LabelsRecipesNotesFilterSorting {
  const _LabelsRecipesNotesFilterSorting();

  final by_date_added_asc = 'recipes.notes.filter.sorting.by_date_added_asc';
  final by_date_added_desc = 'recipes.notes.filter.sorting.by_date_added_desc';
  final by_date_updated_asc =
      'recipes.notes.filter.sorting.by_date_updated_asc';
  final by_date_updated_desc =
      'recipes.notes.filter.sorting.by_date_updated_desc';
  final by_user_added_asc = 'recipes.notes.filter.sorting.by_user_added_asc';
  final by_user_added_desc = 'recipes.notes.filter.sorting.by_user_added_desc';
  final by_user_updated_asc =
      'recipes.notes.filter.sorting.by_user_updated_asc';
  final by_user_updated_desc =
      'recipes.notes.filter.sorting.by_user_updated_desc';
}

class _LabelsRecipesNotesNewNote {
  const _LabelsRecipesNotesNewNote();

  final button_label = 'recipes.notes.new_note.button_label';
  final label = 'recipes.notes.new_note.label';
  final title = 'recipes.notes.new_note.title';
}

class _LabelsRecipesRecipe {
  const _LabelsRecipesRecipe();

  final allergens_and_additives =
      const _LabelsRecipesRecipeAllergensAndAdditives();
  final allergens_and_additives_link =
      'recipes.recipe.allergens_and_additives_link';
  final calculation = const _LabelsRecipesRecipeCalculation();
  final cost_of_sales_label = 'recipes.recipe.cost_of_sales_label';
  final costs_link = 'recipes.recipe.costs_link';
  final ingredients = const _LabelsRecipesRecipeIngredients();
  final ingredients_link = 'recipes.recipe.ingredients_link';
  final inventory_unit_label = 'recipes.recipe.inventory_unit_label';
  final notes_link = 'recipes.recipe.notes_link';
  final nutrients = const _LabelsRecipesRecipeNutrients();
  final nutrients_link = 'recipes.recipe.nutrients_link';
  final order = const _LabelsRecipesRecipeOrder();
  final order_item_suppliers = const _LabelsRecipesRecipeOrderItemSuppliers();
  final order_items = const _LabelsRecipesRecipeOrderItems();
  final preparation_steps = const _LabelsRecipesRecipePreparationSteps();
  final preparation_steps_link = 'recipes.recipe.preparation_steps_link';
  final preparation_time = const _LabelsRecipesRecipePreparationTime();
  final preparation_time_label = 'recipes.recipe.preparation_time_label';
  final sale_price_label = 'recipes.recipe.sale_price_label';
  final share = const _LabelsRecipesRecipeShare();
  final title = 'recipes.recipe.title';
  final type_label = 'recipes.recipe.type_label';
}

class _LabelsRecipesRecipeAllergensAndAdditives {
  const _LabelsRecipesRecipeAllergensAndAdditives();

  final additives = const _LabelsRecipesRecipeAllergensAndAdditivesAdditives();
  final allergens = const _LabelsRecipesRecipeAllergensAndAdditivesAllergens();
  final no_additives = 'recipes.recipe.allergens_and_additives.no_additives';
  final no_allergens = 'recipes.recipe.allergens_and_additives.no_allergens';
  final title = 'recipes.recipe.allergens_and_additives.title';
}

class _LabelsRecipesRecipeAllergensAndAdditivesAdditives {
  const _LabelsRecipesRecipeAllergensAndAdditivesAdditives();

  final title = 'recipes.recipe.allergens_and_additives.additives.title';
}

class _LabelsRecipesRecipeAllergensAndAdditivesAllergens {
  const _LabelsRecipesRecipeAllergensAndAdditivesAllergens();

  final title = 'recipes.recipe.allergens_and_additives.allergens.title';
}

class _LabelsRecipesRecipeCalculation {
  const _LabelsRecipesRecipeCalculation();

  final cost_of_sale_title = 'recipes.recipe.calculation.cost_of_sale_title';
  final general_expanses_title =
      'recipes.recipe.calculation.general_expanses_title';
  final label = const _LabelsRecipesRecipeCalculationLabel();
  final manpower_requirements_title =
      'recipes.recipe.calculation.manpower_requirements_title';
  final only_online = 'recipes.recipe.calculation.only_online';
  final summary_title = 'recipes.recipe.calculation.summary_title';
  final title = 'recipes.recipe.calculation.title';
}

class _LabelsRecipesRecipeCalculationLabel {
  const _LabelsRecipesRecipeCalculationLabel();

  final calculative_profit_margin =
      'recipes.recipe.calculation.label.calculative_profit_margin';
  final calculative_sales_price =
      'recipes.recipe.calculation.label.calculative_sales_price';
  final cost_price = 'recipes.recipe.calculation.label.cost_price';
  final direct_mpwr_reqs = 'recipes.recipe.calculation.label.direct_mpwr_reqs';
  final expenses_cost_of_sales =
      'recipes.recipe.calculation.label.expenses_cost_of_sales';
  final hourly_rate = 'recipes.recipe.calculation.label.hourly_rate';
  final indirect_mpwr_reqs_prod =
      'recipes.recipe.calculation.label.indirect_mpwr_reqs_prod';
  final mpwr_reqs_prod = 'recipes.recipe.calculation.label.mpwr_reqs_prod';
  final mpwr_reqs_prod_recipe =
      'recipes.recipe.calculation.label.mpwr_reqs_prod_recipe';
  final not_billable_accompaniments =
      'recipes.recipe.calculation.label.not_billable_accompaniments';
  final semi_finished_goods_mpwr_reqs =
      'recipes.recipe.calculation.label.semi_finished_goods_mpwr_reqs';
  final specified_sales_price =
      'recipes.recipe.calculation.label.specified_sales_price';
  final total_cost_of_sales =
      'recipes.recipe.calculation.label.total_cost_of_sales';
  final total_cost_of_sales_gross_calc =
      'recipes.recipe.calculation.label.total_cost_of_sales_gross_calc';
  final total_cost_of_sales_portion =
      'recipes.recipe.calculation.label.total_cost_of_sales_portion';
  final total_mpwr_reqs_calc_portion =
      'recipes.recipe.calculation.label.total_mpwr_reqs_calc_portion';
  final total_mpwr_reqs_prod_portion =
      'recipes.recipe.calculation.label.total_mpwr_reqs_prod_portion';
  final total_target_cost_of_sales_calc =
      'recipes.recipe.calculation.label.total_target_cost_of_sales_calc';
}

class _LabelsRecipesRecipeIngredients {
  const _LabelsRecipesRecipeIngredients();

  final add_to_cart = const _LabelsRecipesRecipeIngredientsAddToCart();
  final add_to_cart_confirmation =
      'recipes.recipe.ingredients.add_to_cart_confirmation';
  final add_to_cart_success = 'recipes.recipe.ingredients.add_to_cart_success';
  final only_online = 'recipes.recipe.ingredients.only_online';
  final portions_label = 'recipes.recipe.ingredients.portions_label';
  final title = 'recipes.recipe.ingredients.title';
}

class _LabelsRecipesRecipeIngredientsAddToCart {
  const _LabelsRecipesRecipeIngredientsAddToCart();

  final no_label = 'recipes.recipe.ingredients.add_to_cart.no_label';
  final yes_label = 'recipes.recipe.ingredients.add_to_cart.yes_label';
}

class _LabelsRecipesRecipeNutrients {
  const _LabelsRecipesRecipeNutrients();

  final only_online = 'recipes.recipe.nutrients.only_online';
  final title = 'recipes.recipe.nutrients.title';
}

class _LabelsRecipesRecipeOrder {
  const _LabelsRecipesRecipeOrder();

  final item = const _LabelsRecipesRecipeOrderItem();
  final item_status = const _LabelsRecipesRecipeOrderItemStatus();
  final move_to_cart_label = 'recipes.recipe.order.move_to_cart_label';
  final move_to_cart_success = 'recipes.recipe.order.move_to_cart_success';
  final supplier_status = const _LabelsRecipesRecipeOrderSupplierStatus();
}

class _LabelsRecipesRecipeOrderItem {
  const _LabelsRecipesRecipeOrderItem();

  final meta_id_label = 'recipes.recipe.order.item.meta_id_label';
  final position_id_label = 'recipes.recipe.order.item.position_id_label';
  final quantity_label = 'recipes.recipe.order.item.quantity_label';
  final supplier_label = 'recipes.recipe.order.item.supplier_label';
}

class _LabelsRecipesRecipeOrderItemStatus {
  const _LabelsRecipesRecipeOrderItemStatus();

  final not_available = 'recipes.recipe.order.item_status.not_available';
}

class _LabelsRecipesRecipeOrderSupplierStatus {
  const _LabelsRecipesRecipeOrderSupplierStatus();

  final not_available = 'recipes.recipe.order.supplier_status.not_available';
  final not_selected = 'recipes.recipe.order.supplier_status.not_selected';
  final selected = 'recipes.recipe.order.supplier_status.selected';
  final unknown = 'recipes.recipe.order.supplier_status.unknown';
}

class _LabelsRecipesRecipeOrderItemSuppliers {
  const _LabelsRecipesRecipeOrderItemSuppliers();

  final save_label = 'recipes.recipe.order_item_suppliers.save_label';
  final title = 'recipes.recipe.order_item_suppliers.title';
}

class _LabelsRecipesRecipeOrderItems {
  const _LabelsRecipesRecipeOrderItems();

  final title = 'recipes.recipe.order_items.title';
}

class _LabelsRecipesRecipePreparationSteps {
  const _LabelsRecipesRecipePreparationSteps();

  final current_step_label =
      'recipes.recipe.preparation_steps.current_step_label';
  final final_step = 'recipes.recipe.preparation_steps.final_step';
  final no_steps = 'recipes.recipe.preparation_steps.no_steps';
  final only_online = 'recipes.recipe.preparation_steps.only_online';
  final step = const _LabelsRecipesRecipePreparationStepsStep();
  final title = 'recipes.recipe.preparation_steps.title';
}

class _LabelsRecipesRecipePreparationStepsStep {
  const _LabelsRecipesRecipePreparationStepsStep();

  final next = 'recipes.recipe.preparation_steps.step.next';
  final no_description = 'recipes.recipe.preparation_steps.step.no_description';
  final previous = 'recipes.recipe.preparation_steps.step.previous';
}

class _LabelsRecipesRecipePreparationTime {
  const _LabelsRecipesRecipePreparationTime();

  final minutes = 'recipes.recipe.preparation_time.minutes';
}

class _LabelsRecipesRecipeShare {
  const _LabelsRecipesRecipeShare();

  final title = 'recipes.recipe.share.title';
}

class _LabelsReports {
  const _LabelsReports();

  final coming_soon_label = 'reports.coming_soon_label';
  final filter = const _LabelsReportsFilter();
  final report_available_only_online = 'reports.report_available_only_online';
  final search_available_only_online = 'reports.search_available_only_online';
}

class _LabelsReportsFilter {
  const _LabelsReportsFilter();

  final category = const _LabelsReportsFilterCategory();
  final category_label = 'reports.filter.category_label';
}

class _LabelsReportsFilterCategory {
  const _LabelsReportsFilterCategory();

  final any = 'reports.filter.category.any';
}

class _LabelsRequestSupplierCustomerIdButton {
  const _LabelsRequestSupplierCustomerIdButton();

  final label = 'request_supplier_customer_id_button.label';
  final no_permission_for_requesting_customer_id =
      'request_supplier_customer_id_button.no_permission_for_requesting_customer_id';
  final offline_message = 'request_supplier_customer_id_button.offline_message';
}

class _LabelsSearch {
  const _LabelsSearch();

  final no_connection = 'search.no_connection';
  final no_results = 'search.no_results';
}

class _LabelsSettings {
  const _LabelsSettings();

  final account = const _LabelsSettingsAccount();
  final application_information = const _LabelsSettingsApplicationInformation();
  final apply_button_label = 'settings.apply_button_label';
  final delete_local_data_label = 'settings.delete_local_data_label';
  final language = 'settings.language';
  final main_division_time = 'settings.main_division_time';
  final reset_theme_to_default_label = 'settings.reset_theme_to_default_label';
  final server_time = 'settings.server_time';
  final system = const _LabelsSettingsSystem();
}

class _LabelsSettingsAccount {
  const _LabelsSettingsAccount();

  final cost_center = 'settings.account.cost_center';
  final device_time = 'settings.account.device_time';
  final label = 'settings.account.label';
  final unit = 'settings.account.unit';
}

class _LabelsSettingsApplicationInformation {
  const _LabelsSettingsApplicationInformation();

  final database_version = 'settings.application_information.database_version';
  final label = 'settings.application_information.label';
  final version = const _LabelsSettingsApplicationInformationVersion();
  final version_label = 'settings.application_information.version_label';
}

class _LabelsSettingsApplicationInformationVersion {
  const _LabelsSettingsApplicationInformationVersion();

  final outdated = 'settings.application_information.version.outdated';
  final up_to_date = 'settings.application_information.version.up_to_date';
  final update_asap = 'settings.application_information.version.update_asap';
}

class _LabelsSettingsSystem {
  const _LabelsSettingsSystem();

  final auto_send_order_after_approval =
      'settings.system.auto_send_order_after_approval';
  final bluetooth_devices = const _LabelsSettingsSystemBluetoothDevices();
  final confirmation_dialogs = 'settings.system.confirmation_dialogs';
  final label = 'settings.system.label';
  final licenses = const _LabelsSettingsSystemLicenses();
  final screen_layout = const _LabelsSettingsSystemScreenLayout();
  final stt = 'settings.system.stt';
  final update_on_sync = 'settings.system.update_on_sync';
}

class _LabelsSettingsSystemBluetoothDevices {
  const _LabelsSettingsSystemBluetoothDevices();

  final label = 'settings.system.bluetooth_devices.label';
}

class _LabelsSettingsSystemLicenses {
  const _LabelsSettingsSystemLicenses();

  final label = 'settings.system.licenses.label';
  final open_licenses_screen_label =
      'settings.system.licenses.open_licenses_screen_label';
}

class _LabelsSettingsSystemScreenLayout {
  const _LabelsSettingsSystemScreenLayout();

  final automatic = 'settings.system.screen_layout.automatic';
  final label = 'settings.system.screen_layout.label';
  final mobile = 'settings.system.screen_layout.mobile';
  final tablet = 'settings.system.screen_layout.tablet';
}

class _LabelsSetup {
  const _LabelsSetup();

  final try_again_button_label = 'setup.try_again_button_label';
}

class _LabelsSupplier {
  const _LabelsSupplier();

  final allow_free_text_orders_label = 'supplier.allow_free_text_orders_label';
  final can_send_orders_directly_after_approval =
      const _LabelsSupplierCanSendOrdersDirectlyAfterApproval();
  final contact_label = 'supplier.contact_label';
  final contact_person = 'supplier.contact_person';
  final contract = const _LabelsSupplierContract();
  final contract_documents = 'supplier.contract_documents';
  final contracts = const _LabelsSupplierContracts();
  final customer_id = 'supplier.customer_id';
  final customer_id_is_not_provided_label =
      'supplier.customer_id_is_not_provided_label';
  final customer_id_request = const _LabelsSupplierCustomerIdRequest();
  final customer_id_requested_at = 'supplier.customer_id_requested_at';
  final customer_id_requested_by = 'supplier.customer_id_requested_by';
  final customer_id_requested_label__named =
      'supplier.customer_id_requested_label__named';
  final delivery_days_label = 'supplier.delivery_days_label';
  final details_available_only_online =
      'supplier.details_available_only_online';
  final documents = const _LabelsSupplierDocuments();
  final documents_title = 'supplier.documents_title';
  final expired_contract_label = 'supplier.expired_contract_label';
  final fax_label = 'supplier.fax_label';
  final halal = const _LabelsSupplierHalal();
  final internet_label = 'supplier.internet_label';
  final no_permissions_to_update_message =
      'supplier.no_permissions_to_update_message';
  final oci_catalog_button_label = 'supplier.oci_catalog_button_label';
  final phone = 'supplier.phone';
  final products_button_label = 'supplier.products_button_label';
  final request_customer_id_button_label =
      'supplier.request_customer_id_button_label';
  final second_phone = 'supplier.second_phone';
  final send_orders_after_approval =
      const _LabelsSupplierSendOrdersAfterApproval();
  final soon_to_expire_contract_label =
      'supplier.soon_to_expire_contract_label';
  final terms_of_delivery_label = 'supplier.terms_of_delivery_label';
  final terms_of_payment_label = 'supplier.terms_of_payment_label';
  final title = 'supplier.title';
  final update_success_message = 'supplier.update_success_message';
}

class _LabelsSupplierCanSendOrdersDirectlyAfterApproval {
  const _LabelsSupplierCanSendOrdersDirectlyAfterApproval();

  final disabled = 'supplier.can_send_orders_directly_after_approval.disabled';
  final enabled_for_n =
      'supplier.can_send_orders_directly_after_approval.enabled_for_n';
  final enabled_only_for_one =
      'supplier.can_send_orders_directly_after_approval.enabled_only_for_one';
  final label = 'supplier.can_send_orders_directly_after_approval.label';
}

class _LabelsSupplierContract {
  const _LabelsSupplierContract();

  final halal = const _LabelsSupplierContractHalal();
}

class _LabelsSupplierContractHalal {
  const _LabelsSupplierContractHalal();

  final expired_halal_label = 'supplier.contract.halal.expired_halal_label';
  final halal_label = 'supplier.contract.halal.halal_label';
}

class _LabelsSupplierContracts {
  const _LabelsSupplierContracts();

  final contract = const _LabelsSupplierContractsContract();
}

class _LabelsSupplierContractsContract {
  const _LabelsSupplierContractsContract();

  final start_end_date = 'supplier.contracts.contract.start_end_date';
}

class _LabelsSupplierCustomerIdRequest {
  const _LabelsSupplierCustomerIdRequest();

  final alert = const _LabelsSupplierCustomerIdRequestAlert();
  final no_permission = 'supplier.customer_id_request.no_permission';
  final reply_to_email_filed_label =
      'supplier.customer_id_request.reply_to_email_filed_label';
  final requested_successfully =
      'supplier.customer_id_request.requested_successfully';
  final send_button = 'supplier.customer_id_request.send_button';
  final title = 'supplier.customer_id_request.title';
  final wrong_email_format_label =
      'supplier.customer_id_request.wrong_email_format_label';
}

class _LabelsSupplierCustomerIdRequestAlert {
  const _LabelsSupplierCustomerIdRequestAlert();

  final content = 'supplier.customer_id_request.alert.content';
  final no_action = 'supplier.customer_id_request.alert.no_action';
  final yes_action = 'supplier.customer_id_request.alert.yes_action';
}

class _LabelsSupplierDocuments {
  const _LabelsSupplierDocuments();

  final document = const _LabelsSupplierDocumentsDocument();
  final document_type = const _LabelsSupplierDocumentsDocumentType();
  final filter = const _LabelsSupplierDocumentsFilter();
  final no_files = 'supplier.documents.no_files';
  final private_message = 'supplier.documents.private_message';
  final search_available_only_online =
      'supplier.documents.search_available_only_online';
  final title = 'supplier.documents.title';
}

class _LabelsSupplierDocumentsDocument {
  const _LabelsSupplierDocumentsDocument();

  final expire_in_days = 'supplier.documents.document.expire_in_days';
  final expire_today = 'supplier.documents.document.expire_today';
  final expired = 'supplier.documents.document.expired';
  final halal = const _LabelsSupplierDocumentsDocumentHalal();
  final private = 'supplier.documents.document.private';
  final start_end_date = 'supplier.documents.document.start_end_date';
}

class _LabelsSupplierDocumentsDocumentHalal {
  const _LabelsSupplierDocumentsDocumentHalal();

  final articles = 'supplier.documents.document.halal.articles';
  final certificate = 'supplier.documents.document.halal.certificate';
}

class _LabelsSupplierDocumentsDocumentType {
  const _LabelsSupplierDocumentsDocumentType();

  final catalog = 'supplier.documents.document_type.catalog';
  final certificate = 'supplier.documents.document_type.certificate';
  final contract = 'supplier.documents.document_type.contract';
  final halal_certificate =
      'supplier.documents.document_type.halal_certificate';
  final other = 'supplier.documents.document_type.other';
  final unknown = 'supplier.documents.document_type.unknown';
}

class _LabelsSupplierDocumentsFilter {
  const _LabelsSupplierDocumentsFilter();

  final sorting = const _LabelsSupplierDocumentsFilterSorting();
  final status = const _LabelsSupplierDocumentsFilterStatus();
  final status_label = 'supplier.documents.filter.status_label';
  final type = const _LabelsSupplierDocumentsFilterType();
  final type_label = 'supplier.documents.filter.type_label';
}

class _LabelsSupplierDocumentsFilterSorting {
  const _LabelsSupplierDocumentsFilterSorting();

  final by_name_asc = 'supplier.documents.filter.sorting.by_name_asc';
  final by_name_desc = 'supplier.documents.filter.sorting.by_name_desc';
  final expiration_date = 'supplier.documents.filter.sorting.expiration_date';
  final recently_added = 'supplier.documents.filter.sorting.recently_added';
}

class _LabelsSupplierDocumentsFilterStatus {
  const _LabelsSupplierDocumentsFilterStatus();

  final all = 'supplier.documents.filter.status.all';
  final expire_soon = 'supplier.documents.filter.status.expire_soon';
  final expired = 'supplier.documents.filter.status.expired';
  final no_expiration_date =
      'supplier.documents.filter.status.no_expiration_date';
  final normal = 'supplier.documents.filter.status.normal';
}

class _LabelsSupplierDocumentsFilterType {
  const _LabelsSupplierDocumentsFilterType();

  final any = 'supplier.documents.filter.type.any';
  final catalog = 'supplier.documents.filter.type.catalog';
  final certificate = 'supplier.documents.filter.type.certificate';
  final certificate_halal = 'supplier.documents.filter.type.certificate_halal';
  final contract = 'supplier.documents.filter.type.contract';
  final other = 'supplier.documents.filter.type.other';
}

class _LabelsSupplierHalal {
  const _LabelsSupplierHalal();

  final document = const _LabelsSupplierHalalDocument();
  final no_files = 'supplier.halal.no_files';
  final no_preview = 'supplier.halal.no_preview';
  final product = const _LabelsSupplierHalalProduct();
  final products = const _LabelsSupplierHalalProducts();
  final start_end_date_alert = const _LabelsSupplierHalalStartEndDateAlert();
}

class _LabelsSupplierHalalDocument {
  const _LabelsSupplierHalalDocument();

  final date_unknown = 'supplier.halal.document.date_unknown';
  final loading_error = 'supplier.halal.document.loading_error';
  final open_in_external_app = 'supplier.halal.document.open_in_external_app';
}

class _LabelsSupplierHalalProduct {
  const _LabelsSupplierHalalProduct();

  final content_unit = 'supplier.halal.product.content_unit';
  final files = const _LabelsSupplierHalalProductFiles();
  final no_files = 'supplier.halal.product.no_files';
  final order_unit = 'supplier.halal.product.order_unit';
  final search_available_only_online =
      'supplier.halal.product.search_available_only_online';
}

class _LabelsSupplierHalalProductFiles {
  const _LabelsSupplierHalalProductFiles();

  final loading_error = 'supplier.halal.product.files.loading_error';
}

class _LabelsSupplierHalalProducts {
  const _LabelsSupplierHalalProducts();

  final online_search = 'supplier.halal.products.online_search';
}

class _LabelsSupplierHalalStartEndDateAlert {
  const _LabelsSupplierHalalStartEndDateAlert();

  final close_action = 'supplier.halal.start_end_date_alert.close_action';
  final show_certificate_action =
      'supplier.halal.start_end_date_alert.show_certificate_action';
  final title = 'supplier.halal.start_end_date_alert.title';
}

class _LabelsSupplierSendOrdersAfterApproval {
  const _LabelsSupplierSendOrdersAfterApproval();

  final cost_centers =
      const _LabelsSupplierSendOrdersAfterApprovalCostCenters();
}

class _LabelsSupplierSendOrdersAfterApprovalCostCenters {
  const _LabelsSupplierSendOrdersAfterApprovalCostCenters();

  final filter =
      const _LabelsSupplierSendOrdersAfterApprovalCostCentersFilter();
  final search_available_only_online =
      'supplier.send_orders_after_approval.cost_centers.search_available_only_online';
  final title = 'supplier.send_orders_after_approval.cost_centers.title';
}

class _LabelsSupplierSendOrdersAfterApprovalCostCentersFilter {
  const _LabelsSupplierSendOrdersAfterApprovalCostCentersFilter();

  final sorting =
      const _LabelsSupplierSendOrdersAfterApprovalCostCentersFilterSorting();
}

class _LabelsSupplierSendOrdersAfterApprovalCostCentersFilterSorting {
  const _LabelsSupplierSendOrdersAfterApprovalCostCentersFilterSorting();

  final by_id_asc =
      'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_id_asc';
  final by_id_desc =
      'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_id_desc';
  final by_name_asc =
      'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_name_asc';
  final by_name_desc =
      'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_name_desc';
}

class _LabelsSuppliers {
  const _LabelsSuppliers();

  final expired_contract_label = 'suppliers.expired_contract_label';
  final filter = const _LabelsSuppliersFilter();
  final no_permission_to_view_supplier_products_alert =
      const _LabelsSuppliersNoPermissionToViewSupplierProductsAlert();
  final search_available_only_online = 'suppliers.search_available_only_online';
  final soon_to_expire_contract_label =
      'suppliers.soon_to_expire_contract_label';
  final supplier_favorite_mark_updated_successfully =
      'suppliers.supplier_favorite_mark_updated_successfully';
}

class _LabelsSuppliersFilter {
  const _LabelsSuppliersFilter();

  final sorting = const _LabelsSuppliersFilterSorting();
  final status = const _LabelsSuppliersFilterStatus();
  final status_label = 'suppliers.filter.status_label';
}

class _LabelsSuppliersFilterSorting {
  const _LabelsSuppliersFilterSorting();

  final by_name_asc = 'suppliers.filter.sorting.by_name_asc';
  final by_name_desc = 'suppliers.filter.sorting.by_name_desc';
}

class _LabelsSuppliersFilterStatus {
  const _LabelsSuppliersFilterStatus();

  final all = 'suppliers.filter.status.all';
  final favorite = 'suppliers.filter.status.favorite';
  final wso = 'suppliers.filter.status.wso';
  final wsw = 'suppliers.filter.status.wsw';
}

class _LabelsSuppliersNoPermissionToViewSupplierProductsAlert {
  const _LabelsSuppliersNoPermissionToViewSupplierProductsAlert();

  final content =
      'suppliers.no_permission_to_view_supplier_products_alert.content';
}

class _LabelsSync {
  const _LabelsSync();

  final announcements = 'sync.announcements';
  final clear_cache_button_label = 'sync.clear_cache_button_label';
  final continue_button_label = 'sync.continue_button_label';
  final cost_center = 'sync.cost_center';
  final division = 'sync.division';
  final inhouse_lists_loading_progress = 'sync.inhouse_lists_loading_progress';
  final order_lists_loading_progress = 'sync.order_lists_loading_progress';
  final rebuild_catalog = 'sync.rebuild_catalog';
  final save_inhouse_lists = 'sync.save_inhouse_lists';
  final save_order_lists = 'sync.save_order_lists';
  final select_another_cost_center_button_label =
      'sync.select_another_cost_center_button_label';
  final theme = 'sync.theme';
  final try_again_button_label = 'sync.try_again_button_label';
}

class _LabelsTransferLists {
  const _LabelsTransferLists();

  final booking_approval_request =
      const _LabelsTransferListsBookingApprovalRequest();
  final cost_center_lookup = const _LabelsTransferListsCostCenterLookup();
  final filter = const _LabelsTransferListsFilter();
  final in_house_list = const _LabelsTransferListsInHouseList();
  final inter_property_list = const _LabelsTransferListsInterPropertyList();
  final order_place = const _LabelsTransferListsOrderPlace();
  final orders = const _LabelsTransferListsOrders();
  final transfer_list = const _LabelsTransferListsTransferList();
}

class _LabelsTransferListsBookingApprovalRequest {
  const _LabelsTransferListsBookingApprovalRequest();

  final approver_label =
      'transfer_lists.booking_approval_request.approver_label';
  final request_alert =
      const _LabelsTransferListsBookingApprovalRequestRequestAlert();
  final request_button_label =
      'transfer_lists.booking_approval_request.request_button_label';
  final sent_successfully_label =
      'transfer_lists.booking_approval_request.sent_successfully_label';
  final title = 'transfer_lists.booking_approval_request.title';
}

class _LabelsTransferListsBookingApprovalRequestRequestAlert {
  const _LabelsTransferListsBookingApprovalRequestRequestAlert();

  final content =
      'transfer_lists.booking_approval_request.request_alert.content';
  final no_action =
      'transfer_lists.booking_approval_request.request_alert.no_action';
  final yes_action =
      'transfer_lists.booking_approval_request.request_alert.yes_action';
}

class _LabelsTransferListsCostCenterLookup {
  const _LabelsTransferListsCostCenterLookup();

  final button_label = 'transfer_lists.cost_center_lookup.button_label';
  final search_available_only_online =
      'transfer_lists.cost_center_lookup.search_available_only_online';
  final title = 'transfer_lists.cost_center_lookup.title';
}

class _LabelsTransferListsFilter {
  const _LabelsTransferListsFilter();

  final sorting = const _LabelsTransferListsFilterSorting();
  final type = const _LabelsTransferListsFilterType();
  final type_label = 'transfer_lists.filter.type_label';
}

class _LabelsTransferListsFilterSorting {
  const _LabelsTransferListsFilterSorting();

  final by_default_asc = 'transfer_lists.filter.sorting.by_default_asc';
  final by_default_desc = 'transfer_lists.filter.sorting.by_default_desc';
  final by_name_asc = 'transfer_lists.filter.sorting.by_name_asc';
  final by_name_desc = 'transfer_lists.filter.sorting.by_name_desc';
}

class _LabelsTransferListsFilterType {
  const _LabelsTransferListsFilterType();

  final all = 'transfer_lists.filter.type.all';
  final in_house = 'transfer_lists.filter.type.in_house';
  final inter_property = 'transfer_lists.filter.type.inter_property';
}

class _LabelsTransferListsInHouseList {
  const _LabelsTransferListsInHouseList();

  final item_add = const _LabelsTransferListsInHouseListItemAdd();
}

class _LabelsTransferListsInHouseListItemAdd {
  const _LabelsTransferListsInHouseListItemAdd();

  final added_successfully_label =
      'transfer_lists.in_house_list.item_add.added_successfully_label';
  final button_label = 'transfer_lists.in_house_list.item_add.button_label';
  final search_available_only_online =
      'transfer_lists.in_house_list.item_add.search_available_only_online';
  final title = 'transfer_lists.in_house_list.item_add.title';
}

class _LabelsTransferListsInterPropertyList {
  const _LabelsTransferListsInterPropertyList();

  final item_add = const _LabelsTransferListsInterPropertyListItemAdd();
  final item_mapping = const _LabelsTransferListsInterPropertyListItemMapping();
  final item_merge = const _LabelsTransferListsInterPropertyListItemMerge();
  final item_remap = const _LabelsTransferListsInterPropertyListItemRemap();
  final item_to_add = const _LabelsTransferListsInterPropertyListItemToAdd();
  final source_division_lookup =
      const _LabelsTransferListsInterPropertyListSourceDivisionLookup();
  final target_division_lookup =
      const _LabelsTransferListsInterPropertyListTargetDivisionLookup();
}

class _LabelsTransferListsInterPropertyListItemAdd {
  const _LabelsTransferListsInterPropertyListItemAdd();

  final add_alert =
      const _LabelsTransferListsInterPropertyListItemAddAddAlert();
  final available_only_online =
      'transfer_lists.inter_property_list.item_add.available_only_online';
  final button_label =
      'transfer_lists.inter_property_list.item_add.button_label';
  final category_label =
      'transfer_lists.inter_property_list.item_add.category_label';
  final comment_label =
      'transfer_lists.inter_property_list.item_add.comment_label';
  final cost_type_label =
      'transfer_lists.inter_property_list.item_add.cost_type_label';
  final inventory_unit_average_price_label =
      'transfer_lists.inter_property_list.item_add.inventory_unit_average_price_label';
  final item_added_successfully_label =
      'transfer_lists.inter_property_list.item_add.item_added_successfully_label';
  final item_in_ims_updated_successfully_label =
      'transfer_lists.inter_property_list.item_add.item_in_ims_updated_successfully_label';
  final item_label = 'transfer_lists.inter_property_list.item_add.item_label';
  final select_source_division_first_label =
      'transfer_lists.inter_property_list.item_add.select_source_division_first_label';
  final source_division_label =
      'transfer_lists.inter_property_list.item_add.source_division_label';
  final store_label = 'transfer_lists.inter_property_list.item_add.store_label';
  final title = 'transfer_lists.inter_property_list.item_add.title';
  final update_comment_button_label =
      'transfer_lists.inter_property_list.item_add.update_comment_button_label';
}

class _LabelsTransferListsInterPropertyListItemAddAddAlert {
  const _LabelsTransferListsInterPropertyListItemAddAddAlert();

  final content =
      'transfer_lists.inter_property_list.item_add.add_alert.content';
  final no_action =
      'transfer_lists.inter_property_list.item_add.add_alert.no_action';
  final yes_action =
      'transfer_lists.inter_property_list.item_add.add_alert.yes_action';
}

class _LabelsTransferListsInterPropertyListItemMapping {
  const _LabelsTransferListsInterPropertyListItemMapping();

  final add_to_warehouse_alert =
      const _LabelsTransferListsInterPropertyListItemMappingAddToWarehouseAlert();
  final add_to_warehouse_button_label =
      'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_button_label';
  final add_to_warehouse_description =
      'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_description';
  final added_to_warehouse_successfully_label =
      'transfer_lists.inter_property_list.item_mapping.added_to_warehouse_successfully_label';
  final item_is_not_in_ims_label =
      'transfer_lists.inter_property_list.item_mapping.item_is_not_in_ims_label';
  final merge_item_button_label =
      'transfer_lists.inter_property_list.item_mapping.merge_item_button_label';
  final merge_item_description =
      'transfer_lists.inter_property_list.item_mapping.merge_item_description';
}

class _LabelsTransferListsInterPropertyListItemMappingAddToWarehouseAlert {
  const _LabelsTransferListsInterPropertyListItemMappingAddToWarehouseAlert();

  final content =
      'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_alert.content';
  final no_action =
      'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_alert.no_action';
  final yes_action =
      'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_alert.yes_action';
}

class _LabelsTransferListsInterPropertyListItemMerge {
  const _LabelsTransferListsInterPropertyListItemMerge();

  final button_label =
      'transfer_lists.inter_property_list.item_merge.button_label';
  final item = const _LabelsTransferListsInterPropertyListItemMergeItem();
  final merge_alert =
      const _LabelsTransferListsInterPropertyListItemMergeMergeAlert();
  final merged_successfully_label =
      'transfer_lists.inter_property_list.item_merge.merged_successfully_label';
  final search_available_only_online =
      'transfer_lists.inter_property_list.item_merge.search_available_only_online';
  final title = 'transfer_lists.inter_property_list.item_merge.title';
}

class _LabelsTransferListsInterPropertyListItemMergeItem {
  const _LabelsTransferListsInterPropertyListItemMergeItem();

  final can_not_be_merged_message =
      'transfer_lists.inter_property_list.item_merge.item.can_not_be_merged_message';
  final inventory_unit_not_mapped_message =
      'transfer_lists.inter_property_list.item_merge.item.inventory_unit_not_mapped_message';
}

class _LabelsTransferListsInterPropertyListItemMergeMergeAlert {
  const _LabelsTransferListsInterPropertyListItemMergeMergeAlert();

  final content =
      'transfer_lists.inter_property_list.item_merge.merge_alert.content';
  final no_action =
      'transfer_lists.inter_property_list.item_merge.merge_alert.no_action';
  final yes_action =
      'transfer_lists.inter_property_list.item_merge.merge_alert.yes_action';
}

class _LabelsTransferListsInterPropertyListItemRemap {
  const _LabelsTransferListsInterPropertyListItemRemap();

  final item = const _LabelsTransferListsInterPropertyListItemRemapItem();
  final no_items_to_select_from_label =
      'transfer_lists.inter_property_list.item_remap.no_items_to_select_from_label';
  final remap_alert =
      const _LabelsTransferListsInterPropertyListItemRemapRemapAlert();
  final remap_successfully_label =
      'transfer_lists.inter_property_list.item_remap.remap_successfully_label';
  final search_available_only_online =
      'transfer_lists.inter_property_list.item_remap.search_available_only_online';
  final title = 'transfer_lists.inter_property_list.item_remap.title';
}

class _LabelsTransferListsInterPropertyListItemRemapItem {
  const _LabelsTransferListsInterPropertyListItemRemapItem();

  final already_mapped =
      'transfer_lists.inter_property_list.item_remap.item.already_mapped';
  final inventory_unit_not_mapped_message =
      'transfer_lists.inter_property_list.item_remap.item.inventory_unit_not_mapped_message';
  final mapping_error_message =
      'transfer_lists.inter_property_list.item_remap.item.mapping_error_message';
  final select_button_label =
      'transfer_lists.inter_property_list.item_remap.item.select_button_label';
}

class _LabelsTransferListsInterPropertyListItemRemapRemapAlert {
  const _LabelsTransferListsInterPropertyListItemRemapRemapAlert();

  final content =
      'transfer_lists.inter_property_list.item_remap.remap_alert.content';
  final no_action =
      'transfer_lists.inter_property_list.item_remap.remap_alert.no_action';
  final yes_action =
      'transfer_lists.inter_property_list.item_remap.remap_alert.yes_action';
}

class _LabelsTransferListsInterPropertyListItemToAdd {
  const _LabelsTransferListsInterPropertyListItemToAdd();

  final button_label =
      'transfer_lists.inter_property_list.item_to_add.button_label';
  final item = const _LabelsTransferListsInterPropertyListItemToAddItem();
  final search_available_only_online =
      'transfer_lists.inter_property_list.item_to_add.search_available_only_online';
  final title = 'transfer_lists.inter_property_list.item_to_add.title';
}

class _LabelsTransferListsInterPropertyListItemToAddItem {
  const _LabelsTransferListsInterPropertyListItemToAddItem();

  final already_in_list_message =
      'transfer_lists.inter_property_list.item_to_add.item.already_in_list_message';
  final can_not_be_added_message =
      'transfer_lists.inter_property_list.item_to_add.item.can_not_be_added_message';
  final store_not_mapped_message =
      'transfer_lists.inter_property_list.item_to_add.item.store_not_mapped_message';
}

class _LabelsTransferListsInterPropertyListSourceDivisionLookup {
  const _LabelsTransferListsInterPropertyListSourceDivisionLookup();

  final button_label =
      'transfer_lists.inter_property_list.source_division_lookup.button_label';
  final search_available_only_online =
      'transfer_lists.inter_property_list.source_division_lookup.search_available_only_online';
  final title =
      'transfer_lists.inter_property_list.source_division_lookup.title';
}

class _LabelsTransferListsInterPropertyListTargetDivisionLookup {
  const _LabelsTransferListsInterPropertyListTargetDivisionLookup();

  final button_label =
      'transfer_lists.inter_property_list.target_division_lookup.button_label';
  final search_available_only_online =
      'transfer_lists.inter_property_list.target_division_lookup.search_available_only_online';
  final title =
      'transfer_lists.inter_property_list.target_division_lookup.title';
}

class _LabelsTransferListsOrderPlace {
  const _LabelsTransferListsOrderPlace();

  final comment_label = 'transfer_lists.order_place.comment_label';
  final delivery_date_label = 'transfer_lists.order_place.delivery_date_label';
  final in_house_list_order_placed_successfully =
      'transfer_lists.order_place.in_house_list_order_placed_successfully';
  final inter_property_list_order_placed_successfully =
      'transfer_lists.order_place.inter_property_list_order_placed_successfully';
  final order_alert = const _LabelsTransferListsOrderPlaceOrderAlert();
  final order_button_label = 'transfer_lists.order_place.order_button_label';
  final title = 'transfer_lists.order_place.title';
}

class _LabelsTransferListsOrderPlaceOrderAlert {
  const _LabelsTransferListsOrderPlaceOrderAlert();

  final content = 'transfer_lists.order_place.order_alert.content';
  final no_action = 'transfer_lists.order_place.order_alert.no_action';
  final yes_action = 'transfer_lists.order_place.order_alert.yes_action';
}

class _LabelsTransferListsOrders {
  const _LabelsTransferListsOrders();

  final filter = const _LabelsTransferListsOrdersFilter();
  final order = const _LabelsTransferListsOrdersOrder();
}

class _LabelsTransferListsOrdersFilter {
  const _LabelsTransferListsOrdersFilter();

  final sorting = const _LabelsTransferListsOrdersFilterSorting();
  final status = const _LabelsTransferListsOrdersFilterStatus();
  final status_label = 'transfer_lists.orders.filter.status_label';
}

class _LabelsTransferListsOrdersFilterSorting {
  const _LabelsTransferListsOrdersFilterSorting();

  final by_name_asc = 'transfer_lists.orders.filter.sorting.by_name_asc';
  final by_name_desc = 'transfer_lists.orders.filter.sorting.by_name_desc';
  final by_order_date_asc =
      'transfer_lists.orders.filter.sorting.by_order_date_asc';
  final by_order_date_desc =
      'transfer_lists.orders.filter.sorting.by_order_date_desc';
}

class _LabelsTransferListsOrdersFilterStatus {
  const _LabelsTransferListsOrdersFilterStatus();

  final any = 'transfer_lists.orders.filter.status.any';
  final booking_deleted = 'transfer_lists.orders.filter.status.booking_deleted';
  final confirmed = 'transfer_lists.orders.filter.status.confirmed';
  final delivered = 'transfer_lists.orders.filter.status.delivered';
  final empty = 'transfer_lists.orders.filter.status.empty';
  final not_approved = 'transfer_lists.orders.filter.status.not_approved';
  final pending = 'transfer_lists.orders.filter.status.pending';
  final unconfirmed = 'transfer_lists.orders.filter.status.unconfirmed';
}

class _LabelsTransferListsOrdersOrder {
  const _LabelsTransferListsOrdersOrder();

  final current_level_label = 'transfer_lists.orders.order.current_level_label';
  final last_approval_label = 'transfer_lists.orders.order.last_approval_label';
  final last_task_label = 'transfer_lists.orders.order.last_task_label';
  final products = const _LabelsTransferListsOrdersOrderProducts();
  final status = const _LabelsTransferListsOrdersOrderStatus();
  final view_document_button_label =
      'transfer_lists.orders.order.view_document_button_label';
}

class _LabelsTransferListsOrdersOrderProducts {
  const _LabelsTransferListsOrdersOrderProducts();

  final booking_deleted_label =
      'transfer_lists.orders.order.products.booking_deleted_label';
  final product = const _LabelsTransferListsOrdersOrderProductsProduct();
  final search_available_only_online =
      'transfer_lists.orders.order.products.search_available_only_online';
}

class _LabelsTransferListsOrdersOrderProductsProduct {
  const _LabelsTransferListsOrdersOrderProductsProduct();

  final source_division_label =
      'transfer_lists.orders.order.products.product.source_division_label';
  final source_item_label =
      'transfer_lists.orders.order.products.product.source_item_label';
  final total_amount =
      'transfer_lists.orders.order.products.product.total_amount';
}

class _LabelsTransferListsOrdersOrderStatus {
  const _LabelsTransferListsOrdersOrderStatus();

  final booking_deleted = 'transfer_lists.orders.order.status.booking_deleted';
  final confirmed = 'transfer_lists.orders.order.status.confirmed';
  final delivered = 'transfer_lists.orders.order.status.delivered';
  final empty = 'transfer_lists.orders.order.status.empty';
  final not_approved = 'transfer_lists.orders.order.status.not_approved';
  final pending = 'transfer_lists.orders.order.status.pending';
  final unconfirmed = 'transfer_lists.orders.order.status.unconfirmed';
  final unknown = 'transfer_lists.orders.order.status.unknown';
}

class _LabelsTransferListsTransferList {
  const _LabelsTransferListsTransferList();

  final create = const _LabelsTransferListsTransferListCreate();
  final delete_alert = const _LabelsTransferListsTransferListDeleteAlert();
  final deleted_successfully_label =
      'transfer_lists.transfer_list.deleted_successfully_label';
  final items = const _LabelsTransferListsTransferListItems();
  final menu = const _LabelsTransferListsTransferListMenu();
  final rename = const _LabelsTransferListsTransferListRename();
  final reorder_items = const _LabelsTransferListsTransferListReorderItems();
  final type = const _LabelsTransferListsTransferListType();
  final unsupported_type = 'transfer_lists.transfer_list.unsupported_type';
}

class _LabelsTransferListsTransferListCreate {
  const _LabelsTransferListsTransferListCreate();

  final button_label = 'transfer_lists.transfer_list.create.button_label';
  final cost_center_label =
      'transfer_lists.transfer_list.create.cost_center_label';
  final created_successfully =
      'transfer_lists.transfer_list.create.created_successfully';
  final editable_label = 'transfer_lists.transfer_list.create.editable_label';
  final name_field_label =
      'transfer_lists.transfer_list.create.name_field_label';
  final please_fill_in_all_values =
      'transfer_lists.transfer_list.create.please_fill_in_all_values';
  final title = const _LabelsTransferListsTransferListCreateTitle();
}

class _LabelsTransferListsTransferListCreateTitle {
  const _LabelsTransferListsTransferListCreateTitle();

  final in_house = 'transfer_lists.transfer_list.create.title.in_house';
  final inter_property =
      'transfer_lists.transfer_list.create.title.inter_property';
}

class _LabelsTransferListsTransferListDeleteAlert {
  const _LabelsTransferListsTransferListDeleteAlert();

  final content = 'transfer_lists.transfer_list.delete_alert.content';
  final no_action = 'transfer_lists.transfer_list.delete_alert.no_action';
  final yes_action = 'transfer_lists.transfer_list.delete_alert.yes_action';
}

class _LabelsTransferListsTransferListItems {
  const _LabelsTransferListsTransferListItems();

  final item = const _LabelsTransferListsTransferListItemsItem();
  final search_available_only_online =
      'transfer_lists.transfer_list.items.search_available_only_online';
}

class _LabelsTransferListsTransferListItemsItem {
  const _LabelsTransferListsTransferListItemsItem();

  final errors = const _LabelsTransferListsTransferListItemsItemErrors();
  final order_not_possible_due_to_errors =
      'transfer_lists.transfer_list.items.item.order_not_possible_due_to_errors';
  final qty_updated_successfully_label =
      'transfer_lists.transfer_list.items.item.qty_updated_successfully_label';
  final receiving_division_price_label =
      'transfer_lists.transfer_list.items.item.receiving_division_price_label';
  final remove_alert =
      const _LabelsTransferListsTransferListItemsItemRemoveAlert();
  final removed_successfully_label =
      'transfer_lists.transfer_list.items.item.removed_successfully_label';
  final source_division_label =
      'transfer_lists.transfer_list.items.item.source_division_label';
  final source_item_label =
      'transfer_lists.transfer_list.items.item.source_item_label';
  final source_stock_qty_label =
      'transfer_lists.transfer_list.items.item.source_stock_qty_label';
}

class _LabelsTransferListsTransferListItemsItemErrors {
  const _LabelsTransferListsTransferListItemsItemErrors();

  final inventory_unit_mapping_error =
      'transfer_lists.transfer_list.items.item.errors.inventory_unit_mapping_error';
  final label = 'transfer_lists.transfer_list.items.item.errors.label';
  final mapping_error =
      'transfer_lists.transfer_list.items.item.errors.mapping_error';
}

class _LabelsTransferListsTransferListItemsItemRemoveAlert {
  const _LabelsTransferListsTransferListItemsItemRemoveAlert();

  final content =
      'transfer_lists.transfer_list.items.item.remove_alert.content';
  final no_action =
      'transfer_lists.transfer_list.items.item.remove_alert.no_action';
  final yes_action =
      'transfer_lists.transfer_list.items.item.remove_alert.yes_action';
}

class _LabelsTransferListsTransferListMenu {
  const _LabelsTransferListsTransferListMenu();

  final add_item_label = 'transfer_lists.transfer_list.menu.add_item_label';
  final edit_name_label = 'transfer_lists.transfer_list.menu.edit_name_label';
  final reorder_items_label =
      'transfer_lists.transfer_list.menu.reorder_items_label';
}

class _LabelsTransferListsTransferListRename {
  const _LabelsTransferListsTransferListRename();

  final new_name_label = 'transfer_lists.transfer_list.rename.new_name_label';
  final renamed_successfully =
      'transfer_lists.transfer_list.rename.renamed_successfully';
}

class _LabelsTransferListsTransferListReorderItems {
  const _LabelsTransferListsTransferListReorderItems();

  final available_only_online =
      'transfer_lists.transfer_list.reorder_items.available_only_online';
  final order_saved_successfully =
      'transfer_lists.transfer_list.reorder_items.order_saved_successfully';
  final save_button_label =
      'transfer_lists.transfer_list.reorder_items.save_button_label';
  final title = 'transfer_lists.transfer_list.reorder_items.title';
}

class _LabelsTransferListsTransferListType {
  const _LabelsTransferListsTransferListType();

  final in_house = 'transfer_lists.transfer_list.type.in_house';
  final inter_property = 'transfer_lists.transfer_list.type.inter_property';
}

class _LabelsUser {
  const _LabelsUser();

  final auth_error = const _LabelsUserAuthError();
}

class _LabelsUserAuthError {
  const _LabelsUserAuthError();

  final deactivate = 'user.auth_error.deactivate';
  final dialog_title = 'user.auth_error.dialog_title';
  final password_expired = 'user.auth_error.password_expired';
  final suspended__named = 'user.auth_error.suspended__named';
}

class _LabelsWarehouseLookup {
  const _LabelsWarehouseLookup();

  final action_button_label = 'warehouse_lookup.action_button_label';
  final category = const _LabelsWarehouseLookupCategory();
  final config_booking_reason =
      const _LabelsWarehouseLookupConfigBookingReason();
  final config_booking_type = const _LabelsWarehouseLookupConfigBookingType();
  final config_capex_type = const _LabelsWarehouseLookupConfigCapexType();
  final config_deposit_booking =
      const _LabelsWarehouseLookupConfigDepositBooking();
  final config_deposit_sync = const _LabelsWarehouseLookupConfigDepositSync();
  final config_recipe_prep_type =
      const _LabelsWarehouseLookupConfigRecipePrepType();
  final config_recipe_type = const _LabelsWarehouseLookupConfigRecipeType();
  final config_transfer_type = const _LabelsWarehouseLookupConfigTransferType();
  final cost_center = const _LabelsWarehouseLookupCostCenter();
  final cost_type = const _LabelsWarehouseLookupCostType();
  final currency = const _LabelsWarehouseLookupCurrency();
  final deposit_item = const _LabelsWarehouseLookupDepositItem();
  final inventory_unit = const _LabelsWarehouseLookupInventoryUnit();
  final item = const _LabelsWarehouseLookupItem();
  final local_supplier = const _LabelsWarehouseLookupLocalSupplier();
  final packing_unit = const _LabelsWarehouseLookupPackingUnit();
  final search_available_only_online =
      'warehouse_lookup.search_available_only_online';
  final store = const _LabelsWarehouseLookupStore();
  final vat = const _LabelsWarehouseLookupVat();
}

class _LabelsWarehouseLookupCategory {
  const _LabelsWarehouseLookupCategory();

  final title = 'warehouse_lookup.category.title';
}

class _LabelsWarehouseLookupConfigBookingReason {
  const _LabelsWarehouseLookupConfigBookingReason();

  final title = 'warehouse_lookup.config_booking_reason.title';
}

class _LabelsWarehouseLookupConfigBookingType {
  const _LabelsWarehouseLookupConfigBookingType();

  final title = 'warehouse_lookup.config_booking_type.title';
}

class _LabelsWarehouseLookupConfigCapexType {
  const _LabelsWarehouseLookupConfigCapexType();

  final title = 'warehouse_lookup.config_capex_type.title';
}

class _LabelsWarehouseLookupConfigDepositBooking {
  const _LabelsWarehouseLookupConfigDepositBooking();

  final title = 'warehouse_lookup.config_deposit_booking.title';
}

class _LabelsWarehouseLookupConfigDepositSync {
  const _LabelsWarehouseLookupConfigDepositSync();

  final title = 'warehouse_lookup.config_deposit_sync.title';
}

class _LabelsWarehouseLookupConfigRecipePrepType {
  const _LabelsWarehouseLookupConfigRecipePrepType();

  final title = 'warehouse_lookup.config_recipe_prep_type.title';
}

class _LabelsWarehouseLookupConfigRecipeType {
  const _LabelsWarehouseLookupConfigRecipeType();

  final title = 'warehouse_lookup.config_recipe_type.title';
}

class _LabelsWarehouseLookupConfigTransferType {
  const _LabelsWarehouseLookupConfigTransferType();

  final title = 'warehouse_lookup.config_transfer_type.title';
}

class _LabelsWarehouseLookupCostCenter {
  const _LabelsWarehouseLookupCostCenter();

  final title = 'warehouse_lookup.cost_center.title';
}

class _LabelsWarehouseLookupCostType {
  const _LabelsWarehouseLookupCostType();

  final title = 'warehouse_lookup.cost_type.title';
}

class _LabelsWarehouseLookupCurrency {
  const _LabelsWarehouseLookupCurrency();

  final title = 'warehouse_lookup.currency.title';
}

class _LabelsWarehouseLookupDepositItem {
  const _LabelsWarehouseLookupDepositItem();

  final title = 'warehouse_lookup.deposit_item.title';
}

class _LabelsWarehouseLookupInventoryUnit {
  const _LabelsWarehouseLookupInventoryUnit();

  final title = 'warehouse_lookup.inventory_unit.title';
}

class _LabelsWarehouseLookupItem {
  const _LabelsWarehouseLookupItem();

  final title = 'warehouse_lookup.item.title';
}

class _LabelsWarehouseLookupLocalSupplier {
  const _LabelsWarehouseLookupLocalSupplier();

  final title = 'warehouse_lookup.local_supplier.title';
}

class _LabelsWarehouseLookupPackingUnit {
  const _LabelsWarehouseLookupPackingUnit();

  final title = 'warehouse_lookup.packing_unit.title';
}

class _LabelsWarehouseLookupStore {
  const _LabelsWarehouseLookupStore();

  final title = 'warehouse_lookup.store.title';
}

class _LabelsWarehouseLookupVat {
  const _LabelsWarehouseLookupVat();

  final title = 'warehouse_lookup.vat.title';
}

class _LabelsWebshop {
  const _LabelsWebshop();

  final cost_center_lookup = const _LabelsWebshopCostCenterLookup();
}

class _LabelsWebshopCostCenterLookup {
  const _LabelsWebshopCostCenterLookup();

  final filter = const _LabelsWebshopCostCenterLookupFilter();
  final title = 'webshop.cost_center_lookup.title';
}

class _LabelsWebshopCostCenterLookupFilter {
  const _LabelsWebshopCostCenterLookupFilter();

  final sorting = const _LabelsWebshopCostCenterLookupFilterSorting();
}

class _LabelsWebshopCostCenterLookupFilterSorting {
  const _LabelsWebshopCostCenterLookupFilterSorting();

  final by_cost_center_asc =
      'webshop.cost_center_lookup.filter.sorting.by_cost_center_asc';
  final by_cost_center_desc =
      'webshop.cost_center_lookup.filter.sorting.by_cost_center_desc';
  final by_cost_center_id_asc =
      'webshop.cost_center_lookup.filter.sorting.by_cost_center_id_asc';
  final by_cost_center_id_desc =
      'webshop.cost_center_lookup.filter.sorting.by_cost_center_id_desc';
  final by_division_asc =
      'webshop.cost_center_lookup.filter.sorting.by_division_asc';
  final by_division_desc =
      'webshop.cost_center_lookup.filter.sorting.by_division_desc';
  final by_last_accessed_at_asc =
      'webshop.cost_center_lookup.filter.sorting.by_last_accessed_at_asc';
  final by_last_accessed_at_desc =
      'webshop.cost_center_lookup.filter.sorting.by_last_accessed_at_desc';
}
