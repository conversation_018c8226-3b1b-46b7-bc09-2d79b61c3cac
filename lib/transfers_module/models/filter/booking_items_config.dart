import 'package:app/receivings_module/models/filter/config.dart';
import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';

const BOOKING_PRODUCTS_FREE_TEXT_FID = '1629181796';

class BookingItemsConfig extends FilterConfig {
  const BookingItemsConfig();

  @override
  QueryFilterModel get freeTextFilterConfig => const QueryFilterModel(
        id: BOOKING_PRODUCTS_FREE_TEXT_FID,
        label: 'filter.search',
        eanScannerEnabled: true,
      );

  @override
  ListFilterModel get sortingFilterConfig => throw UnimplementedError();

  @override
  List<FilterModel> get advancedFilterConfig => throw UnimplementedError();

  @override
  Map<String, FilterValueModel> get defaultFilterValues => {
        BOOKING_PRODUCTS_FREE_TEXT_FID:
            const FilterValueModel<String>(value: ''),
      };
}
