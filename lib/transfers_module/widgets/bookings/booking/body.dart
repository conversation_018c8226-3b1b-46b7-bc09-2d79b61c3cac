import 'package:app/i18n/i18n.dart';
import 'package:app/shared/helpers/helpers.dart';
import 'package:app/shared/widgets/widgets.dart';
import 'package:app/transfers_module/cubits/booking_items/cubit.dart';
import 'package:app/transfers_module/screens/bookings/booking_result.dart';
import 'package:app/transfers_module/widgets/bookings/booking/errors_banner.dart';
import 'package:app/transfers_module/widgets/bookings/booking/list.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BookingBody extends StatelessWidget {
  const BookingBody({super.key});

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return BlocListener<BookingItemsCubit, BookingItemsState>(
      listenWhen: (p, c) {
        return p.extension.status != c.extension.status &&
            const {
              BookingItemsStatus.booking_confirm_in_progress,
              BookingItemsStatus.booking_confirm_error,
              BookingItemsStatus.booking_confirm_success,
            }.contains(c.extension.status);
      },
      listener: (context, state) {
        Dialogs.closeOpenDialog(context);

        switch (state.extension.status) {
          case BookingItemsStatus.booking_confirm_in_progress:
            Dialogs.showLoadingDialog(context);
            break;

          case BookingItemsStatus.booking_confirm_error:
            final error = state.error;

            if (error == null) {
              return;
            }

            AppErrorHandler.of(context).handle(
              error: error,
              onRetry: context.read<BookingItemsCubit>().confirmBooking,
            );
            break;

          case BookingItemsStatus.booking_confirm_success:
            final navigator = Navigator.of(context);

            final statuses = state.ext.bookingStatuses;

            navigator.pop();

            Dialogs.showSnackBar(
              tr(Labels.bookings.book.booked_successfully_label),
              context: context,
              action: statuses.isEmpty
                  ? null
                  : SnackBarAction(
                      onPressed: () {
                        navigator.push(
                          BookingResultScreen.route(statuses: statuses),
                        );
                      },
                      label: tr(
                        Labels.bookings.book.view_booking_log_button_label,
                      ),
                    ),
              duration: const Duration(seconds: 8),
            );
            break;

          default:
            break;
        }
      },
      child: ScaffoldMessenger(
        child: Scaffold(
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              BlocBuilder<BookingItemsCubit, BookingItemsState>(
                buildWhen: (p, c) =>
                    !const DeepCollectionEquality.unordered().equals(
                  p.ext.booking?.errors ?? {},
                  c.ext.booking?.errors ?? {},
                ),
                builder: (context, state) {
                  final errors = state.ext.booking?.errors;

                  if (errors == null || errors.isEmpty) {
                    return const SizedBox.shrink();
                  }

                  return BookingErrorsBanner(errors: errors);
                },
              ),
              const Expanded(
                child: WideScreenWrapper(
                  child: Column(
                    children: <Widget>[
                      Expanded(
                        child: BookingItemsList(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
