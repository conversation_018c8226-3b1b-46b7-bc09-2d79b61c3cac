import 'package:app/approvals/cubits/filter/cubits.dart';
import 'package:app/approvals/models/filter/capex_approval_requests_config.dart';
import 'package:app/approvals/widgets/capex_approval_requests/capex_approval_requests/active_tab.dart';
import 'package:app/approvals/widgets/capex_approval_requests/capex_approval_requests/app_bar.dart';
import 'package:app/approvals/widgets/capex_approval_requests/capex_approval_requests/authorized_tab.dart';
import 'package:app/approvals/widgets/capex_approval_requests/capex_approval_requests/closed_tab.dart';
import 'package:app/i18n/i18n.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/helpers/permission_checker.dart';
import 'package:app/shared/models/user_auth.dart';
import 'package:app/shared/repositories/capex.dart';
import 'package:app/shared/screens/app_menu.dart';
import 'package:app/shared/screens/entity_lookup.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/widgets/status/connection_status.dart';
import 'package:app/shared/widgets/wrapper/v3_theme_wrapper.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum CapexApprovalRequestsScreenInitialView {
  active(0),
  activeAssignedToMe(0),
  forUniversalApprover(0),
  authorized(1),
  closed(2);

  final int tabIndex;

  const CapexApprovalRequestsScreenInitialView(this.tabIndex);
}

class CapexApprovalRequestsScreen extends StatelessWidget {
  final CapexApprovalRequestsScreenInitialView initialView;

  const CapexApprovalRequestsScreen({
    Key? key,
    this.initialView = CapexApprovalRequestsScreenInitialView.active,
  }) : super(key: key);

  static MaterialPageRoute<void> route({
    CapexApprovalRequestsScreenInitialView initialView =
        CapexApprovalRequestsScreenInitialView.active,
  }) {
    return MaterialPageRoute<void>(
      settings: const RouteSettings(
        name: '/capex_approval_requests',
      ),
      builder: (_) => CapexApprovalRequestsScreen(
        initialView: initialView,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final division = context.read<PreferencesCacheService>().getField(
          PreferencesField.division,
        );

    final bool isForUniversalApprover =
        context.read<PermissionChecker>().checkPermission(
              PermissionType.CAPEX_UNIVERSAL_APPROVER_ONLY,
            );

    return MultiBlocProvider(
      providers: [
        BlocProvider<ActiveCapexApprovalRequestsFilterCubit>(
          create: (context) => ActiveCapexApprovalRequestsFilterCubit(
            ActiveCapexApprovalRequestFilterConfig(
              isForUniversalApprover,
              valueInputDelegates: {
                CAPEX_APPROVAL_REQUESTS_FILTER_BY_DIVISION_FID: (_) =>
                    _divisionFilterLookup(context),
              },
              isForUniversalApprover: isForUniversalApprover,
              assignedToMeByDefault: initialView ==
                      CapexApprovalRequestsScreenInitialView
                          .activeAssignedToMe ||
                  isForUniversalApprover,
            ),
          ),
        ),
        BlocProvider<AuthorizedCapexApprovalRequestsFilterCubit>(
          create: (_) => AuthorizedCapexApprovalRequestsFilterCubit(
            AuthorizedCapexApprovalRequestFilterConfig(
              division.isCrossPropertyCapexApprovalEnabled,
              valueInputDelegates: {
                CAPEX_APPROVAL_REQUESTS_FILTER_BY_DIVISION_FID: (_) =>
                    _divisionFilterLookup(context),
              },
            ),
          ),
        ),
        BlocProvider<ClosedCapexApprovalRequestsFilterCubit>(
          create: (_) => ClosedCapexApprovalRequestsFilterCubit(
            ClosedCapexApprovalRequestFilterConfig(
              division.isCrossPropertyCapexApprovalEnabled,
              valueInputDelegates: {
                CAPEX_APPROVAL_REQUESTS_FILTER_BY_DIVISION_FID: (_) =>
                    _divisionFilterLookup(context),
              },
            ),
          ),
        ),
      ],
      child: CapexApprovalRequestsScreenView(
        isCrossPropertyCapexEnabled:
            division.isCrossPropertyCapexApprovalEnabled,
        initialView: isForUniversalApprover
            ? CapexApprovalRequestsScreenInitialView.forUniversalApprover
            : initialView,
      ),
    );
  }

  Future<String?> _divisionFilterLookup(BuildContext context) async {
    final result = await Navigator.of(context).push(
      EntityLookupScreen.route(
        title:
            tr(context, Labels.approvals.capex_requests.filter.division_label),
        loadEntitiesDelegate: (pageSize, pageIndex, filterValues) =>
            context.read<CapexRepository>().approvalDivisionFilterLookup(
                  filterValues: filterValues,
                  pageSize: pageSize,
                  page: pageIndex,
                ),
      ),
    );

    if (result == null) {
      return null;
    }

    return result.id;
  }
}

class CapexApprovalRequestsScreenView extends StatefulWidget {
  const CapexApprovalRequestsScreenView({
    Key? key,
    required this.isCrossPropertyCapexEnabled,
    required this.initialView,
  }) : super(key: key);

  final bool isCrossPropertyCapexEnabled;

  final CapexApprovalRequestsScreenInitialView initialView;

  @override
  State<CapexApprovalRequestsScreenView> createState() =>
      _CapexApprovalRequestsScreenViewState();
}

class _CapexApprovalRequestsScreenViewState
    extends State<CapexApprovalRequestsScreenView>
    with TickerProviderStateMixin {
  late TabController _controller;

  int tabIndex = 0;

  @override
  void initState() {
    super.initState();

    _controller = TabController(
      initialIndex: widget.initialView.tabIndex,
      length: widget.initialView ==
              CapexApprovalRequestsScreenInitialView.forUniversalApprover
          ? 1
          : 3,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tr = context.translator;

    return V3ThemeWrapper(
      child: Scaffold(
        drawer: const AppMenu(),
        bottomNavigationBar: const ConnectionStatus(),
        appBar: CapexApprovalRequestsAppBar(
          tabController: _controller,
        ),
        body: Column(
          children: [
            FLTabBar(
              controller: _controller,
              tabs: [
                FLTab(
                  label: tr(Labels.approvals.capex.active_tab),
                ),
                if (widget.initialView !=
                    CapexApprovalRequestsScreenInitialView
                        .forUniversalApprover) ...[
                  FLTab(
                    label: tr(Labels.approvals.capex.authorized_tab),
                  ),
                  FLTab(
                    label: tr(Labels.approvals.capex.closed_tab),
                  ),
                ],
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _controller,
                children: [
                  CapexApprovalRequestsActiveTab(
                    isCrossPropertyCapexEnabled:
                        widget.isCrossPropertyCapexEnabled,
                  ),
                  if (widget.initialView !=
                      CapexApprovalRequestsScreenInitialView
                          .forUniversalApprover) ...[
                    CapexApprovalRequestsAuthorizedTab(
                      isCrossPropertyCapexEnabled:
                          widget.isCrossPropertyCapexEnabled,
                    ),
                    CapexApprovalRequestsClosedTab(
                      isCrossPropertyCapexEnabled:
                          widget.isCrossPropertyCapexEnabled,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
