import 'package:app/i18n/labels_config.dart';
import 'package:app/receivings_module/models/filter/config.dart';
import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:flutter/material.dart';

const PURCHASE_REQUESTS_FREE_TEXT_FID = '1629099014';

const PURCHASE_REQUESTS_SORTING_FID = '1629099015';
const PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_ASCENDING = '1629099016';
const PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_DESCENDING = '1629099017';
const PURCHASE_REQUESTS_SORT_BY_REQUESTED_AT_ASCENDING = '1629099018';
const PURCHASE_REQUESTS_SORT_BY_REQUESTED_AT_DESCENDING = '1629099019';

const PURCHASE_REQUESTS_FILTER_STATUS_FID = '1629099020';
const PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME = '1629099021';
const PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_MY_GROUP = '1707396488';
const PURCHASE_REQUESTS_FILTER_STATUS_OPEN = '1629099022';
const PURCHASE_REQUESTS_FILTER_STATUS_APPROVED = '1629099023';
const PURCHASE_REQUESTS_FILTER_STATUS_CLOSED = '1629099024';
const PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_APPROVED = '1686121778';
const PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_DECLINED = '1686121779';

const PURCHASE_REQUESTS_DATE_RANGE_FID = '1629099025';

const PURCHASE_REQUESTS_FILTER_SUPPLIER_FID = '1690300726';

const PURCHASE_REQUESTS_FILTER_COST_CENTER_FID = '1686121775';

const PURCHASE_REQUESTS_FILTER_CATEGORY_FID = '1686121776';

const PURCHASE_REQUESTS_FILTER_DESCRIPTION_FID = '1686121777';

const PURCHASE_REQUESTS_FILTER_REQUESTOR_FID = '1752134272';

class PurchaseRequestFilterConfig extends FilterConfig {
  late final List<FilterModel> _advancedFilterConfig;

  PurchaseRequestFilterConfig({
    required super.valueInputDelegates,
  }) : assert(
          valueInputDelegates
              .containsKey(PURCHASE_REQUESTS_FILTER_REQUESTOR_FID),
        ) {
    _advancedFilterConfig = [
      FilterModel.list(
        id: PURCHASE_REQUESTS_FILTER_STATUS_FID,
        label: Labels.approvals.purchase_requests.filter.status_label,
        values: [
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME,
            label:
                Labels.approvals.purchase_requests.filter.status.assigned_to_me,
          ),
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_MY_GROUP,
            label: Labels
                .approvals.purchase_requests.filter.status.assigned_to_my_group,
          ),
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_OPEN,
            label: Labels.approvals.purchase_requests.filter.status.open,
          ),
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_APPROVED,
            label: Labels.approvals.purchase_requests.filter.status.approved,
          ),
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_CLOSED,
            label: Labels.approvals.purchase_requests.filter.status.closed,
          ),
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_APPROVED,
            label: Labels
                .approvals.purchase_requests.filter.status.closed_approved,
          ),
          FilterValueModel<String>(
            value: PURCHASE_REQUESTS_FILTER_STATUS_CLOSED_DECLINED,
            label: Labels
                .approvals.purchase_requests.filter.status.closed_declined,
          ),
        ],
      ),
      FilterModel.dateRange(
        id: PURCHASE_REQUESTS_DATE_RANGE_FID,
        label: Labels.approvals.purchase_requests.filter.status.date_range,
      ),
      FilterModel.text(
        id: PURCHASE_REQUESTS_FILTER_COST_CENTER_FID,
        label: Labels.approvals.purchase_requests.filter.cost_center.label,
        hintText:
            Labels.approvals.purchase_requests.filter.cost_center.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: PURCHASE_REQUESTS_FILTER_CATEGORY_FID,
        label: Labels.approvals.purchase_requests.filter.category.label,
        hintText: Labels.approvals.purchase_requests.filter.category.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: PURCHASE_REQUESTS_FILTER_SUPPLIER_FID,
        label: Labels.approvals.purchase_requests.filter.supplier.label,
        hintText: Labels.approvals.purchase_requests.filter.supplier.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: PURCHASE_REQUESTS_FILTER_DESCRIPTION_FID,
        label: Labels.approvals.purchase_requests.filter.description.label,
        hintText:
            Labels.approvals.purchase_requests.filter.description.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: PURCHASE_REQUESTS_FILTER_REQUESTOR_FID,
        label: Labels.approvals.purchase_requests.filter.requestor.label,
        hintText: Labels.approvals.purchase_requests.filter.requestor.hint_text,
        maxLength: 255,
        valueInputDelegate:
            valueInputDelegates[PURCHASE_REQUESTS_FILTER_REQUESTOR_FID],
      ),
    ];
  }

  static final _freeTextFilterConfig = QueryFilterModel(
    id: PURCHASE_REQUESTS_FREE_TEXT_FID,
    label: Labels.filter.search,
    eanScannerEnabled: false,
  );

  static final _sortingFilterConfig = ListFilterModel(
    id: PURCHASE_REQUESTS_SORTING_FID,
    label: Labels.filter.sorting,
    values: [
      FilterValueModel<String>(
        value: PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_ASCENDING,
        label: Labels.approvals.purchase_requests.sort.by_added_to_cart_at_asc,
      ),
      FilterValueModel<String>(
        value: PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_DESCENDING,
        label: Labels.approvals.purchase_requests.sort.by_added_to_cart_at_desc,
      ),
      FilterValueModel<String>(
        value: PURCHASE_REQUESTS_SORT_BY_REQUESTED_AT_ASCENDING,
        label: Labels.approvals.purchase_requests.sort.by_requested_at_asc,
      ),
      FilterValueModel<String>(
        value: PURCHASE_REQUESTS_SORT_BY_REQUESTED_AT_DESCENDING,
        label: Labels.approvals.purchase_requests.sort.by_requested_at_desc,
      ),
    ],
  );

  @override
  QueryFilterModel get freeTextFilterConfig => _freeTextFilterConfig;

  @override
  ListFilterModel get sortingFilterConfig => _sortingFilterConfig;

  @override
  List<FilterModel> get advancedFilterConfig => _advancedFilterConfig;

  @override
  Map<String, FilterValueModel> get defaultFilterValues => {
        PURCHASE_REQUESTS_FREE_TEXT_FID:
            const FilterValueModel<String>(value: ''),
        PURCHASE_REQUESTS_SORTING_FID: FilterValueModel<String>(
          value: PURCHASE_REQUESTS_SORT_BY_ADDED_TO_CART_AT_DESCENDING,
          label:
              Labels.approvals.purchase_requests.sort.by_added_to_cart_at_desc,
        ),
        PURCHASE_REQUESTS_FILTER_STATUS_FID: FilterValueModel<String>(
          value: PURCHASE_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME,
          label:
              Labels.approvals.purchase_requests.filter.status.assigned_to_me,
        ),
        PURCHASE_REQUESTS_DATE_RANGE_FID:
            const FilterValueModel<DateTimeRange?>(
          value: null,
        ),
        PURCHASE_REQUESTS_FILTER_COST_CENTER_FID:
            const FilterValueModel<String>(value: ''),
        PURCHASE_REQUESTS_FILTER_CATEGORY_FID:
            const FilterValueModel<String>(value: ''),
        PURCHASE_REQUESTS_FILTER_SUPPLIER_FID:
            const FilterValueModel<String>(value: ''),
        PURCHASE_REQUESTS_FILTER_DESCRIPTION_FID:
            const FilterValueModel<String>(value: ''),
        PURCHASE_REQUESTS_FILTER_REQUESTOR_FID:
            const FilterValueModel<String>(value: ''),
      };
}
