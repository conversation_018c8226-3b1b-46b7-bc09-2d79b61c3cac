import 'package:app/approvals/cubits/capex_approval_requests/cubit.dart';
import 'package:app/approvals/cubits/filter/cubits.dart';
import 'package:app/approvals/models/filter/capex_approval_requests_config.dart';
import 'package:app/approvals/widgets/capex_approval_requests/capex_approval_requests/list.dart';
import 'package:app/i18n/labels_config.dart';
import 'package:app/i18n/localization_state.dart';
import 'package:app/shared/cubits/api_connectivity/cubit.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/helpers/permission_checker.dart';
import 'package:app/shared/models/user_auth.dart';
import 'package:app/shared/repositories/capex.dart';
import 'package:app/shared/screens/entity_lookup.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/widgets/filters/buttons.dart';
import 'package:app/shared/widgets/wide_screen_wrapper.dart';
import 'package:app/shared/widgets/wrapper/api_connectivity_listener.dart';
import 'package:app/shared/widgets/wrapper/filter_listener.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CapexApprovalRequestsActiveTab extends StatelessWidget {
  final bool isCrossPropertyCapexEnabled;

  const CapexApprovalRequestsActiveTab({
    super.key,
    required this.isCrossPropertyCapexEnabled,
  });

  @override
  Widget build(BuildContext context) {
    final user =
        context.read<PreferencesCacheService>().getField(PreferencesField.user);

    return BlocProvider<CapexApprovalRequestsCubit>(
      create: (context) => CapexApprovalRequestsCubit(
        context.read<CapexRepository>(),
        defaultFilterValues:
            context.read<ActiveCapexApprovalRequestsFilterCubit>().state.values,
        isOnline: context.read<ApiConnectivityCubit>().state,
        language: context.read<LocalizationState>().locale.toString(),
        userId: user.userId,
        changeCapexPermission:
            context.read<PermissionChecker>().checkPermission(
                  PermissionType.CAN_CHANGE_CAPEX,
                ),
        crossPropertyEnabled: isCrossPropertyCapexEnabled,
      )..load(),
      child: MultiBlocListener(
        listeners: [
          FilterListener<ActiveCapexApprovalRequestsFilterCubit>(
            onFilterValuesUpdated: (context, state) async {
              if (isCrossPropertyCapexEnabled &&
                  state.values[CAPEX_APPROVAL_REQUESTS_FILTER_STATUS_FID]
                          ?.value ==
                      CAPEX_APPROVAL_REQUESTS_FILTER_STATUS_ASSIGNED_TO_ME) {
                context
                    .read<ActiveCapexApprovalRequestsFilterCubit>()
                    .updateConfig(
                      (config) =>
                          config = ActiveCapexApprovalRequestFilterConfig(
                        true,
                        valueInputDelegates: {
                          CAPEX_APPROVAL_REQUESTS_FILTER_BY_DIVISION_FID: (_) =>
                              _divisionFilterLookup(context),
                        },
                        isForUniversalApprover:
                            (config as ActiveCapexApprovalRequestFilterConfig)
                                .isForUniversalApprover,
                        assignedToMeByDefault: true,
                      ),
                    );
              } else {
                final filterCubit =
                    context.read<ActiveCapexApprovalRequestsFilterCubit>();

                filterCubit.clear(
                  id: CAPEX_APPROVAL_REQUESTS_FILTER_BY_DIVISION_FID,
                );

                filterCubit.updateConfig(
                  (config) => config = ActiveCapexApprovalRequestFilterConfig(
                    false,
                    valueInputDelegates: {
                      CAPEX_APPROVAL_REQUESTS_FILTER_BY_DIVISION_FID: (_) =>
                          _divisionFilterLookup(context),
                    },
                    isForUniversalApprover:
                        (config as ActiveCapexApprovalRequestFilterConfig)
                            .isForUniversalApprover,
                    assignedToMeByDefault: true,
                  ),
                );
              }

              context
                  .read<CapexApprovalRequestsCubit>()
                  .updateFilterValues(state.values);
            },
          ),
          ApiConnectivityListener(
            onConnectionStateChanged: (context, state) =>
                context.read<CapexApprovalRequestsCubit>().updateNetworkState(
                      state,
                    ),
          ),
        ],
        child: const WideScreenWrapper(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              FilterButtons<ActiveCapexApprovalRequestsFilterCubit>(
                sortingAvailable: true,
                advancedFilteringAvailable: true,
              ),
              Expanded(
                child: CapexApprovalRequestsList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<String?> _divisionFilterLookup(BuildContext context) async {
    final result = await Navigator.of(context).push(
      EntityLookupScreen.route(
        title:
            tr(context, Labels.approvals.capex_requests.filter.division_label),
        loadEntitiesDelegate: (pageSize, pageIndex, filterValues) =>
            context.read<CapexRepository>().approvalDivisionFilterLookup(
                  filterValues: filterValues,
                  pageSize: pageSize,
                  page: pageIndex,
                ),
      ),
    );

    if (result == null) {
      return null;
    }

    return result.id;
  }
}
